// lib/features/faceDetection/presentation/widgets/app_bar.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../../l10n/app_localizations.dart';
import '../cubit/face_checkin_cubit.dart';
import '../cubit/face_checkin_state.dart';

class FaceDetectionAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  const FaceDetectionAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final l10n = AppLocalizations.of(context)!;

    return BlocConsumer<FaceDetectionCubit, FaceDetectionState>(
      listener: (context, state) {
        if (state.status.isEmpty) {
          return;
        }
        final detectedFacesPrefix = l10n
            .detectFaceFacesDetected(0)
            .split('0')[0]; // L<PERSON>y phần "Đã phát hiện "

        final List<String> intermediateOrUIActionsStatuses = [
          l10n.detectFaceReadyForCheckIn,
          l10n.detectFaceReadyForCheckOut,
          l10n.detectFaceAttendanceCompleted,
          l10n.detectFaceOpeningCamera,
          l10n.detectFaceImageCapturedReady,
          l10n.detectFaceProcessingCheckIn,
          l10n.detectFaceProcessingCheckOut,
          l10n.detectFaceAnalyzingSecurity,
          l10n.detectFaceFaceNotFound,
        ];

        if (intermediateOrUIActionsStatuses.contains(state.status) ||
            state.status.startsWith(detectedFacesPrefix)) {
          // <<<--- THAY ĐỔI Ở ĐÂY
          return;
        }

        final checkInErrorPrefix = l10n
            .detectFaceCheckInError('DUMMY_ERROR_MESSAGE')
            .split('DUMMY_ERROR_MESSAGE')[0];
        final checkOutErrorPrefix = l10n
            .detectFaceCheckOutError('DUMMY_ERROR_MESSAGE')
            .split('DUMMY_ERROR_MESSAGE')[0];

        if (state.status.startsWith(checkInErrorPrefix) ||
            state.status.startsWith(checkOutErrorPrefix) ||
            state.status == l10n.detectFaceCheckInSuccess ||
            state.status == l10n.detectFaceCheckOutSuccess) {
          // Những trạng thái này cũng thường được ActionButtons xử lý,
          // hoặc chúng ta không muốn AppBar hiển thị SnackBar chồng chéo.
          return;
        }
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(state.status),
            backgroundColor:
                state.status.toLowerCase().contains('successful') &&
                    !state.status.toLowerCase().contains('error') &&
                    !state.status.toLowerCase().contains('fail') &&
                    !state.status.toLowerCase().contains('thất bại') &&
                    !state.status.toLowerCase().contains('không')
                ? Colors.green.shade600
                : Colors.red.shade600,
            duration: const Duration(seconds: 3),
          ),
        );
      },
      builder: (context, state) {
        // ... builder không đổi ...
        return AppBar(
          elevation: 0,
          backgroundColor: Colors.transparent,
          foregroundColor: Colors.black87,
          title: Text(
            l10n.detectFaceSecureCheckInTitle,
            style: TextStyle(
              fontSize: responsive.fontSize(22),
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          centerTitle: true,
          actions: [
            IconButton(
              icon:
                  state.isLoading &&
                      state.locationStatus == l10n.detectFaceGettingLocation
                  ? SizedBox(
                      width: responsive.fontSize(20),
                      height: responsive.fontSize(20),
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.blue.shade600,
                      ),
                    )
                  : Icon(Icons.refresh, size: responsive.fontSize(24)),
              onPressed: state.isLoading
                  ? null
                  : () =>
                        context.read<FaceDetectionCubit>().getCurrentLocation(),
              tooltip: l10n.detectFaceUpdateLocationTooltip,
            ),
            IconButton(
              icon: Icon(Icons.security, size: responsive.fontSize(24)),
              onPressed: () => _showSecurityInfoDialog(context),
              tooltip: l10n.detectFaceSecurityInfoTooltip,
            ),
          ],
        );
      },
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  // ... _showSecurityInfoDialog và các widget con không đổi ...
  void _showSecurityInfoDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) {
        final responsive = Responsive.of(dialogContext);
        final l10nDialog = AppLocalizations.of(dialogContext)!;
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(responsive.fontSize(20)),
          ),
          title: _SecurityDialogTitle(responsive: responsive, l10n: l10nDialog),
          content: _SecurityDialogContent(
            responsive: responsive,
            l10n: l10nDialog,
          ),
          actionsPadding: responsive.padding(horizontal: 16, vertical: 8),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(dialogContext),
              child: Text(
                l10nDialog.detectFaceUnderstood,
                style: TextStyle(
                  color: Colors.blue.shade700,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

class _SecurityDialogTitle extends StatelessWidget {
  final Responsive responsive;
  final AppLocalizations l10n;

  const _SecurityDialogTitle({required this.responsive, required this.l10n});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.admin_panel_settings_outlined,
          color: Colors.blue.shade600,
          size: responsive.fontSize(24),
        ),
        SizedBox(width: responsive.scaleWidth(10)),
        Text(
          l10n.detectFaceSecurityInfoTitle,
          style: TextStyle(
            fontSize: responsive.fontSize(18),
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}

class _SecurityDialogContent extends StatelessWidget {
  final Responsive responsive;
  final AppLocalizations l10n;

  const _SecurityDialogContent({required this.responsive, required this.l10n});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _SecurityInfoItem(
            title: l10n.detectFaceFaceAnalysis,
            description: l10n.detectFaceFaceAnalysisDescription,
            icon: Icons.face_retouching_natural,
            responsive: responsive,
          ),
          Divider(height: responsive.scaleHeight(20)),
          _SecurityInfoItem(
            title: l10n.detectFaceLocationVerification,
            description: l10n.detectFaceLocationVerificationDescription,
            icon: Icons.my_location,
            responsive: responsive,
          ),
          SizedBox(height: responsive.scaleHeight(20)),
          Container(
            padding: responsive.padding(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.amber.withAlpha(38),
              borderRadius: BorderRadius.circular(responsive.fontSize(8)),
              border: Border.all(color: Colors.amber.shade600.withAlpha(128)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.amber.shade700,
                  size: responsive.fontSize(20),
                ),
                SizedBox(width: responsive.scaleWidth(10)),
                Expanded(
                  child: Text(
                    l10n.detectFaceSecurityInfoNotice,
                    style: TextStyle(
                      fontSize: responsive.fontSize(12),
                      color: Colors.amber.shade800,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _SecurityInfoItem extends StatelessWidget {
  final String title;
  final String description;
  final IconData icon;
  final Responsive responsive;

  const _SecurityInfoItem({
    required this.title,
    required this.description,
    required this.icon,
    required this.responsive,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: responsive.padding(all: 7),
          decoration: BoxDecoration(
            color: Colors.blue.withAlpha(26),
            borderRadius: BorderRadius.circular(responsive.fontSize(8)),
          ),
          child: Icon(
            icon,
            size: responsive.fontSize(20),
            color: Colors.blue.shade700,
          ),
        ),
        SizedBox(width: responsive.scaleWidth(12)),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: responsive.fontSize(14),
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: responsive.scaleHeight(2)),
              Text(
                description,
                style: TextStyle(
                  fontSize: responsive.fontSize(12),
                  color: Colors.grey.shade700,
                  height: 1.3,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
