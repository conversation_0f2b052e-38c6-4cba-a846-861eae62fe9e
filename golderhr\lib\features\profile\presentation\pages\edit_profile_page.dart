import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/features/profile/presentation/cubit/profile_cubit.dart';
import 'package:golderhr/features/profile/presentation/cubit/profile_state.dart';

import 'package:iconsax/iconsax.dart';
import 'package:go_router/go_router.dart';

class EditProfilePage extends StatefulWidget {
  const EditProfilePage({super.key});

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<EditProfilePage> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;

  @override
  void initState() {
    super.initState();
    final userProfile = context.read<ProfileCubit>().state.userProfile;
    _nameController = TextEditingController(text: userProfile.name);
    _emailController = TextEditingController(text: userProfile.email);
    _phoneController = TextEditingController(text: userProfile.phone);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  // Helper method to get avatar image (local file or network image)
  ImageProvider? _getAvatarImage(ProfileState state) {
    if (state.newImageFile != null) {
      // Show selected local image
      return FileImage(File(state.newImageFile!));
    } else if (state.userProfile.avatarUrl.isNotEmpty) {
      // Show current network image
      return NetworkImage(state.userProfile.avatarUrl);
    }
    return null; // No image available
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final responsive = context.responsive;

    return Scaffold(
      appBar: AppBar(
        title: Text(context.l10n.editProfile),
        centerTitle: true,
        actions: [
          BlocConsumer<ProfileCubit, ProfileState>(
            listener: (context, state) {
              if (state.status == ProfileStatus.loaded) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Profile updated successfully'),
                    backgroundColor: theme.colorScheme.primary,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
                // Pop back to profile page - it will automatically refresh
                context.pop(true); // Pass true to indicate successful update
              } else if (state.status == ProfileStatus.error) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(state.errorMessage ?? 'Update failed'),
                    backgroundColor: theme.colorScheme.error,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              }
            },
            builder: (context, state) {
              return TextButton(
                onPressed: state.status == ProfileStatus.loading
                    ? null
                    : _saveProfile,
                child: state.status == ProfileStatus.loading
                    ? SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            theme.colorScheme.primary,
                          ),
                        ),
                      )
                    : Text(
                        context.l10n.save,
                        style: TextStyle(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: responsive.padding(all: 16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile Header
              _buildProfileHeader(theme, responsive),
              const SizedBox(height: 32),

              // Form Fields
              _buildFormFields(theme, responsive),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader(ThemeData theme, dynamic responsive) {
    return BlocBuilder<ProfileCubit, ProfileState>(
      builder: (context, state) {
        return Card(
          elevation: 1,
          shadowColor: Colors.black.withValues(alpha: 0.05),
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(color: Colors.grey.shade300, width: 1.5),
          ),
          child: Padding(
            padding: responsive.padding(all: 20.0),
            child: Row(
              children: [
                Stack(
                  children: [
                    CircleAvatar(
                      radius: 40,
                      backgroundColor: theme.colorScheme.primary.withValues(
                        alpha: 0.1,
                      ),
                      backgroundImage: _getAvatarImage(state),
                      child: _getAvatarImage(state) == null
                          ? Icon(
                              Iconsax.user,
                              size: 40,
                              color: theme.colorScheme.primary,
                            )
                          : null,
                    ),
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: GestureDetector(
                        onTap: () {
                          context.read<ProfileCubit>().pickImageForEdit();
                        },
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: theme.colorScheme.surface,
                              width: 2,
                            ),
                          ),
                          child: Icon(
                            Iconsax.camera,
                            size: 16,
                            color: theme.colorScheme.onPrimary,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        state.userProfile.name.isNotEmpty
                            ? state.userProfile.name
                            : 'No Name',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        state.userProfile.email,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(
                            alpha: 0.7,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildFormFields(ThemeData theme, dynamic responsive) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Personal Information',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
        const SizedBox(height: 16),

        // Full Name Field
        _buildTextField(
          controller: _nameController,
          label: context.l10n.fullName,
          icon: Iconsax.user,
          validator: (value) {
            if (value != null && value.isNotEmpty && value.trim().length < 2) {
              return 'Name must be at least 2 characters';
            }
            return null;
          },
          theme: theme,
        ),
        const SizedBox(height: 16),

        // Email Field
        _buildTextField(
          controller: _emailController,
          label: context.l10n.loginEmail,
          icon: Iconsax.sms,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter email address';
            }
            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
              return 'Please enter a valid email address';
            }
            return null;
          },
          keyboardType: TextInputType.emailAddress,
          theme: theme,
          readOnly: false,
        ),
        const SizedBox(height: 16),

        // Phone Field
        _buildTextField(
          controller: _phoneController,
          label: context.l10n.profilePhone,
          icon: Iconsax.call,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter phone number';
            }
            if (!RegExp(r'^\+?\d{10,}$').hasMatch(value)) {
              return 'Please enter a valid phone number';
            }
            return null;
          },
          keyboardType: TextInputType.phone,
          theme: theme,
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    required ThemeData theme,
    bool readOnly = false,
  }) {
    return TextFormField(
      controller: controller,
      validator: validator,
      keyboardType: keyboardType,
      readOnly: readOnly,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: theme.colorScheme.primary),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.error),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: theme.colorScheme.error, width: 2),
        ),
        filled: true,
        fillColor: readOnly
            ? theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5)
            : theme.colorScheme.surface,
      ),
    );
  }

  void _saveProfile() {
    if (_formKey.currentState!.validate()) {
      final currentProfile = context.read<ProfileCubit>().state.userProfile;
      final newName = _nameController.text.trim();
      final newEmail = _emailController.text.trim();
      final newPhone = _phoneController.text.trim();

      // Check if anything actually changed
      bool hasChanges = false;
      Map<String, dynamic> updateData = {};

      if (newName.isNotEmpty && newName != currentProfile.name) {
        updateData['name'] = newName;
        hasChanges = true;
      }

      if (newEmail.isNotEmpty && newEmail != currentProfile.email) {
        updateData['email'] = newEmail;
        hasChanges = true;
      }

      if (newPhone.isNotEmpty && newPhone != currentProfile.phone) {
        updateData['phone'] = newPhone;
        hasChanges = true;
      }

      if (!hasChanges) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No changes to save'),
            behavior: SnackBarBehavior.floating,
          ),
        );
        return;
      }

      // Update profile with current values (unchanged fields keep current values)
      context.read<ProfileCubit>().updateProfile(
        name: newName.isNotEmpty ? newName : currentProfile.name,
        email: newEmail.isNotEmpty ? newEmail : currentProfile.email,
        phone: newPhone.isNotEmpty ? newPhone : currentProfile.phone,
      );
    }
  }
}
