import '../../domain/entities/overtime_request_entity.dart';

class OvertimeRequestModel extends OvertimeRequestEntity {
  const OvertimeRequestModel({
    required super.id,
    required super.employeeId,
    required super.employeeName,
    required super.date,
    required super.startTime,
    required super.endTime,
    required super.hours,
    required super.reason,
    required super.type,
    required super.status,
    super.assignedApproverId,
    super.approvedBy,
    super.approvedAt,
    super.rejectionReason,
    required super.createdAt,
    required super.updatedAt,
  });

  factory OvertimeRequestModel.fromJson(Map<String, dynamic> json) {
    return OvertimeRequestModel(
      id: json['_id'] ?? json['id'] ?? '',
      employeeId: json['employeeId'] is Map
          ? json['employeeId']['_id'] ?? ''
          : json['employeeId'] ?? '',
      employeeName: json['employeeName'] ?? '',
      date: DateTime.parse(json['date']),
      startTime: DateTime.parse(json['startTime']),
      endTime: DateTime.parse(json['endTime']),
      hours: (json['hours'] ?? 0.0).toDouble(),
      reason: json['reason'] ?? '',
      type: _parseOvertimeType(json['type']),
      status: _parseOvertimeStatus(json['status']),
      assignedApproverId: json['assignedApproverId'] is Map
          ? json['assignedApproverId']['name'] ??
                json['assignedApproverId']['fullname'] ??
                json['assignedApproverId']['_id']
          : json['assignedApproverId'],
      approvedBy: json['approvedBy'] is Map
          ? json['approvedBy']['name'] ??
                json['approvedBy']['fullname'] ??
                json['approvedBy']['_id']
          : json['approvedBy'],
      approvedAt: json['approvedAt'] != null
          ? DateTime.parse(json['approvedAt'])
          : null,
      rejectionReason: json['rejectionReason'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'employeeId': employeeId,
      'employeeName': employeeName,
      'date': date.toIso8601String(),
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'hours': hours,
      'reason': reason,
      'type': _overtimeTypeToString(type),
      'status': _overtimeStatusToString(status),
      'assignedApproverId': assignedApproverId,
      'approvedBy': approvedBy,
      'approvedAt': approvedAt?.toIso8601String(),
      'rejectionReason': rejectionReason,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  static OvertimeType _parseOvertimeType(String? type) {
    switch (type?.toLowerCase()) {
      case 'holiday':
        return OvertimeType.holiday;
      case 'weekend':
        return OvertimeType.weekend;
      default:
        return OvertimeType.regular;
    }
  }

  static OvertimeStatus _parseOvertimeStatus(String? status) {
    switch (status?.toLowerCase()) {
      case 'approved':
        return OvertimeStatus.approved;
      case 'rejected':
        return OvertimeStatus.rejected;
      default:
        return OvertimeStatus.pending;
    }
  }

  static String _overtimeTypeToString(OvertimeType type) {
    switch (type) {
      case OvertimeType.holiday:
        return 'holiday';
      case OvertimeType.weekend:
        return 'weekend';
      case OvertimeType.regular:
        return 'regular';
    }
  }

  static String _overtimeStatusToString(OvertimeStatus status) {
    switch (status) {
      case OvertimeStatus.approved:
        return 'approved';
      case OvertimeStatus.rejected:
        return 'rejected';
      case OvertimeStatus.pending:
        return 'pending';
    }
  }

  @override
  OvertimeRequestModel copyWith({
    String? id,
    String? employeeId,
    String? employeeName,
    DateTime? date,
    DateTime? startTime,
    DateTime? endTime,
    double? hours,
    String? reason,
    OvertimeType? type,
    OvertimeStatus? status,
    String? assignedApproverId,
    String? approvedBy,
    DateTime? approvedAt,
    String? rejectionReason,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return OvertimeRequestModel(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      employeeName: employeeName ?? this.employeeName,
      date: date ?? this.date,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      hours: hours ?? this.hours,
      reason: reason ?? this.reason,
      type: type ?? this.type,
      status: status ?? this.status,
      assignedApproverId: assignedApproverId ?? this.assignedApproverId,
      approvedBy: approvedBy ?? this.approvedBy,
      approvedAt: approvedAt ?? this.approvedAt,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
