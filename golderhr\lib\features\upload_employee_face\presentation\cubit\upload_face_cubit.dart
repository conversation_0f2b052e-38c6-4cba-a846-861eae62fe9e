import 'dart:io';
import 'package:bloc/bloc.dart';
import 'package:image_picker/image_picker.dart';
import '../../domain/entities/user_for_dropdown_entity.dart';
import '../../domain/reponsitories/upload_face_repository.dart';

import 'upload_face_state.dart';

class UploadFaceCubit extends Cubit<UploadFaceState> {
  final UploadFaceRepository repository;
  final ImagePicker _picker = ImagePicker();

  UploadFaceCubit({required this.repository}) : super(const UploadFaceState());

  Future<void> fetchUsers() async {
    emit(state.copyWith(status: UploadStatus.loadingUsers));
    final result = await repository.getUsersForDropdown();
    result.fold(
      (failure) => emit(
        state.copyWith(
          status: UploadStatus.failure,
          errorMessage: failure.message,
        ),
      ),
      (users) =>
          emit(state.copyWith(status: UploadStatus.success, users: users)),
    );
  }

  // S<PERSON>a đổi để sử dụng flag khi user là null
  void userSelected(UserForDropdownEntity? user) {
    if (user == null) {
      emit(state.copyWith(clearSelectedUser: true));
    } else {
      emit(state.copyWith(selectedUser: user));
    }
  }

  Future<void> pickImage() async {
    try {
      final pickedFile = await _picker.pickImage(source: ImageSource.gallery);
      if (pickedFile != null) {
        emit(state.copyWith(imageFile: File(pickedFile.path)));
      }
    } catch (e) {
      emit(
        state.copyWith(
          status: UploadStatus.failure,
          errorMessage: 'Không thể chọn ảnh: $e',
        ),
      );
    }
  }

  // Sửa đổi để sử dụng flag
  void clearImage() {
    emit(state.copyWith(clearImageFile: true));
  }

  Future<void> uploadFace() async {
    if (state.selectedUser == null || state.imageFile == null) {
      emit(
        state.copyWith(
          status: UploadStatus.failure,
          errorMessage: 'Vui lòng chọn nhân viên và ảnh',
        ),
      );
      return;
    }

    emit(state.copyWith(status: UploadStatus.uploading));
    final result = await repository.uploadEmployeeFace(
      userId: state.selectedUser!.id,
      imageFile: state.imageFile!,
    );

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: UploadStatus.failure,
          errorMessage: failure.message,
        ),
      ),
      // Sửa đổi để sử dụng các flag
      (imageUrl) => emit(
        state.copyWith(
          status: UploadStatus.uploadSuccess,
          clearImageFile: true,
          clearSelectedUser: true,
        ),
      ),
    );
  }
}
