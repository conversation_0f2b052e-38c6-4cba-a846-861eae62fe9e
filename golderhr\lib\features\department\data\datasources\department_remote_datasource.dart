import 'package:dio/dio.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/network/dio_client.dart';
import '../models/department_model.dart';
import '../../domain/entities/department_entity.dart';

abstract class DepartmentRemoteDataSource {
  Future<DepartmentListResult> getAllDepartments({
    int page = 1,
    int limit = 10,
    String? search,
    bool includeDeleted = false,
  });

  Future<DepartmentModel> getDepartmentById(String departmentId);

  Future<DepartmentModel> createDepartment({
    required String name,
    String? description,
    String? code,
    String? parentId,
  });

  Future<DepartmentModel> updateDepartment(
    String departmentId, {
    String? name,
    String? description,
    String? code,
    bool? isActive,
    bool? isDisabled,
    String? parentId,
  });

  Future<void> deleteDepartment(String departmentId);

  Future<DepartmentModel> restoreDepartment(String departmentId);

  Future<DepartmentModel> toggleDepartmentStatus(String departmentId);

  Future<List<DepartmentModel>> getDepartmentsForDropdown();

  Future<bool> checkDepartmentNameExists(String name, {String? excludeId});

  Future<bool> checkDepartmentCodeExists(String code, {String? excludeId});

  Future<List<DepartmentModel>> getDepartmentHierarchy();
}

class DepartmentRemoteDataSourceImpl implements DepartmentRemoteDataSource {
  final DioClient dioClient;

  DepartmentRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<DepartmentListResult> getAllDepartments({
    int page = 1,
    int limit = 10,
    String? search,
    bool includeDeleted = false,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
        'includeDeleted': includeDeleted,
        if (search != null && search.isNotEmpty) 'search': search,
      };

      final response = await dioClient.get(
        '/api/admin/departments',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        return DepartmentListResult.fromJson(response.data['data']);
      } else {
        throw ServerException('Failed to fetch departments');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }

  @override
  Future<DepartmentModel> getDepartmentById(String departmentId) async {
    try {
      final response = await dioClient.get(
        '/api/admin/departments/$departmentId',
      );

      if (response.statusCode == 200) {
        return DepartmentModel.fromJson(response.data['data']);
      } else {
        throw ServerException('Failed to fetch department');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }

  @override
  Future<DepartmentModel> createDepartment({
    required String name,
    String? description,
    String? code,
    String? parentId,
  }) async {
    try {
      final response = await dioClient.post(
        '/api/admin/departments',
        data: CreateDepartmentParams(
          name: name,
          description: description,
          code: code,
          parentId: parentId,
        ).toJson(),
      );

      if (response.statusCode == 201) {
        return DepartmentModel.fromJson(response.data['data']);
      } else {
        throw ServerException('Failed to create department');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }

  @override
  Future<DepartmentModel> updateDepartment(
    String departmentId, {
    String? name,
    String? description,
    String? code,
    bool? isActive,
    bool? isDisabled,
    String? parentId,
  }) async {
    try {
      final response = await dioClient.put(
        '/api/admin/departments/$departmentId',
        data: UpdateDepartmentParams(
          departmentId: departmentId,
          name: name,
          description: description,
          code: code,
          isActive: isActive,
          isDisabled: isDisabled,
          parentId: parentId,
        ).toJson(),
      );

      if (response.statusCode == 200) {
        return DepartmentModel.fromJson(response.data['data']);
      } else {
        throw ServerException('Failed to update department');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }

  @override
  Future<void> deleteDepartment(String departmentId) async {
    try {
      final response = await dioClient.delete(
        '/api/admin/departments/$departmentId',
      );

      if (response.statusCode != 200) {
        throw ServerException('Failed to delete department');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }

  @override
  Future<DepartmentModel> restoreDepartment(String departmentId) async {
    try {
      final response = await dioClient.patch(
        '/api/admin/departments/$departmentId/restore',
      );

      if (response.statusCode == 200) {
        return DepartmentModel.fromJson(response.data['data']);
      } else {
        throw ServerException('Failed to restore department');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }

  @override
  Future<DepartmentModel> toggleDepartmentStatus(String departmentId) async {
    try {
      final response = await dioClient.patch(
        '/api/admin/departments/$departmentId/toggle-status',
      );

      if (response.statusCode == 200) {
        return DepartmentModel.fromJson(response.data['data']);
      } else {
        throw ServerException('Failed to toggle department status');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }

  @override
  Future<List<DepartmentModel>> getDepartmentsForDropdown() async {
    try {
      final response = await dioClient.get(
        '/department-salary/departments-dropdown',
      );

      if (response.statusCode == 200) {
        final List<dynamic> departmentsJson = response.data['data'];
        return departmentsJson
            .map((dept) => DepartmentModel.fromJson(dept))
            .toList();
      } else {
        throw ServerException('Failed to fetch departments');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }

  @override
  Future<bool> checkDepartmentNameExists(
    String name, {
    String? excludeId,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'name': name,
        if (excludeId != null) 'excludeId': excludeId,
      };

      final response = await dioClient.get(
        '/api/admin/departments/check-name',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        return response.data['data']['exists'] ?? false;
      } else {
        throw ServerException('Failed to check department name');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }

  @override
  Future<bool> checkDepartmentCodeExists(
    String code, {
    String? excludeId,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'code': code,
        if (excludeId != null) 'excludeId': excludeId,
      };

      final response = await dioClient.get(
        '/api/admin/departments/check-code',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        return response.data['data']['exists'] ?? false;
      } else {
        throw ServerException('Failed to check department code');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }

  @override
  Future<List<DepartmentModel>> getDepartmentHierarchy() async {
    try {
      final response = await dioClient.get('/api/admin/departments/hierarchy');

      if (response.statusCode == 200) {
        final List<dynamic> departmentsJson = response.data['data'];
        return departmentsJson
            .map((dept) => DepartmentModel.fromJson(dept))
            .toList();
      } else {
        throw ServerException('Failed to fetch department hierarchy');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }
}
