import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/department_entity.dart';
import '../../domain/repositories/department_repository.dart';
import '../datasources/department_remote_datasource.dart';
import '../models/department_model.dart';

class DepartmentRepositoryImpl implements DepartmentRepository {
  final DepartmentRemoteDataSource remoteDataSource;

  DepartmentRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, DepartmentListResult>> getAllDepartments({
    int page = 1,
    int limit = 10,
    String? search,
    bool includeDeleted = false,
  }) async {
    try {
      final result = await remoteDataSource.getAllDepartments(
        page: page,
        limit: limit,
        search: search,
        includeDeleted: includeDeleted,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, DepartmentEntity>> getDepartmentById(
    String departmentId,
  ) async {
    try {
      final result = await remoteDataSource.getDepartmentById(departmentId);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, DepartmentEntity>> createDepartment({
    required String name,
    String? description,
    String? code,
    String? parentId,
  }) async {
    try {
      final result = await remoteDataSource.createDepartment(
        name: name,
        description: description,
        code: code,
        parentId: parentId,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, DepartmentEntity>> updateDepartment(
    String departmentId, {
    String? name,
    String? description,
    String? code,
    bool? isActive,
    bool? isDisabled,
    String? parentId,
  }) async {
    try {
      final result = await remoteDataSource.updateDepartment(
        departmentId,
        name: name,
        description: description,
        code: code,
        isActive: isActive,
        isDisabled: isDisabled,
        parentId: parentId,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteDepartment(String departmentId) async {
    try {
      await remoteDataSource.deleteDepartment(departmentId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, DepartmentEntity>> restoreDepartment(
    String departmentId,
  ) async {
    try {
      final result = await remoteDataSource.restoreDepartment(departmentId);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, DepartmentEntity>> toggleDepartmentStatus(
    String departmentId,
  ) async {
    try {
      final result = await remoteDataSource.toggleDepartmentStatus(
        departmentId,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<DepartmentEntity>>>
  getDepartmentsForDropdown() async {
    try {
      final result = await remoteDataSource.getDepartmentsForDropdown();
      return Right(result.cast<DepartmentEntity>());
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, bool>> checkDepartmentNameExists(
    String name, {
    String? excludeId,
  }) async {
    try {
      final result = await remoteDataSource.checkDepartmentNameExists(
        name,
        excludeId: excludeId,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, bool>> checkDepartmentCodeExists(
    String code, {
    String? excludeId,
  }) async {
    try {
      final result = await remoteDataSource.checkDepartmentCodeExists(
        code,
        excludeId: excludeId,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<DepartmentEntity>>>
  getDepartmentHierarchy() async {
    try {
      final result = await remoteDataSource.getDepartmentHierarchy();
      return Right(result.cast<DepartmentEntity>());
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred: ${e.toString()}'));
    }
  }
}
