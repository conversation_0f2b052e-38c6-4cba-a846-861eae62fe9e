import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/widgets/gradient_background.dart';
import 'package:golderhr/shared/widgets/responsive_spacer.dart';

import '../../../../core/animation/auth_page_animation_mixin.dar.dart';

import '../../../../core/animation/fade_slide_animation.dart';
import '../widgets/auth_card.dart';
import '../widgets/auth_redirect_row.dart';
import '../widgets/auth_welcome_section.dart';
import '../widgets/language_selector.dart';
import '../widgets/register_form.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage>
    with TickerProviderStateMixin, AuthPageAnimationMixin {
  @override
  void initState() {
    super.initState();
    initializeAnimations();
  }

  @override
  void dispose() {
    disposeAnimations();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: SafeArea(
          child: SingleChildScrollView(
            physics: AlwaysScrollableScrollPhysics(),
            keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height,
              ),
              child: Padding(
                padding: context.responsive.padding(
                  horizontal: 24,
                  vertical: 10,
                ),
                child: Column(
                  children: [
                    _buildHeader(context),
                    ResponsiveSpacer(
                      mobileSize: 25,
                      tabletSize: 30,
                      mobileLandscapeSize: 25,
                      tabletLandscapeSize: 30,
                    ),
                    _buildRegisterForm(),
                    ResponsiveSpacer(
                      mobileSize: 15,
                      tabletSize: 20,
                      mobileLandscapeSize: 15,
                      tabletLandscapeSize: 20,
                    ),

                    _buildAuthRedirectRow(),
                    ResponsiveSpacer(
                      mobileSize: 15,
                      tabletSize: 20,
                      mobileLandscapeSize: 15,
                      tabletLandscapeSize: 20,
                    ),
                    _buildLanguageSelector(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRegisterForm() {
    return FadeSlideAnimation(
      fadeAnimation: fadeAnimation,
      slideAnimation: slideAnimation,
      child: AuthCard(child: RegisterForm()),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return FadeSlideAnimation(
      fadeAnimation: fadeAnimation,
      slideAnimation: slideAnimation,
      child: AuthWelcomeSection(
        title: context.l10n.registerTitleSignUp,
        subtitle: context.l10n.registerSubTitleSignUp,
        showLogo: false,
      ),
    );
  }

  Widget _buildLanguageSelector() {
    return FadeTransition(
      opacity: fadeAnimation,
      child: const LanguageSelector(),
    );
  }

  Widget _buildAuthRedirectRow() {
    return FadeSlideAnimation(
      fadeAnimation: fadeAnimation,
      slideAnimation: slideAnimation,
      child: AuthRedirectRow(
        questionText: context.l10n.registerHaveAccount,
        actionText: context.l10n.loginSignIn,
        onPressed: () {
          context.pop();
        },
      ),
    );
  }
}
