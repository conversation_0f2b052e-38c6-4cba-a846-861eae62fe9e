import '../../../../core/error/exceptions.dart';
import '../../../../core/network/dio_client.dart';
import '../../domain/entities/overtime_request_entity.dart';
import '../models/approver_model.dart';
import '../models/overtime_request_model.dart';
import '../models/overtime_summary_model.dart';

abstract class OvertimeRemoteDataSource {
  Future<OvertimeSummaryModel> getOvertimeSummary();
  Future<List<OvertimeRequestModel>> getOvertimeHistory({
    int page = 1,
    int limit = 10,
    OvertimeStatus? status,
  });
  Future<List<ApproverModel>> getApprovers();
  Future<OvertimeRequestModel> submitOvertimeRequest({
    required DateTime date,
    required DateTime startTime,
    required DateTime endTime,
    required String reason,
    required OvertimeType type,
    String? approverId,
  });
  Future<OvertimeRequestModel> updateOvertimeRequest({
    required String requestId,
    required DateTime date,
    required DateTime startTime,
    required DateTime endTime,
    required String reason,
    required OvertimeType type,
    String? approverId,
  });
  Future<bool> cancelOvertimeRequest(String requestId);
  Future<OvertimeRequestModel> getOvertimeRequestById(String requestId);
}

class OvertimeRemoteDataSourceImpl implements OvertimeRemoteDataSource {
  final DioClient dioClient;

  OvertimeRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<OvertimeSummaryModel> getOvertimeSummary() async {
    try {
      final response = await dioClient.get('/api/overtime/summary');

      if (response.statusCode == 200) {
        return OvertimeSummaryModel.fromJson(response.data['data']);
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to get overtime summary',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException(
        'Network error occurred while fetching overtime summary',
      );
    }
  }

  @override
  Future<List<OvertimeRequestModel>> getOvertimeHistory({
    int page = 1,
    int limit = 10,
    OvertimeStatus? status,
  }) async {
    try {
      final queryParams = <String, dynamic>{'page': page, 'limit': limit};

      if (status != null) {
        queryParams['status'] = _overtimeStatusToString(status);
      }

      final response = await dioClient.get(
        '/api/overtime/history',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['data'];
        return data.map((json) => OvertimeRequestModel.fromJson(json)).toList();
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to get overtime history',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException(
        'Network error occurred while fetching overtime history',
      );
    }
  }

  @override
  Future<List<ApproverModel>> getApprovers() async {
    try {
      print('🔍 [DEBUG] Calling /api/overtime/approvers...');
      final response = await dioClient.get('/api/overtime/approvers');

      print('🔍 [DEBUG] Response status: ${response.statusCode}');
      print('🔍 [DEBUG] Response data: ${response.data}');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['data'];
        print('🔍 [DEBUG] Approvers data: $data');
        final approvers = data
            .map((json) => ApproverModel.fromJson(json))
            .toList();
        print('🔍 [DEBUG] Parsed ${approvers.length} approvers');
        return approvers;
      } else {
        print('❌ [ERROR] Failed to get approvers: ${response.data}');
        throw ServerException(
          response.data['message'] ?? 'Failed to get approvers',
        );
      }
    } catch (e) {
      print('❌ [ERROR] Exception in getApprovers: $e');
      if (e is ServerException) rethrow;
      throw ServerException('Network error occurred while fetching approvers');
    }
  }

  @override
  Future<OvertimeRequestModel> submitOvertimeRequest({
    required DateTime date,
    required DateTime startTime,
    required DateTime endTime,
    required String reason,
    required OvertimeType type,
    String? approverId,
  }) async {
    try {
      final data = {
        'date': date.toIso8601String(),
        'startTime': startTime.toIso8601String(),
        'endTime': endTime.toIso8601String(),
        'reason': reason,
        'type': _overtimeTypeToString(type),
      };

      if (approverId != null) {
        data['approverId'] = approverId;
      }

      final response = await dioClient.post('/api/overtime/submit', data: data);

      if (response.statusCode == 201) {
        return OvertimeRequestModel.fromJson(response.data['data']);
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to submit overtime request',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException(
        'Network error occurred while submitting overtime request',
      );
    }
  }

  @override
  Future<OvertimeRequestModel> updateOvertimeRequest({
    required String requestId,
    required DateTime date,
    required DateTime startTime,
    required DateTime endTime,
    required String reason,
    required OvertimeType type,
    String? approverId,
  }) async {
    try {
      final data = {
        'date': date.toIso8601String(),
        'startTime': startTime.toIso8601String(),
        'endTime': endTime.toIso8601String(),
        'reason': reason,
        'type': _overtimeTypeToString(type),
      };

      if (approverId != null) {
        data['approverId'] = approverId;
      }

      final response = await dioClient.put(
        '/api/overtime/$requestId',
        data: data,
      );

      if (response.statusCode == 200) {
        return OvertimeRequestModel.fromJson(response.data['data']);
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to update overtime request',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException(
        'Network error occurred while updating overtime request',
      );
    }
  }

  @override
  Future<bool> cancelOvertimeRequest(String requestId) async {
    try {
      final response = await dioClient.delete('/api/overtime/$requestId');

      if (response.statusCode == 200) {
        return true;
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to cancel overtime request',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException(
        'Network error occurred while canceling overtime request',
      );
    }
  }

  @override
  Future<OvertimeRequestModel> getOvertimeRequestById(String requestId) async {
    try {
      final response = await dioClient.get('/api/overtime/$requestId');

      if (response.statusCode == 200) {
        return OvertimeRequestModel.fromJson(response.data['data']);
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to get overtime request',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException(
        'Network error occurred while fetching overtime request',
      );
    }
  }

  String _overtimeTypeToString(OvertimeType type) {
    switch (type) {
      case OvertimeType.holiday:
        return 'holiday';
      case OvertimeType.weekend:
        return 'weekend';
      case OvertimeType.regular:
        return 'regular';
    }
  }

  String _overtimeStatusToString(OvertimeStatus status) {
    switch (status) {
      case OvertimeStatus.approved:
        return 'approved';
      case OvertimeStatus.rejected:
        return 'rejected';
      case OvertimeStatus.pending:
        return 'pending';
    }
  }
}
