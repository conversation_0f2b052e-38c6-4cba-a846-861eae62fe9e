import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:golderhr/core/cache/image_cache_manager.dart';

class CachedAvatar extends StatelessWidget {
  final String? imageUrl;
  final double size;
  final double borderWidth;
  final Color borderColor;
  final Color? backgroundColor;
  final IconData fallbackIcon;
  final double? fallbackIconSize;
  final Color? fallbackIconColor;
  final VoidCallback? onTap;
  final Widget? editIcon;

  const CachedAvatar({
    super.key,
    this.imageUrl,
    this.size = 80,
    this.borderWidth = 2,
    this.borderColor = Colors.white,
    this.backgroundColor,
    this.fallbackIcon = Icons.person,
    this.fallbackIconSize,
    this.fallbackIconColor,
    this.onTap,
    this.editIcon,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor = backgroundColor ?? Colors.grey[300];
    final effectiveFallbackIconSize = fallbackIconSize ?? size * 0.5;
    final effectiveFallbackIconColor = fallbackIconColor ?? Colors.grey;

    Widget avatarWidget = Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: borderColor, width: borderWidth),
      ),
      child: ClipOval(
        child: imageUrl != null && imageUrl!.isNotEmpty
            ? CachedNetworkImage(
                imageUrl: imageUrl!,
                width: size,
                height: size,
                fit: BoxFit.cover,
                cacheManager: ProfileImageCacheManager.instance,
                placeholder: (context, url) => Container(
                  width: size,
                  height: size,
                  decoration: BoxDecoration(
                    color: effectiveBackgroundColor,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  width: size,
                  height: size,
                  decoration: BoxDecoration(
                    color: effectiveBackgroundColor,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    fallbackIcon,
                    size: effectiveFallbackIconSize,
                    color: effectiveFallbackIconColor,
                  ),
                ),
                // Remove memory cache size constraints to avoid ImageCacheManager requirement
              )
            : Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  color: effectiveBackgroundColor,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  fallbackIcon,
                  size: effectiveFallbackIconSize,
                  color: effectiveFallbackIconColor,
                ),
              ),
      ),
    );

    if (editIcon != null) {
      avatarWidget = Stack(
        alignment: Alignment.center,
        children: [
          avatarWidget,
          Positioned(bottom: 0, right: 0, child: editIcon!),
        ],
      );
    }

    if (onTap != null) {
      return GestureDetector(onTap: onTap, child: avatarWidget);
    }

    return avatarWidget;
  }
}

/// Predefined avatar sizes for consistency
class AvatarSize {
  static const double small = 40;
  static const double medium = 60;
  static const double large = 80;
  static const double extraLarge = 120;
}
