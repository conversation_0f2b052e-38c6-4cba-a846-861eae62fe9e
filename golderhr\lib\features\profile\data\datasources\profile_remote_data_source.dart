import 'package:golderhr/features/profile/data/models/user_profile_model.dart';
import 'package:golderhr/core/network/dio_client.dart';
import 'package:golderhr/core/error/exceptions.dart';
import 'package:dio/dio.dart';
import 'dart:io';

abstract class ProfileRemoteDataSource {
  Future<UserProfileModel> getUserProfile();
  Future<UserProfileModel> updateUserProfile({
    required String name,
    required String email,
    required String phone,
    String? avatarUrl,
  });
  Future<String> uploadProfileImage(String imagePath);
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  });
}

class ProfileRemoteDataSourceImpl implements ProfileRemoteDataSource {
  final DioClient dioClient;

  ProfileRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<UserProfileModel> getUserProfile() async {
    try {
      final response = await dioClient.get('/api/auth/me');

      if (response.statusCode == 200) {
        final userData = response.data['data'];
        return UserProfileModel(
          id: userData['_id'] ?? '',
          name: userData['fullname'] ?? '',
          email: userData['email'] ?? '',
          phone: userData['phone'] ?? '',
          avatarUrl: userData['avatar'] ?? '',
          department:
              userData['department'] ?? _getDepartmentName(userData['role']),
          position: userData['position'] ?? _getPositionName(userData['role']),
        );
      } else {
        throw ServerException('Failed to get user profile');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }

  String _getDepartmentName(dynamic role) {
    if (role is Map<String, dynamic>) {
      return role['name'] ?? 'N/A';
    }
    return 'N/A';
  }

  String _getPositionName(dynamic role) {
    if (role is Map<String, dynamic>) {
      final roleName = role['name'] ?? '';
      switch (roleName.toLowerCase()) {
        case 'admin':
          return 'Administrator';
        case 'manager':
          return 'Manager';
        case 'user':
          return 'Employee';
        default:
          return 'Employee';
      }
    }
    return 'Employee';
  }

  @override
  Future<UserProfileModel> updateUserProfile({
    required String name,
    required String email,
    required String phone,
    String? avatarUrl,
  }) async {
    try {
      final response = await dioClient.put(
        '/api/auth/profile',
        data: {
          'fullname': name,
          'email': email,
          'phone': phone,
          if (avatarUrl != null) 'avatar': avatarUrl,
        },
      );

      if (response.statusCode == 200) {
        final userData = response.data['data'];
        return UserProfileModel(
          id: userData['_id'] ?? '',
          name: userData['fullname'] ?? '',
          email: userData['email'] ?? '',
          phone: userData['phone'] ?? '',
          avatarUrl: userData['avatar'] ?? '',
          department:
              userData['department'] ?? _getDepartmentName(userData['role']),
          position: userData['position'] ?? _getPositionName(userData['role']),
        );
      } else {
        throw ServerException('Failed to update profile');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }

  @override
  Future<String> uploadProfileImage(String imagePath) async {
    try {
      final file = File(imagePath);
      final formData = FormData.fromMap({
        'avatar': await MultipartFile.fromFile(
          imagePath,
          filename: file.path.split('/').last,
        ),
      });

      final response = await dioClient.post(
        '/api/auth/upload-avatar',
        data: formData,
      );

      if (response.statusCode == 200) {
        return response.data['data']['avatarUrl'] ?? '';
      } else {
        throw ServerException('Failed to upload image');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }

  @override
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final response = await dioClient.post(
        '/api/auth/change-password',
        data: {'oldPassword': currentPassword, 'newPassword': newPassword},
      );

      if (response.statusCode != 200) {
        throw ServerException('Failed to change password');
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: $e');
    }
  }
}
