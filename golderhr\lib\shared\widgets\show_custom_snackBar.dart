import 'dart:async';

import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/widgets/responsive_spacer.dart';

/// Hiển thị một SnackBar tùy chỉnh ở phía trên màn hình.
///
/// Widget này sử dụng Overlay để có toàn quyền kiểm soát vị trí và animation.
/// - [title]: Tiêu đề của thông báo.
/// - [message]: Nội dung chi tiết của thông báo.
/// - [isError]: <PERSON><PERSON><PERSON> là true, sẽ hiển thị màu đỏ và icon lỗi. Ngược lại là màu xanh và icon thành công.
/// - [duration]: Thời gian hiển thị thông báo trước khi tự động đóng.
/// - [onDismiss]: Callback được gọi khi thông báo được đóng.
void showTopSnackBar(
  BuildContext context, {
  required String title,
  required String message,
  bool isError = false,
  Duration duration = const Duration(seconds: 4),
  VoidCallback? onDismiss,
}) {
  // Sử dụng Overlay để hiển thị widget trên tất cả các widget khác
  final overlay = Overlay.of(context);
  OverlayEntry? overlayEntry;

  // Key để truy cập State của widget con và gọi hàm đóng
  final GlobalKey<_TopSnackBarWidgetState> snackBarKey = GlobalKey();

  overlayEntry = OverlayEntry(
    builder: (context) {
      return _TopSnackBarWidget(
        key: snackBarKey,
        title: title,
        message: message,
        isError: isError,
        onDismiss: () {
          // Khi animation đóng kết thúc, xóa widget khỏi Overlay
          overlayEntry?.remove();
          overlayEntry = null; // Giải phóng bộ nhớ
          onDismiss?.call();
        },
      );
    },
  );

  // Thêm widget vào Overlay
  overlay.insert(overlayEntry!);

  // Thiết lập bộ đếm thời gian để tự động đóng
  Timer(duration, () {
    // Gọi hàm đóng thông qua GlobalKey
    snackBarKey.currentState?.dismiss();
  });
}

// Widget chính của SnackBar, là một StatefulWidget để quản lý AnimationController
class _TopSnackBarWidget extends StatefulWidget {
  final String title;
  final String message;
  final bool isError;
  final VoidCallback onDismiss;

  const _TopSnackBarWidget({
    super.key,
    required this.title,
    required this.message,
    required this.isError,
    required this.onDismiss,
  });

  @override
  State<_TopSnackBarWidget> createState() => _TopSnackBarWidgetState();
}

class _TopSnackBarWidgetState extends State<_TopSnackBarWidget>
    with SingleTickerProviderStateMixin {
  late final AnimationController _animationController;
  late final Animation<Offset> _slideAnimation;

  // Cờ để tránh việc gọi hàm dismiss nhiều lần
  bool _isDismissing = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 700),
      reverseDuration: const Duration(milliseconds: 300),
    );

    // Animation trượt từ phải sang trái
    // Offset(1.1, 0) -> bắt đầu từ bên phải, ngoài màn hình 10%
    // Offset.zero   -> kết thúc tại vị trí cuối cùng
    _slideAnimation =
        Tween<Offset>(begin: const Offset(1.1, 0), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic, // Animation vào
            reverseCurve: Curves.easeInCubic, // Animation ra
          ),
        );

    // Bắt đầu animation vào
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Bắt đầu animation đóng và gọi callback onDismiss khi hoàn tất
  void dismiss() {
    if (_isDismissing) return; // Nếu đang đóng rồi thì không làm gì cả
    _isDismissing = true;

    // Chạy animation ngược lại (trượt ra ngoài)
    _animationController.reverse().then((_) {
      // Khi animation kết thúc, gọi callback để xóa OverlayEntry
      widget.onDismiss();
    });
  }

  @override
  Widget build(BuildContext context) {
    // Lấy padding trên cùng của thiết bị (tai thỏ, camera, v.v.)
    final topPadding = MediaQuery.of(context).padding.top;

    return Positioned(
      // Vị trí cố định ở trên cùng, có khoảng cách an toàn với status bar
      top: topPadding + 16.0,
      left: 16.0,
      right: 16.0,
      child: GestureDetector(
        // Cho phép người dùng vuốt ngang để đóng (vuốt từ trái sang phải)
        onHorizontalDragUpdate: (details) {
          // Vuốt sang phải
          if (details.primaryDelta! > 1) {
            dismiss();
          }
        },
        child: SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(
            opacity: _animationController, // Dùng chung controller cho fade
            child: Material(
              color: Colors.transparent, // Để thấy được gradient của Container
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: widget.isError
                        ? [Colors.red.shade700, Colors.red.shade500]
                        : [const Color(0xFF22C55E), const Color(0xFF86EFAC)],
                    // Màu xanh lá đẹp hơn
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.15),
                      blurRadius: 20,
                      spreadRadius: -5,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Icon(
                      widget.isError
                          ? Icons.error_outline_rounded
                          : Icons.check_circle_outline_rounded,
                      color: Colors.white,
                      size: context.responsive.fontSize(28),
                    ),
                    ResponsiveSpacer(
                      mobileSize: 16,
                      tabletSize: 16,
                      axis: Axis.horizontal,
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            widget.title,
                            style: context.lightTheme.textTheme.titleMedium!
                                .copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          ResponsiveSpacer(mobileSize: 4, tabletSize: 6),
                          Text(
                            widget.message,
                            style: context.lightTheme.textTheme.titleMedium!
                                .copyWith(color: Colors.white),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    ResponsiveSpacer(
                      mobileSize: 8,
                      tabletSize: 10,
                      axis: Axis.horizontal,
                    ),
                    IconButton(
                      onPressed: dismiss,
                      icon: Icon(
                        Icons.close,
                        color: Colors.white,
                        size: context.responsive.fontSize(20),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
