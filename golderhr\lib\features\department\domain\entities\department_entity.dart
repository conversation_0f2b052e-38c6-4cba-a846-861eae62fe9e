import 'package:equatable/equatable.dart';

class DepartmentEntity extends Equatable {
  final String id;
  final String name;
  final String? description;
  final String? code;
  final bool isActive;
  final bool isDeleted;
  final bool isDisabled;
  final String? parentId;
  final int? idMapper;
  final String? codeMapper;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const DepartmentEntity({
    required this.id,
    required this.name,
    this.description,
    this.code,
    this.isActive = true,
    this.isDeleted = false,
    this.isDisabled = false,
    this.parentId,
    this.idMapper,
    this.codeMapper,
    this.createdAt,
    this.updatedAt,
  });

  DepartmentEntity copyWith({
    String? id,
    String? name,
    String? description,
    String? code,
    bool? isActive,
    bool? isDeleted,
    bool? isDisabled,
    String? parentId,
    int? idMapper,
    String? codeMapper,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DepartmentEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      code: code ?? this.code,
      isActive: isActive ?? this.isActive,
      isDeleted: isDeleted ?? this.isDeleted,
      isDisabled: isDisabled ?? this.isDisabled,
      parentId: parentId ?? this.parentId,
      idMapper: idMapper ?? this.idMapper,
      codeMapper: codeMapper ?? this.codeMapper,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    code,
    isActive,
    isDeleted,
    isDisabled,
    parentId,
    idMapper,
    codeMapper,
    createdAt,
    updatedAt,
  ];

  @override
  String toString() {
    return 'DepartmentEntity(id: $id, name: $name, description: $description, code: $code, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

class DepartmentListResult {
  final List<DepartmentEntity> departments;
  final int totalCount;
  final int currentPage;
  final int totalPages;
  final bool hasNextPage;
  final bool hasPrevPage;

  const DepartmentListResult({
    required this.departments,
    required this.totalCount,
    required this.currentPage,
    required this.totalPages,
    required this.hasNextPage,
    required this.hasPrevPage,
  });

  factory DepartmentListResult.fromJson(Map<String, dynamic> json) {
    return DepartmentListResult(
      departments:
          (json['departments'] as List<dynamic>?)
              ?.map(
                (deptJson) => DepartmentEntity(
                  id: deptJson['_id'] ?? deptJson['id'] ?? '',
                  name: deptJson['name'] ?? '',
                  description: deptJson['description'],
                  code: deptJson['code'],
                  isActive: deptJson['isActive'] ?? true,
                  createdAt: deptJson['createdAt'] != null
                      ? DateTime.parse(deptJson['createdAt'])
                      : null,
                  updatedAt: deptJson['updatedAt'] != null
                      ? DateTime.parse(deptJson['updatedAt'])
                      : null,
                ),
              )
              .toList() ??
          [],
      totalCount: json['totalCount'] ?? 0,
      currentPage: json['currentPage'] ?? 1,
      totalPages: json['totalPages'] ?? 1,
      hasNextPage: json['hasNextPage'] ?? false,
      hasPrevPage: json['hasPrevPage'] ?? false,
    );
  }
}
