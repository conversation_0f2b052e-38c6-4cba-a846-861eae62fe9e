import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/overtime_request_entity.dart';
import '../repositories/overtime_admin_repository.dart';

class RejectOvertimeRequest
    implements UseCase<OvertimeRequestEntity, RejectOvertimeRequestParams> {
  final OvertimeAdminRepository repository;

  RejectOvertimeRequest(this.repository);

  @override
  Future<Either<Failure, OvertimeRequestEntity>> call(
    RejectOvertimeRequestParams params,
  ) async {
    return await repository.rejectOvertimeRequest(
      params.requestId,
      params.rejectionReason,
    );
  }
}

class RejectOvertimeRequestParams extends Equatable {
  final String requestId;
  final String rejectionReason;

  const RejectOvertimeRequestParams({
    required this.requestId,
    required this.rejectionReason,
  });

  @override
  List<Object> get props => [requestId, rejectionReason];
}
