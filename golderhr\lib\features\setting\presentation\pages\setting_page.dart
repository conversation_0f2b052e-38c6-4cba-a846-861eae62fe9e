import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart'; // Thêm import này
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/core/routes/app_routes.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../shared/theme/app_colors.dart';
//... các import khác

// Thêm import cho cubit và state
import '../../../../shared/widgets/responsive_spacer.dart';
import '../../../../shared/widgets/title_app_bar.dart';
import '../../../cubit/language_cubit.dart';
import '../cubit/settings_cubit.dart';
import '../cubit/settings_state.dart';

import '../widgets/dropdown_tile.dart';
import '../widgets/logout_button.dart';
import '../widgets/logout_dialog.dart';
import '../widgets/section_title.dart';
import '../widgets/settings_group.dart';
import '../widgets/settings_tile.dart';
import '../widgets/switch_tile.dart';

class SettingPage extends StatefulWidget {
  // Chuyển thành StatelessWidget
  const SettingPage({super.key});

  @override
  State<SettingPage> createState() => _SettingPageState();
}

class _SettingPageState extends State<SettingPage> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      // Bọc page bằng BlocProvider để cung cấp Cubit
      create: (context) =>
          SettingsCubit()..loadInitialSettings(), // Tạo và load settings
      child: const SettingView(), // Tách view ra để dễ đọc
    );
  }
}

class SettingView extends StatelessWidget {
  const SettingView({super.key});

  @override
  Widget build(BuildContext context) {
    // Lấy cubit để gọi hàm khi tương tác
    final settingsCubit = context.read<SettingsCubit>();
    final Map<String, String> languageOptions = const {
      'en': 'English',
      'vi': 'Tiếng Việt',
    };

    return Scaffold(
      appBar: TitleAppBar(title: context.l10n.setting),
      body: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Padding(
          padding: context.responsive.padding(all: 16),
          // Dùng BlocBuilder để rebuild UI khi state thay đổi
          child: BlocBuilder<SettingsCubit, SettingsState>(
            builder: (context, state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SectionTitle(title: context.l10n.settingAccount),
                  ResponsiveSpacer(mobileSize: 10, tabletSize: 12),
                  SettingsGroup(
                    children: [
                      SettingsTile(
                        title: context.l10n.settingTitleProfile,
                        subtitle: context.l10n.settingSubTitleProfile,
                        icon: Iconsax.personalcard,
                        color: Colors.blue,
                        onTap: () => context.push(AppRoutes.profile),
                      ),
                    ],
                  ),
                  ResponsiveSpacer(mobileSize: 20, tabletSize: 24),
                  SectionTitle(title: context.l10n.settingCheckInAndSecurity),
                  ResponsiveSpacer(mobileSize: 10, tabletSize: 12),
                  SettingsGroup(
                    children: [
                      SwitchTile(
                        title: context.l10n.settingFaceRecognition,
                        subtitle: context.l10n.settingSubEnableFaceRecognition,
                        icon: Icons.face_retouching_natural_outlined,
                        color: AppColors.primary,

                        value: state.isFaceRecognitionEnabled,

                        onChanged: (value) =>
                            settingsCubit.toggleFaceRecognition(value),
                      ),
                      SwitchTile(
                        title: context.l10n.settingBiometricLogin,
                        subtitle: context.l10n.settingSubUseBiometricLogin,
                        icon: Icons.fingerprint,
                        color: Colors.purple.shade600,
                        value: state.isBiometricLoginEnabled,
                        onChanged: (value) =>
                            settingsCubit.toggleBiometricLogin(value),
                      ),
                      SwitchTile(
                        title: context.l10n.settingAutoCheckOut,
                        subtitle: context.l10n.settingSubEnableAutoCheckOut,
                        icon: Iconsax.logout,
                        color: Colors.teal.shade600,
                        value: state.isAutoCheckOutEnabled,
                        onChanged: (value) =>
                            settingsCubit.toggleAutoCheckOut(value),
                      ),
                    ],
                  ),
                  ResponsiveSpacer(mobileSize: 10, tabletSize: 12),
                  SectionTitle(title: context.l10n.settingApp),
                  ResponsiveSpacer(mobileSize: 10, tabletSize: 12),
                  SettingsGroup(
                    children: [
                      BlocBuilder<LanguageCubit, String>(
                        builder: (context, currentLangCode) {
                          return DropdownTile(
                            // responsive: responsive,
                            // theme: theme,
                            title: context.l10n.settingLanguage,
                            // Lấy tên hiển thị dựa vào state (langCode) từ Cubit
                            value:
                                languageOptions[currentLangCode] ?? 'English',
                            icon: Icons.language,
                            color: AppColors.warning,
                            // Cung cấp danh sách các tên hiển thị để người dùng chọn
                            options: languageOptions.values.toList(),
                            onChanged: (selectedDisplayName) {
                              if (selectedDisplayName != null) {
                                // Khi người dùng chọn, tìm lại langCode tương ứng
                                final selectedLangCode = languageOptions.entries
                                    .firstWhere(
                                      (entry) =>
                                          entry.value == selectedDisplayName,
                                    )
                                    .key;
                                // Gọi phương thức trong Cubit để thay đổi ngôn ngữ
                                context.read<LanguageCubit>().changeLanguage(
                                  selectedLangCode,
                                );
                              }
                            },
                          );
                        },
                      ),
                      // === KẾT THÚC PHẦN CHỈNH SỬA ===

                      // Theme setting - simplified
                      SettingsTile(
                        title: context.l10n.settingTheme,
                        subtitle: "System",
                        icon: Icons.palette_outlined,
                        color: Colors.pink.shade600,
                        showArrow: false,
                      ),
                    ],
                  ),
                  ResponsiveSpacer(mobileSize: 10, tabletSize: 12),
                  SectionTitle(title: context.l10n.settingIntroduce),
                  ResponsiveSpacer(mobileSize: 10, tabletSize: 12),
                  SettingsGroup(
                    children: [
                      SettingsTile(
                        title: context.l10n.settingVersion,
                        subtitle: "1.1.0",
                        icon: Icons.info_outline,
                        color: AppColors.textSecondary,
                        showArrow: false,
                      ),
                    ],
                  ),
                  ResponsiveSpacer(mobileSize: 10, tabletSize: 12),
                  LogoutButton(
                    onPressed: () {
                      LogoutDialog.show(context);
                    },
                    title: context.l10n.logout,
                  ),
                  ResponsiveSpacer(mobileSize: 10, tabletSize: 12),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
