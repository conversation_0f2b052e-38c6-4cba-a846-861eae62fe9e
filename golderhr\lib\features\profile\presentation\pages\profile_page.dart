import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/widgets/title_app_bar.dart';
import 'package:golderhr/shared/widgets/responsive_spacer.dart';
import 'package:golderhr/features/profile/presentation/cubit/profile_cubit.dart';
import 'package:golderhr/features/profile/presentation/cubit/profile_state.dart';
import 'package:golderhr/features/profile/presentation/widgets/job_info_card.dart';
import 'package:iconsax/iconsax.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/routes/app_routes.dart';
import 'package:golderhr/shared/theme/app_colors.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  @override
  void initState() {
    super.initState();
    context.read<ProfileCubit>().fetchUserProfile();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final responsive = context.responsive;

    return Scaffold(
      appBar: TitleAppBar(title: context.l10n.profile),
      body: BlocBuilder<ProfileCubit, ProfileState>(
        builder: (context, state) {
          if (state.status == ProfileStatus.loading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state.status == ProfileStatus.error) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Iconsax.warning_2,
                    size: 64,
                    color: theme.colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    state.errorMessage ?? 'Unknown error',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: theme.colorScheme.error,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<ProfileCubit>().fetchUserProfile();
                    },
                    child: Text(context.l10n.retry),
                  ),
                ],
              ),
            );
          }

          final userProfile = state.userProfile;

          return SingleChildScrollView(
            padding: responsive.padding(all: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Profile Header
                _buildProfileHeader(userProfile, theme, responsive),
                ResponsiveSpacer(mobileSize: 24, tabletSize: 32),

                // Job Information
                JobInfoCard(
                  position: userProfile.position,
                  department: userProfile.department,
                ),
                ResponsiveSpacer(mobileSize: 16, tabletSize: 20),

                // Contact Information
                _buildContactInfo(userProfile, theme, responsive),
                ResponsiveSpacer(mobileSize: 16, tabletSize: 20),

                // Action Buttons
                _buildActionButtons(theme, responsive),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildProfileHeader(
    dynamic userProfile,
    ThemeData theme,
    dynamic responsive,
  ) {
    return Card(
      elevation: 1,
      shadowColor: Colors.black.withValues(alpha: 0.05),
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade300, width: 1.5),
      ),
      child: Padding(
        padding: responsive.padding(all: 20.0),
        child: Row(
          children: [
            // Avatar
            CircleAvatar(
              radius: 40,
              backgroundColor: AppColors.primary.withValues(alpha: 0.1),
              child: userProfile.avatarUrl.isNotEmpty
                  ? CachedNetworkImage(
                      imageUrl: userProfile.avatarUrl,
                      imageBuilder: (context, imageProvider) => CircleAvatar(
                        radius: 40,
                        backgroundImage: imageProvider,
                      ),
                      placeholder: (context, url) =>
                          const CircularProgressIndicator(),
                      errorWidget: (context, url, error) => Icon(
                        Iconsax.user,
                        size: 40,
                        color: AppColors.primary,
                      ),
                    )
                  : Icon(Iconsax.user, size: 40, color: AppColors.primary),
            ),
            const SizedBox(width: 16),
            // User Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    userProfile.name,
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    userProfile.email,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfo(
    dynamic userProfile,
    ThemeData theme,
    dynamic responsive,
  ) {
    return Card(
      elevation: 1,
      shadowColor: Colors.black.withValues(alpha: 0.05),
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: Colors.grey.shade300, width: 1.5),
      ),
      child: Padding(
        padding: responsive.padding(all: 20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.l10n.contactInfo,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildContactItem(
              icon: Iconsax.call,
              label: context.l10n.profilePhone,
              value: userProfile.phone,
              theme: theme,
            ),
            const SizedBox(height: 12),
            _buildContactItem(
              icon: Iconsax.sms,
              label: context.l10n.loginEmail,
              value: userProfile.email,
              theme: theme,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactItem({
    required IconData icon,
    required String label,
    required String value,
    required ThemeData theme,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, size: 20, color: theme.colorScheme.primary),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
              Text(
                value,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(ThemeData theme, dynamic responsive) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () async {
              final result = await context.push(AppRoutes.editProfile);
              // If profile was updated, refresh the data
              if (result == true && mounted) {
                context.read<ProfileCubit>().fetchUserProfile();
              }
            },
            icon: const Icon(Iconsax.edit),
            label: Text(context.l10n.editProfile),
            style: ElevatedButton.styleFrom(
              padding: responsive.padding(vertical: 16.0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () async {
              final result = await context.push(AppRoutes.changePassword);
              // Show success message if password was changed
              if (result == true && mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Password changed successfully'),
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              }
            },
            icon: const Icon(Iconsax.lock),
            label: Text(context.l10n.changePassword),
            style: OutlinedButton.styleFrom(
              padding: responsive.padding(vertical: 16.0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
