import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/admin_user_entity.dart';
import '../repositories/admin_user_repository.dart';

class CreateUserUseCase implements UseCase<AdminUserEntity, CreateUserParams> {
  final AdminUserRepository repository;

  CreateUserUseCase(this.repository);

  @override
  Future<Either<Failure, AdminUserEntity>> call(CreateUserParams params) async {
    return await repository.createUser(
      fullname: params.fullname,
      email: params.email,
      password: params.password,
      phone: params.phone,
      department: params.department,
      position: params.position,
      role: params.role,
      organization: params.organization,
    );
  }
}

class CreateUserParams {
  final String fullname;
  final String email;
  final String password;
  final String? phone;
  final String? department;
  final String? position;
  final String role;
  final String? organization;

  CreateUserParams({
    required this.fullname,
    required this.email,
    required this.password,
    this.phone,
    this.department,
    this.position,
    required this.role,
    this.organization,
  });
}
