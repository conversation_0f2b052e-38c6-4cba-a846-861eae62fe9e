import '../../domain/entities/overtime_request_entity.dart';

class ApproverModel extends ApproverEntity {
  const ApproverModel({
    required super.id,
    required super.fullname,
    required super.department,
    required super.position,
    required super.email,
    required super.roleName,
  });

  factory ApproverModel.fromJson(Map<String, dynamic> json) {
    return ApproverModel(
      id: json['_id'] ?? json['id'] ?? '',
      fullname: json['fullname'] ?? '',
      department: json['department'] ?? '',
      position: json['position'] ?? '',
      email: json['email'] ?? '',
      roleName: json['role'] is Map<String, dynamic>
          ? json['role']['name'] ?? ''
          : json['role'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fullname': fullname,
      'department': department,
      'position': position,
      'email': email,
      'roleName': roleName,
    };
  }
}
