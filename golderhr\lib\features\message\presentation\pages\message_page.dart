import 'package:flutter/material.dart';
import 'package:iconsax/iconsax.dart';

// --- BƯỚC 1: TẠO MODEL CHO TIN NHẮN ---
// Sử dụng một class riêng giúp code rõ ràng và an toàn hơn so với dùng Map.
class ChatMessage {
  final String text;
  final bool
  isUser; // `true` nếu là tin nhắn của người dùng hiện tại (bên phải)
  final String time;

  ChatMessage({required this.text, required this.isUser, required this.time});
}

// --- WIDGET CHÍNH ĐÃ ĐƯỢC NÂNG CẤP ---
class MessagePage extends StatefulWidget {
  const MessagePage({super.key});

  @override
  _MessagePageState createState() => _MessagePageState();
}

class _MessagePageState extends State<MessagePage> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final GlobalKey<AnimatedListState> _listKey = GlobalKey<AnimatedListState>();

  // Dùng List<ChatMessage> thay vì List<Map>
  final List<ChatMessage> _messages = [
    ChatMessage(
      text: 'Xin chào, tôi cần hỗ trợ về tài khoản!',
      isUser: false,
      time: '10:30 AM',
    ),
    ChatMessage(
      text: 'Chào bạn! Vấn đề cụ thể bạn đang gặp phải là gì ạ?',
      isUser: true,
      time: '10:32 AM',
    ),
    ChatMessage(
      text: 'Tôi quên mật khẩu, làm sao để lấy lại bây giờ?',
      isUser: false,
      time: '10:35 AM',
    ),
    ChatMessage(
      text:
          'Bạn vui lòng vào màn hình đăng nhập và chọn chức năng "Quên mật khẩu" nhé!',
      isUser: true,
      time: '10:36 AM',
    ),
  ];

  @override
  void initState() {
    super.initState();
    // Tự động cuộn xuống dưới cùng khi trang được mở
    WidgetsBinding.instance.addPostFrameCallback((_) => _scrollToBottom());
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // Hàm gửi tin nhắn, đã được nâng cấp để dùng AnimatedList
  void _sendMessage() {
    if (_messageController.text.trim().isNotEmpty) {
      final message = ChatMessage(
        text: _messageController.text,
        isUser:
            false, // Giả sử người dùng hiện tại luôn là người hỏi (isUser: false)
        time: TimeOfDay.now().format(context),
      );

      // Thêm tin nhắn vào danh sách và thực hiện animation
      _messages.add(message);
      _listKey.currentState?.insertItem(
        _messages.length - 1,
        duration: const Duration(milliseconds: 300),
      );
      _messageController.clear();

      // Đợi một chút để animation hoàn tất rồi cuộn xuống
      Future.delayed(
        const Duration(milliseconds: 300),
        () => _scrollToBottom(),
      );
    }
  }

  // Hàm tự động cuộn xuống tin nhắn mới nhất
  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50, // Màu nền trang nhã hơn
      appBar: _buildAppBar(),
      body: Column(
        children: [
          Expanded(
            // --- BƯỚC 2: SỬ DỤNG ANIMATED LIST ---
            // Giúp tin nhắn mới xuất hiện mượt mà hơn
            child: AnimatedList(
              key: _listKey,
              controller: _scrollController,
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 20.0,
              ),
              initialItemCount: _messages.length,
              itemBuilder: (context, index, animation) {
                return _buildMessageItem(_messages[index], animation);
              },
            ),
          ),
          _buildInputArea(),
        ],
      ),
    );
  }

  // --- WIDGET BUILDERS: TÁCH CÁC PHẦN UI RA RIÊNG CHO DỄ QUẢN LÝ ---

  // Xây dựng AppBar hiện đại
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 1,
      backgroundColor: Colors.white,
      foregroundColor: Colors.blue.shade800,
      leading: IconButton(
        icon: const Icon(Iconsax.arrow_left_2),
        onPressed: () => Navigator.pop(context),
      ),
      title: Row(
        children: [
          const CircleAvatar(
            backgroundImage: NetworkImage(
              'https://i.pravatar.cc/150?img=1',
            ), // Avatar mẫu
            radius: 20,
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Hỗ trợ viên',
                style: TextStyle(
                  color: Colors.black87,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                'Đang hoạt động',
                style: TextStyle(color: Colors.green.shade600, fontSize: 13),
              ),
            ],
          ),
        ],
      ),
      actions: [
        IconButton(
          icon: Icon(Iconsax.call, color: Colors.blue.shade800),
          onPressed: () {},
        ),
        IconButton(
          icon: Icon(Iconsax.more, color: Colors.blue.shade800),
          onPressed: () {},
        ),
      ],
    );
  }

  // Xây dựng item tin nhắn với animation
  Widget _buildMessageItem(ChatMessage message, Animation<double> animation) {
    // Căn chỉnh tin nhắn của người dùng qua phải, của người hỗ trợ qua trái
    final alignment = message.isUser
        ? MainAxisAlignment.end
        : MainAxisAlignment.start;

    // Widget bong bóng chat
    final bubble = Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.75,
      ),
      decoration: BoxDecoration(
        color: message.isUser ? Colors.blue.shade700 : Colors.white,
        // --- BƯỚC 3: BO GÓC THÔNG MINH ---
        // Bo tròn các góc một cách hợp lý để tạo hình dáng bong bóng chat
        borderRadius: BorderRadius.only(
          topLeft: const Radius.circular(20),
          topRight: const Radius.circular(20),
          bottomLeft: Radius.circular(message.isUser ? 20 : 0),
          bottomRight: Radius.circular(message.isUser ? 0 : 20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.07),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: message.isUser
            ? CrossAxisAlignment.end
            : CrossAxisAlignment.start,
        children: [
          Text(
            message.text,
            style: TextStyle(
              color: message.isUser ? Colors.white : Colors.black87,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 5),
          Text(
            message.time,
            style: TextStyle(
              color: message.isUser ? Colors.white70 : Colors.grey.shade500,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );

    return SizeTransition(
      sizeFactor: CurvedAnimation(parent: animation, curve: Curves.easeOut),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 6.0),
        child: Row(
          mainAxisAlignment: alignment,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            // Hiển thị Avatar cho người hỗ trợ
            if (!message.isUser) ...[
              const CircleAvatar(
                backgroundImage: NetworkImage(
                  'https://i.pravatar.cc/150?img=1',
                ), // Avatar mẫu
                radius: 16,
              ),
              const SizedBox(width: 8),
            ],
            Flexible(child: bubble), // Dùng Flexible để bubble không bị tràn
          ],
        ),
      ),
    );
  }

  // Xây dựng khu vực nhập liệu
  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // Nút đính kèm
            IconButton(
              icon: Icon(Iconsax.paperclip_2, color: Colors.grey.shade600),
              onPressed: () {
                /* Xử lý đính kèm file */
              },
            ),
            // Ô nhập liệu
            Expanded(
              child: TextField(
                controller: _messageController,
                onSubmitted: (_) => _sendMessage(),
                decoration: InputDecoration(
                  hintText: 'Nhập tin nhắn...',
                  border: InputBorder.none,
                  filled: false,
                  hintStyle: TextStyle(color: Colors.grey.shade500),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 10,
                  ),
                ),
                textCapitalization: TextCapitalization.sentences,
              ),
            ),
            // Nút gửi
            InkWell(
              onTap: _sendMessage,
              borderRadius: BorderRadius.circular(50),
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [Colors.blue.shade600, Colors.blue.shade800],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: const Icon(
                  Iconsax.send_1,
                  color: Colors.white,
                  size: 22,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
