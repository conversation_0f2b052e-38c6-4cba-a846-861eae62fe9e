// lib/features/language/presentation/cubit/language_cubit.dart
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LanguageCubit extends Cubit<String> {
  static const _languageKey = 'selected_language';

  final SharedPreferences sharedPreferences;

  LanguageCubit({required this.sharedPreferences}) : super('en');

  Future<void> loadSavedLanguage() async {
    final savedLang = sharedPreferences.getString(_languageKey);
    emit(savedLang ?? 'en');
  }

  void changeLanguage(String langCode) {
    emit(langCode);
    sharedPreferences.setString(_languageKey, langCode);
  }
}
