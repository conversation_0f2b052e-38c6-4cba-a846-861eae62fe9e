import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/widgets/responsive_layout.dart';
import 'package:golderhr/shared/theme/app_text_styles.dart';
import 'package:iconsax/iconsax.dart';
import '../../domain/entities/admin_user_entity.dart';

class AdminUserStatsWidget extends StatelessWidget {
  final UserStatistics statistics;

  const AdminUserStatsWidget({super.key, required this.statistics});

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final l10n = context.l10n;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with refresh button
        _buildHeader(context),

        SizedBox(height: responsive.defaultSpacing),

        // Overview Cards
        _buildSectionTitle(context, l10n.userOverview, Iconsax.people),
        Sized<PERSON>ox(height: responsive.defaultSpacing),
        _buildOverviewCards(context),

        SizedBox(height: responsive.defaultSpacing * 2),

        // Role Distribution
        _buildSectionTitle(context, l10n.roleDistribution, Iconsax.chart_21),
        SizedBox(height: responsive.defaultSpacing),
        _buildRoleDistribution(context),

        SizedBox(height: responsive.defaultSpacing * 2),

        // Department Analytics
        _buildSectionTitle(context, l10n.departmentBreakdown, Iconsax.building),
        SizedBox(height: responsive.defaultSpacing),
        _buildDepartmentAnalytics(context),

        SizedBox(height: responsive.defaultSpacing * 2),

        // User Activity
        _buildSectionTitle(context, l10n.userActivity, Iconsax.activity),
        SizedBox(height: responsive.defaultSpacing),
        _buildUserActivity(context),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    final responsive = context.responsive;
    final l10n = context.l10n;

    return Container(
      padding: responsive.padding(all: 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade600, Colors.blue.shade800],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(responsive.defaultRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: responsive.padding(all: 12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(responsive.defaultRadius),
            ),
            child: Icon(
              Iconsax.chart_21,
              color: Colors.white,
              size: responsive.adaptiveValue<double>(
                mobile: 24,
                tablet: 28,
                mobileLandscape: 26,
                tabletLandscape: 30,
              ),
            ),
          ),
          SizedBox(width: responsive.scaleWidth(16)),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  l10n.adminStatistics,
                  style: AppTextStyle.bold(
                    context,
                    size: responsive.adaptiveValue<double>(
                      mobile: 20,
                      tablet: 24,
                      mobileLandscape: 22,
                      tabletLandscape: 26,
                    ),
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: responsive.scaleHeight(4)),
                Text(
                  'Real-time user analytics and insights',
                  style: AppTextStyle.regular(
                    context,
                    size: responsive.adaptiveValue<double>(
                      mobile: 14,
                      tablet: 16,
                      mobileLandscape: 15,
                      tabletLandscape: 17,
                    ),
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              // TODO: Implement refresh functionality
            },
            icon: Icon(
              Iconsax.refresh,
              color: Colors.white,
              size: responsive.adaptiveValue<double>(
                mobile: 20,
                tablet: 24,
                mobileLandscape: 22,
                tabletLandscape: 26,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title, IconData icon) {
    final responsive = context.responsive;

    return Row(
      children: [
        Container(
          padding: responsive.padding(all: 8),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(responsive.scaleRadius(8)),
          ),
          child: Icon(
            icon,
            size: responsive.adaptiveValue<double>(
              mobile: 18,
              tablet: 20,
              mobileLandscape: 19,
              tabletLandscape: 22,
            ),
            color: Colors.blue.shade600,
          ),
        ),
        SizedBox(width: responsive.scaleWidth(12)),
        Text(
          title,
          style: AppTextStyle.bold(
            context,
            size: responsive.adaptiveValue<double>(
              mobile: 18,
              tablet: 20,
              mobileLandscape: 19,
              tabletLandscape: 22,
            ),
            color: Colors.grey.shade800,
          ),
        ),
      ],
    );
  }

  Widget _buildOverviewCards(BuildContext context) {
    final overview = statistics.overview;
    final responsive = context.responsive;
    final l10n = context.l10n;

    return ResponsiveLayout(
      mobile: GridView.count(
        crossAxisCount: 2,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisSpacing: responsive.scaleWidth(12),
        mainAxisSpacing: responsive.scaleHeight(12),
        childAspectRatio: 1.3,
        children: [
          _buildModernStatCard(
            context,
            l10n.totalUsers,
            overview.totalUsers.toString(),
            Iconsax.people,
            [Colors.blue.shade400, Colors.blue.shade600],
          ),
          _buildModernStatCard(
            context,
            l10n.activeUsers,
            overview.activeUsers.toString(),
            Iconsax.tick_circle,
            [Colors.green.shade400, Colors.green.shade600],
          ),
          _buildModernStatCard(
            context,
            l10n.disabledUsers,
            overview.disabledUsers.toString(),
            Iconsax.close_circle,
            [Colors.orange.shade400, Colors.orange.shade600],
          ),
          _buildModernStatCard(
            context,
            l10n.deletedUsers,
            overview.deletedUsers.toString(),
            Iconsax.trash,
            [Colors.red.shade400, Colors.red.shade600],
          ),
        ],
      ),
      tablet: GridView.count(
        crossAxisCount: 4,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisSpacing: responsive.scaleWidth(16),
        mainAxisSpacing: responsive.scaleHeight(16),
        childAspectRatio: 1.2,
        children: [
          _buildModernStatCard(
            context,
            l10n.totalUsers,
            overview.totalUsers.toString(),
            Iconsax.people,
            [Colors.blue.shade400, Colors.blue.shade600],
          ),
          _buildModernStatCard(
            context,
            l10n.activeUsers,
            overview.activeUsers.toString(),
            Iconsax.tick_circle,
            [Colors.green.shade400, Colors.green.shade600],
          ),
          _buildModernStatCard(
            context,
            l10n.disabledUsers,
            overview.disabledUsers.toString(),
            Iconsax.close_circle,
            [Colors.orange.shade400, Colors.orange.shade600],
          ),
          _buildModernStatCard(
            context,
            l10n.deletedUsers,
            overview.deletedUsers.toString(),
            Iconsax.trash,
            [Colors.red.shade400, Colors.red.shade600],
          ),
        ],
      ),
    );
  }

  Widget _buildModernStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    List<Color> gradientColors,
  ) {
    final responsive = context.responsive;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: gradientColors,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(responsive.defaultRadius),
        boxShadow: [
          BoxShadow(
            color: gradientColors.first.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(responsive.defaultRadius),
          onTap: () {
            // TODO: Add tap functionality for detailed view
          },
          child: Padding(
            padding: responsive.padding(all: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: responsive.padding(all: 8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(
                          responsive.scaleRadius(8),
                        ),
                      ),
                      child: Icon(
                        icon,
                        color: Colors.white,
                        size: responsive.adaptiveValue<double>(
                          mobile: 18,
                          tablet: 20,
                          mobileLandscape: 19,
                          tabletLandscape: 22,
                        ),
                      ),
                    ),
                    const Spacer(),
                    Text(
                      value,
                      style: AppTextStyle.bold(
                        context,
                        size: responsive.adaptiveValue<double>(
                          mobile: 20,
                          tablet: 24,
                          mobileLandscape: 22,
                          tabletLandscape: 26,
                        ),
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: responsive.scaleHeight(12)),
                Text(
                  title,
                  style: AppTextStyle.medium(
                    context,
                    size: responsive.adaptiveValue<double>(
                      mobile: 12,
                      tablet: 14,
                      mobileLandscape: 13,
                      tabletLandscape: 15,
                    ),
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRoleDistribution(BuildContext context) {
    final responsive = context.responsive;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(responsive.defaultRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: responsive.padding(all: 20),
        child: Column(
          children: statistics.roleDistribution.map((role) {
            final percentage = statistics.overview.totalUsers > 0
                ? (role.count / statistics.overview.totalUsers) * 100
                : 0.0;

            return Padding(
              padding: responsive.padding(vertical: 12),
              child: Column(
                children: [
                  Row(
                    children: [
                      Container(
                        width: responsive.scaleWidth(16),
                        height: responsive.scaleHeight(16),
                        decoration: BoxDecoration(
                          color: _getRoleColor(role.roleName),
                          shape: BoxShape.circle,
                        ),
                      ),
                      SizedBox(width: responsive.scaleWidth(12)),
                      Expanded(
                        child: Text(
                          role.displayRoleName,
                          style: AppTextStyle.medium(
                            context,
                            size: responsive.adaptiveValue<double>(
                              mobile: 14,
                              tablet: 16,
                              mobileLandscape: 15,
                              tabletLandscape: 17,
                            ),
                          ),
                        ),
                      ),
                      Text(
                        '${role.count}',
                        style: AppTextStyle.bold(
                          context,
                          size: responsive.adaptiveValue<double>(
                            mobile: 16,
                            tablet: 18,
                            mobileLandscape: 17,
                            tabletLandscape: 19,
                          ),
                          color: _getRoleColor(role.roleName),
                        ),
                      ),
                      SizedBox(width: responsive.scaleWidth(8)),
                      Text(
                        '(${percentage.toStringAsFixed(1)}%)',
                        style: AppTextStyle.regular(
                          context,
                          size: responsive.adaptiveValue<double>(
                            mobile: 12,
                            tablet: 14,
                            mobileLandscape: 13,
                            tabletLandscape: 15,
                          ),
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: responsive.scaleHeight(8)),
                  // Animated Progress Bar
                  TweenAnimationBuilder<double>(
                    duration: const Duration(milliseconds: 1000),
                    tween: Tween(begin: 0, end: percentage / 100),
                    builder: (context, value, child) {
                      return LinearProgressIndicator(
                        value: value,
                        backgroundColor: Colors.grey.shade200,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          _getRoleColor(role.roleName),
                        ),
                        minHeight: responsive.scaleHeight(6),
                      );
                    },
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildDepartmentAnalytics(BuildContext context) {
    final responsive = context.responsive;
    final l10n = context.l10n;

    // Mock department data - replace with real data
    final departments = [
      {'name': l10n.departmentIT, 'count': 25, 'color': Colors.blue},
      {'name': l10n.departmentHR, 'count': 8, 'color': Colors.purple},
      {'name': l10n.departmentFinance, 'count': 12, 'color': Colors.green},
      {'name': l10n.departmentMarketing, 'count': 15, 'color': Colors.orange},
    ];

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(responsive.defaultRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: responsive.padding(all: 20),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  Iconsax.building,
                  color: Colors.grey.shade600,
                  size: responsive.adaptiveValue<double>(
                    mobile: 20,
                    tablet: 22,
                    mobileLandscape: 21,
                    tabletLandscape: 24,
                  ),
                ),
                SizedBox(width: responsive.scaleWidth(8)),
                Text(
                  'Department Distribution',
                  style: AppTextStyle.bold(
                    context,
                    size: responsive.adaptiveValue<double>(
                      mobile: 16,
                      tablet: 18,
                      mobileLandscape: 17,
                      tabletLandscape: 19,
                    ),
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
            SizedBox(height: responsive.defaultSpacing),
            ...departments.map(
              (dept) => Padding(
                padding: responsive.padding(vertical: 8),
                child: Row(
                  children: [
                    Container(
                      width: responsive.scaleWidth(12),
                      height: responsive.scaleHeight(12),
                      decoration: BoxDecoration(
                        color: dept['color'] as Color,
                        shape: BoxShape.circle,
                      ),
                    ),
                    SizedBox(width: responsive.scaleWidth(12)),
                    Expanded(
                      child: Text(
                        dept['name'] as String,
                        style: AppTextStyle.regular(
                          context,
                          size: responsive.adaptiveValue<double>(
                            mobile: 14,
                            tablet: 16,
                            mobileLandscape: 15,
                            tabletLandscape: 17,
                          ),
                        ),
                      ),
                    ),
                    Text(
                      '${dept['count']}',
                      style: AppTextStyle.bold(
                        context,
                        size: responsive.adaptiveValue<double>(
                          mobile: 14,
                          tablet: 16,
                          mobileLandscape: 15,
                          tabletLandscape: 17,
                        ),
                        color: dept['color'] as Color,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserActivity(BuildContext context) {
    final responsive = context.responsive;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(responsive.defaultRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: responsive.padding(all: 20),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  Iconsax.activity,
                  color: Colors.grey.shade600,
                  size: responsive.adaptiveValue<double>(
                    mobile: 20,
                    tablet: 22,
                    mobileLandscape: 21,
                    tabletLandscape: 24,
                  ),
                ),
                SizedBox(width: responsive.scaleWidth(8)),
                Text(
                  'Recent Activity',
                  style: AppTextStyle.bold(
                    context,
                    size: responsive.adaptiveValue<double>(
                      mobile: 16,
                      tablet: 18,
                      mobileLandscape: 17,
                      tabletLandscape: 19,
                    ),
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
            SizedBox(height: responsive.defaultSpacing),
            _buildActivityItem(
              context,
              'New user registrations',
              '+5 today',
              Iconsax.user_add,
              Colors.green,
            ),
            _buildActivityItem(
              context,
              'User logins',
              '42 today',
              Iconsax.login,
              Colors.blue,
            ),
            _buildActivityItem(
              context,
              'Account updates',
              '8 today',
              Iconsax.edit,
              Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    final responsive = context.responsive;

    return Padding(
      padding: responsive.padding(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: responsive.padding(all: 8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(responsive.scaleRadius(8)),
            ),
            child: Icon(
              icon,
              color: color,
              size: responsive.adaptiveValue<double>(
                mobile: 16,
                tablet: 18,
                mobileLandscape: 17,
                tabletLandscape: 19,
              ),
            ),
          ),
          SizedBox(width: responsive.scaleWidth(12)),
          Expanded(
            child: Text(
              title,
              style: AppTextStyle.regular(
                context,
                size: responsive.adaptiveValue<double>(
                  mobile: 14,
                  tablet: 16,
                  mobileLandscape: 15,
                  tabletLandscape: 17,
                ),
              ),
            ),
          ),
          Text(
            value,
            style: AppTextStyle.bold(
              context,
              size: responsive.adaptiveValue<double>(
                mobile: 14,
                tablet: 16,
                mobileLandscape: 15,
                tabletLandscape: 17,
              ),
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Color _getRoleColor(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return Colors.red;
      case 'hr':
        return Colors.purple;
      case 'manager':
        return Colors.blue;
      case 'user':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }
}
