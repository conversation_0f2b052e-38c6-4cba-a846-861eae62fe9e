import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/core/routes/app_routes.dart';
import 'package:golderhr/features/auth/domain/entities/user_entity.dart';
import 'package:golderhr/shared/widgets/action_button.dart';
import 'package:golderhr/shared/widgets/action_item_model.dart';
import 'package:golderhr/shared/widgets/responsive_spacer.dart';
import 'package:iconsax/iconsax.dart';

import '../../../cubit/user_cubit.dart';

class MorePage extends StatelessWidget {
  const MorePage({super.key});

  static List<ActionItem> getAllActions(
    BuildContext context,
    UserEntity? user,
  ) {
    final l10n = context.l10n;
    final isAdmin = user?.role.toLowerCase() == 'admin';
    final actions = [
      ActionItem(
        label: l10n.moreOverTime,
        icon: Iconsax.clock,
        color: Colors.purple,
        route: AppRoutes.overtimeRequest,
        category: ActionCategory.hrm,
      ),
      ActionItem(
        label: l10n.moreAttendance,
        icon: Icons.check_circle_outline,
        color: Colors.green,
        route: AppRoutes.attendance,
        category: ActionCategory.hrm,
      ),
      ActionItem(
        label: l10n.moreLeave,
        icon: Icons.holiday_village_outlined,
        color: Colors.blue,
        route: AppRoutes.leave,
        category: ActionCategory.hrm,
      ),
      ActionItem(
        label: l10n.moreTeam,
        icon: Iconsax.people,
        color: Colors.teal,
        route: '/team',
        category: ActionCategory.hrm,
      ),
      ActionItem(
        label: l10n.moreCalendar,
        icon: Iconsax.calendar_1,
        color: Colors.red,
        route: '/calendar',
        category: ActionCategory.hrm,
      ),
      ActionItem(
        label: l10n.moreRecruitment,
        icon: Iconsax.user_add,
        color: Colors.cyan,
        route: '/recruitment',
        category: ActionCategory.crm,
      ),
      ActionItem(
        label: l10n.moreTraining,
        icon: Iconsax.teacher,
        color: Colors.indigo,
        route: '/training',
        category: ActionCategory.crm,
      ),
      ActionItem(
        label: l10n.moreSupport,
        icon: Icons.support_agent,
        color: Colors.pink,
        route: '/support',
        category: ActionCategory.utility,
      ),
      ActionItem(
        label: l10n.moreSetting,
        icon: Iconsax.setting_2,
        color: Colors.grey,
        route: '/setting',
        category: ActionCategory.utility,
      ),
    ];

    if (isAdmin) {
      actions.addAll([
        ActionItem(
          label: 'Overtime Management',
          icon: Icons.admin_panel_settings,
          color: Colors.deepPurple,
          route: AppRoutes.overtimeAdmin,
          category: ActionCategory.hrm,
        ),
        ActionItem(
          label: 'Leave Management',
          icon: Icons.event_available_rounded,
          color: Colors.green,
          route: AppRoutes.leaveAdmin,
          category: ActionCategory.hrm,
        ),
        ActionItem(
          label: l10n.settingAddImage,
          icon: Icons.add_a_photo,
          color: Colors.greenAccent,
          route: AppRoutes.uploadEmployeeFace,
          category: ActionCategory.hrm,
        ),
        ActionItem(
          label: 'User Management',
          icon: Icons.manage_accounts,
          color: Colors.indigo,
          route: AppRoutes.adminUserManagement,
          category: ActionCategory.hrm,
        ),
        ActionItem(
          label: 'Role Management',
          icon: Icons.security,
          color: Colors.deepOrange,
          route: AppRoutes.roleManagement,
          category: ActionCategory.hrm,
        ),
        ActionItem(
          label: 'Department Management',
          icon: Icons.business,
          color: Colors.teal,
          route: AppRoutes.departmentManagement,
          category: ActionCategory.hrm,
        ),
      ]);
    }

    return actions;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return BlocBuilder<UserCubit, UserEntity?>(
      builder: (context, user) {
        // Pass user to getAllActions
        final allActions = getAllActions(context, user);
        final hrmActions = allActions
            .where((a) => a.category == ActionCategory.hrm)
            .toList();
        final crmActions = allActions
            .where((a) => a.category == ActionCategory.crm)
            .toList();
        final utilityActions = allActions
            .where((a) => a.category == ActionCategory.utility)
            .toList();

        return Scaffold(
          appBar: AppBar(
            title: Text(
              l10n.moreAllFeatures,
              style: context.lightTheme.textTheme.displaySmall!.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            centerTitle: true,
          ),
          body: SingleChildScrollView(
            padding: context.responsive.padding(all: 16.0),
            child: Column(
              children: [
                if (hrmActions.isNotEmpty) ...[
                  _ActionCategorySection(
                    title: l10n.moreHRM,
                    actions: hrmActions,
                  ),
                  ResponsiveSpacer(mobileSize: 24, tabletSize: 26),
                ],
                if (crmActions.isNotEmpty) ...[
                  _ActionCategorySection(
                    title: l10n.moreCRM,
                    actions: crmActions,
                  ),
                  ResponsiveSpacer(mobileSize: 24, tabletSize: 26),
                ],
                if (utilityActions.isNotEmpty) ...[
                  _ActionCategorySection(
                    title: l10n.moreUtility,
                    actions: utilityActions,
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}

class _ActionCategorySection extends StatelessWidget {
  final String title;
  final List<ActionItem> actions;

  const _ActionCategorySection({required this.title, required this.actions});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: context.responsive.padding(left: 4.0, bottom: 8.0),
          child: Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.grey.shade300, width: 1.5),
          ),
          child: Padding(
            padding: context.responsive.padding(all: 12.0),
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              itemCount: actions.length,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                return ActionButton(item: actions[index]);
              },
            ),
          ),
        ),
      ],
    );
  }
}
