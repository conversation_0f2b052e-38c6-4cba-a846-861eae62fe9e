import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import '../../../../../core/logger/app_logger.dart';
import '../../models/user_model.dart';

abstract class AuthLocalDataSource {
  Future<void> cacheToken(String token);

  Future<String?> getToken();

  Future<void> clearCache();

  // Ví dụ lưu cả user data
  Future<void> cacheUser(UserModel user);

  // Future<UserModel?> getUser();
  UserModel? getUser();
}

class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final SharedPreferences sharedPreferences;

  AuthLocalDataSourceImpl({required this.sharedPreferences});

  static const AUTH_TOKEN = 'AUTH_TOKEN';
  static const CACHED_USER = 'CACHED_USER';

  @override
  Future<void> cacheToken(String token) async {
    await sharedPreferences.setString(AUTH_TOKEN, token);
  }

  @override
  Future<String?> getToken() async {
    return sharedPreferences.getString(AUTH_TOKEN);
  }

  @override
  Future<void> cacheUser(UserModel user) async {
    await sharedPreferences.setString(CACHED_USER, json.encode(user.toJson()));
  }

  @override
  Future<void> clearCache() async {
    await sharedPreferences.remove(AUTH_TOKEN);
    await sharedPreferences.remove(CACHED_USER);
  }

  // @override
  // Future<UserModel?> getUser() async {
  //   final jsonString = sharedPreferences.getString(CACHED_USER);
  //   if (jsonString != null) {
  //     return UserModel.fromJson(json.decode(jsonString));
  //   }
  //   return null;
  // }
  @override
  UserModel? getUser() {
    final jsonString = sharedPreferences.getString(CACHED_USER);
    AppLogger.info('Retrieved cached user: $jsonString');
    if (jsonString != null) {
      return UserModel.fromJson(json.decode(jsonString));
    }
    return null;
  }
}
