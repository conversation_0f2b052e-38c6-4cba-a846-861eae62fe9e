import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../../l10n/app_localizations.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/overtime_request_entity.dart';
import '../cubit/overtime_cubit.dart';
import '../cubit/overtime_state.dart';
import 'overtime_detail_page.dart';

class OvertimeHistoryPage extends StatefulWidget {
  const OvertimeHistoryPage({super.key});

  @override
  State<OvertimeHistoryPage> createState() => _OvertimeHistoryPageState();
}

class _OvertimeHistoryPageState extends State<OvertimeHistoryPage>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  final ScrollController _scrollController = ScrollController();
  OvertimeStatus? _selectedFilter;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    // Start animations
    _fadeController.forward();
    _slideController.forward();

    // Load history data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<OvertimeCubit>().loadInitialData();
    });

    // Setup scroll listener for pagination
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      context.read<OvertimeCubit>().loadMoreHistory();
    }
  }

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final theme = Theme.of(context);
    final l10n = context.l10n;

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          l10n.overtimeHistory,
          style: theme.textTheme.titleLarge?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: Column(
            children: [
              // Filter Section
              _buildFilterSection(responsive, theme, l10n),

              // History List
              Expanded(
                child: BlocBuilder<OvertimeCubit, OvertimeState>(
                  builder: (context, state) {
                    if (state.isLoadingHistory && state.history.isEmpty) {
                      return _buildLoadingState(responsive, l10n);
                    }

                    if (state.history.isEmpty) {
                      return _buildEmptyState(responsive, theme, l10n);
                    }

                    return _buildHistoryList(responsive, theme, l10n, state);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterSection(
    Responsive responsive,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return Container(
      margin: responsive.padding(all: 16),
      padding: responsive.padding(all: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryBlue.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.filter_list_rounded,
                color: AppColors.primaryBlue,
                size: 20,
              ),
              SizedBox(width: responsive.widthPercentage(2)),
              Text(
                l10n.filterByStatus,
                style: theme.textTheme.titleSmall?.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: responsive.heightPercentage(1.5)),
          _buildStatusDropdown(responsive, theme, l10n),
        ],
      ),
    );
  }

  Widget _buildStatusDropdown(
    Responsive responsive,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return Container(
      padding: responsive.padding(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(
          color: AppColors.primaryBlue.withValues(alpha: 0.3),
          width: 1.5,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryBlue.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: DropdownButton<OvertimeStatus?>(
        value: _selectedFilter,
        hint: Row(
          children: [
            Icon(
              Icons.all_inclusive_rounded,
              size: 16,
              color: AppColors.textSecondary,
            ),
            const SizedBox(width: 8),
            Text(
              l10n.all,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        items: [
          DropdownMenuItem<OvertimeStatus?>(
            value: null,
            child: Row(
              children: [
                Icon(
                  Icons.all_inclusive_rounded,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 8),
                Text(l10n.all),
              ],
            ),
          ),
          DropdownMenuItem<OvertimeStatus?>(
            value: OvertimeStatus.pending,
            child: Row(
              children: [
                Icon(Icons.schedule_rounded, size: 16, color: Colors.orange),
                const SizedBox(width: 8),
                Text(l10n.pending),
              ],
            ),
          ),
          DropdownMenuItem<OvertimeStatus?>(
            value: OvertimeStatus.approved,
            child: Row(
              children: [
                Icon(Icons.check_circle_rounded, size: 16, color: Colors.green),
                const SizedBox(width: 8),
                Text(l10n.approved),
              ],
            ),
          ),
          DropdownMenuItem<OvertimeStatus?>(
            value: OvertimeStatus.rejected,
            child: Row(
              children: [
                Icon(Icons.cancel_rounded, size: 16, color: Colors.red),
                const SizedBox(width: 8),
                Text(l10n.rejected),
              ],
            ),
          ),
        ],
        onChanged: (value) {
          setState(() {
            _selectedFilter = value;
          });
          context.read<OvertimeCubit>().filterHistory(value);
        },
        isExpanded: true,
        underline: const SizedBox(),
        icon: Icon(
          Icons.keyboard_arrow_down_rounded,
          color: AppColors.primaryBlue,
        ),
        style: theme.textTheme.bodyMedium?.copyWith(
          color: AppColors.textPrimary,
          fontWeight: FontWeight.w600,
        ),
        dropdownColor: Colors.white,
      ),
    );
  }

  Widget _buildHistoryList(
    Responsive responsive,
    ThemeData theme,
    AppLocalizations l10n,
    OvertimeState state,
  ) {
    final filteredHistory = _selectedFilter == null
        ? state.history
        : state.history.where((r) => r.status == _selectedFilter).toList();

    return RefreshIndicator(
      onRefresh: () async {
        context.read<OvertimeCubit>().loadInitialData();
      },
      color: AppColors.primaryBlue,
      child: ListView.builder(
        controller: _scrollController,
        padding: responsive.padding(all: 16),
        itemCount: filteredHistory.length + (state.isLoadingHistory ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= filteredHistory.length) {
            return _buildLoadingMoreIndicator(responsive);
          }

          final request = filteredHistory[index];
          return _buildHistoryItem(request, responsive, theme, l10n, index);
        },
      ),
    );
  }

  Widget _buildHistoryItem(
    OvertimeRequestEntity request,
    Responsive responsive,
    ThemeData theme,
    AppLocalizations l10n,
    int index,
  ) {
    final statusColor = _getStatusColor(request.status);
    final statusText = _getStatusText(request.status, l10n);

    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 300 + (index * 100)),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              margin: responsive.padding(bottom: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: statusColor.withValues(alpha: 0.08),
                    blurRadius: 20,
                    offset: const Offset(0, 4),
                    spreadRadius: 0,
                  ),
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color: statusColor.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () => _navigateToDetail(request),
                  borderRadius: BorderRadius.circular(16),
                  child: Padding(
                    padding: responsive.padding(all: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: statusColor.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.schedule_rounded,
                                color: statusColor,
                                size: 20,
                              ),
                            ),
                            SizedBox(width: responsive.widthPercentage(3)),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '${request.date.day}/${request.date.month}/${request.date.year}',
                                    style: theme.textTheme.titleSmall?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.textPrimary,
                                    ),
                                  ),
                                  SizedBox(
                                    height: responsive.heightPercentage(0.5),
                                  ),
                                  Text(
                                    '${_formatTime(request.startTime)} - ${_formatTime(request.endTime)}',
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: statusColor.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: statusColor.withValues(alpha: 0.3),
                                      width: 1,
                                    ),
                                  ),
                                  child: Text(
                                    statusText,
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: statusColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: responsive.fontSize(10),
                                    ),
                                  ),
                                ),
                                SizedBox(
                                  height: responsive.heightPercentage(0.5),
                                ),
                                Text(
                                  '${request.hours.toStringAsFixed(1)}h',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: AppColors.primaryBlue,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(width: responsive.widthPercentage(2)),
                            Icon(
                              Icons.arrow_forward_ios_rounded,
                              size: 14,
                              color: AppColors.textSecondary,
                            ),
                          ],
                        ),
                        if (request.reason.isNotEmpty) ...[
                          SizedBox(height: responsive.heightPercentage(1)),
                          Container(
                            padding: responsive.padding(all: 12),
                            decoration: BoxDecoration(
                              color: AppColors.background,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.description_outlined,
                                  size: 16,
                                  color: AppColors.textSecondary,
                                ),
                                SizedBox(width: responsive.widthPercentage(2)),
                                Expanded(
                                  child: Text(
                                    request.reason,
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: AppColors.textSecondary,
                                      fontStyle: FontStyle.italic,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingMoreIndicator(Responsive responsive) {
    return Container(
      padding: responsive.padding(all: 16),
      child: Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryBlue),
        ),
      ),
    );
  }

  Widget _buildLoadingState(Responsive responsive, AppLocalizations l10n) {
    return Container(
      padding: responsive.padding(all: 40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 1000),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.scale(
                scale: 0.8 + (0.2 * value),
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppColors.primaryBlue.withValues(alpha: value),
                  ),
                ),
              );
            },
          ),
          SizedBox(height: responsive.heightPercentage(2)),
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 800),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Opacity(
                opacity: value,
                child: Text(
                  l10n.loadingOvertimeHistory,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(
    Responsive responsive,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1200),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              padding: responsive.padding(all: 40),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TweenAnimationBuilder<double>(
                    duration: const Duration(milliseconds: 2000),
                    tween: Tween(begin: 0.0, end: 1.0),
                    builder: (context, iconValue, child) {
                      return Transform.scale(
                        scale: 0.8 + (0.2 * iconValue),
                        child: Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: AppColors.primaryBlue.withValues(alpha: 0.1),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.schedule_rounded,
                            size: responsive.scaleWidth(60),
                            color: AppColors.primaryBlue,
                          ),
                        ),
                      );
                    },
                  ),
                  SizedBox(height: responsive.heightPercentage(3)),
                  Text(
                    l10n.noOvertimeRequests,
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: responsive.heightPercentage(1)),
                  Text(
                    l10n.submitYourFirstOvertimeRequest,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _navigateToDetail(OvertimeRequestEntity request) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OvertimeDetailPage(request: request),
      ),
    );
  }

  Color _getStatusColor(OvertimeStatus status) {
    switch (status) {
      case OvertimeStatus.approved:
        return Colors.green;
      case OvertimeStatus.rejected:
        return Colors.red;
      case OvertimeStatus.pending:
        return Colors.orange;
    }
  }

  String _getStatusText(OvertimeStatus status, AppLocalizations l10n) {
    switch (status) {
      case OvertimeStatus.approved:
        return l10n.approved;
      case OvertimeStatus.rejected:
        return l10n.rejected;
      case OvertimeStatus.pending:
        return l10n.pending;
    }
  }

  String _formatTime(DateTime time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
}
