import 'package:flutter/cupertino.dart';

class FadeSlideAnimation extends StatelessWidget {
  final Widget child;
  final Animation<double> fadeAnimation; //  adjust opacity
  final Animation<Offset> slideAnimation; // adjust coordinate
  const FadeSlideAnimation({
    super.key,
    required this.child,
    required this.fadeAnimation,
    required this.slideAnimation,
  });

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: fadeAnimation,
      child: SlideTransition(position: slideAnimation, child: child),
    );
  }
}
