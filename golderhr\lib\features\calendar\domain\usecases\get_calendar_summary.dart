import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/calendar_summary.dart';
import '../repositories/calendar_repository.dart';

/// Use case để lấy calendar summary
class GetCalendarSummary implements UseCase<CalendarSummary, NoParams> {
  final CalendarRepository repository;

  GetCalendarSummary(this.repository);

  @override
  Future<Either<Failure, CalendarSummary>> call(NoParams params) async {
    return await repository.getCalendarSummary();
  }
}

/// Use case để lấy monthly summary
class GetMonthlySummary
    implements UseCase<CalendarSummary, GetMonthlySummaryParams> {
  final CalendarRepository repository;

  GetMonthlySummary(this.repository);

  @override
  Future<Either<Failure, CalendarSummary>> call(
    GetMonthlySummaryParams params,
  ) async {
    return await repository.getMonthlySummary(params.year, params.month);
  }
}

/// Parameters cho GetMonthlySummary
class GetMonthlySummaryParams extends Equatable {
  final int year;
  final int month;

  const GetMonthlySummaryParams({required this.year, required this.month});

  @override
  List<Object> get props => [year, month];
}
