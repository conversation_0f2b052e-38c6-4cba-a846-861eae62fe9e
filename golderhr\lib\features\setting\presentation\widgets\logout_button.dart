import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

class LogoutButton extends StatelessWidget {
  final VoidCallback onPressed;
  final String title;

  const LogoutButton({super.key, required this.onPressed, required this.title});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: theme.colorScheme.surface,
          foregroundColor: Colors.red,
          elevation: 0,
          side: BorderSide(color: Colors.black.withAlpha((255 * 0.3).round())),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.symmetric(vertical: 14),
        ),
        icon: Icon(Icons.logout, size: context.responsive.fontSize(20)),
        label: Text(title, style: theme.textTheme.headlineMedium),
      ),
    );
  }
}
