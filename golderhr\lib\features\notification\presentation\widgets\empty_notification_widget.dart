import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import '../../../../shared/widgets/responsive_spacer.dart';

class EmptyNotificationWidget extends StatelessWidget {
  final String? title;
  final String? message;
  final String assetPath;

  const EmptyNotificationWidget({
    super.key,
    this.title,
    this.message,
    this.assetPath =
        'assets/images/no_notification.svg', // <PERSON><PERSON> cấp một giá trị mặc định
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: context.responsive.padding(horizontal: 32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              assetPath, // Dùng assetPath được truyền vào
              width: context.responsive.adaptiveValue(
                mobile: 50,
                tablet: 60,
                mobileLandscape: 50,
                tabletLandscape: 60,
              ),

              placeholderBuilder: (context) => Icon(
                Icons.notifications_off_outlined,
                size: context.responsive.fontSize(80),
                color: Colors.grey,
              ),
            ),
            ResponsiveSpacer(
              mobileSize: 24,
              tabletSize: 26,
              mobileLandscapeSize: 24,
              tabletLandscapeSize: 26,
            ),
            Text(
              // Sử dụng title truyền vào hoặc giá trị mặc định
              title ?? context.l10n.notificationNoNewUpdates,
              style: context.lightTheme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            ResponsiveSpacer(
              mobileSize: 8,
              tabletSize: 10,
              mobileLandscapeSize: 8,
              tabletLandscapeSize: 10,
            ),
            Text(
              // Sử dụng message truyền vào hoặc giá trị mặc định
              message ?? context.l10n.notificationPlaceholder,
              style: context.lightTheme.textTheme.bodyLarge?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
