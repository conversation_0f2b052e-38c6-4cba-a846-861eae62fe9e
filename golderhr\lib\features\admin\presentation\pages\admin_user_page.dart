import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/widgets/responsive_layout.dart';
import 'package:golderhr/shared/theme/app_text_styles.dart';

import 'package:iconsax/iconsax.dart';
import '../../../../injection_container.dart' as di;
import '../cubit/admin_user_cubit.dart';
import '../widgets/admin_user_filter_widget.dart';
import '../widgets/admin_user_data_grid.dart';
import '../widgets/admin_user_stats_widget.dart';

import '../widgets/edit_user_dialog.dart';
import '../widgets/create_user_dialog.dart';
import '../../../role/presentation/cubit/role_cubit.dart';
import '../../../department/presentation/cubit/department_cubit.dart';
import '../../domain/entities/admin_user_entity.dart';
import '../../../../shared/widgets/show_custom_snackBar.dart';

class AdminUserPage extends StatelessWidget {
  const AdminUserPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<AdminUserCubit>()
        ..loadUsers()
        ..loadStatistics(),
      child: const AdminUserView(),
    );
  }
}

class AdminUserView extends StatefulWidget {
  const AdminUserView({super.key});

  @override
  State<AdminUserView> createState() => _AdminUserViewState();
}

class _AdminUserViewState extends State<AdminUserView>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _scrollController = ScrollController();

    // Setup infinite scroll
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      final cubit = context.read<AdminUserCubit>();
      final state = cubit.state;

      if (state.canLoadMore && !state.isLoadingMore) {
        cubit.loadUsers(page: state.currentPage + 1, loadMore: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.lightTheme;
    final responsive = context.responsive;

    return BlocListener<AdminUserCubit, AdminUserState>(
      listener: (context, state) {
        if (state.hasError) {
          showTopSnackBar(
            context,
            title: context.l10n.error,
            message: state.error!,
            isError: true,
          );
        } else if (state.hasSuccess) {
          showTopSnackBar(
            context,
            title: context.l10n.success,
            message: state.successMessage!,
            isError: false,
          );

          context.read<AdminUserCubit>().clearSuccess();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            'User Management',
            style: AppTextStyle.bold(context, size: 20),
          ),
          centerTitle: responsive.isMobile,
          elevation: 0,
          backgroundColor: theme.scaffoldBackgroundColor,
          actions: [
            // Reload Button
            IconButton(
              onPressed: () => context.read<AdminUserCubit>().loadUsers(),
              icon: Icon(
                Iconsax.refresh,
                size: responsive.adaptiveValue<double>(
                  mobile: 20,
                  tablet: 24,
                  mobileLandscape: 22,
                  tabletLandscape: 26,
                ),
              ),
              tooltip: 'Reload Data', // l10n.refreshUsers,
            ),
            BlocBuilder<AdminUserCubit, AdminUserState>(
              builder: (context, state) {
                if (state.hasSelection) {
                  return Row(
                    children: [
                      IconButton(
                        onPressed: () => _showBulkActionsDialog(context),
                        icon: Icon(
                          Icons.more_vert,
                          size: responsive.adaptiveValue<double>(
                            mobile: 20,
                            tablet: 24,
                            mobileLandscape: 22,
                            tabletLandscape: 26,
                          ),
                        ),
                        tooltip: 'Bulk Actions', // l10n.bulkActions,
                      ),
                      IconButton(
                        onPressed: () =>
                            context.read<AdminUserCubit>().clearSelection(),
                        icon: Icon(
                          Icons.clear,
                          size: responsive.adaptiveValue<double>(
                            mobile: 20,
                            tablet: 24,
                            mobileLandscape: 22,
                            tabletLandscape: 26,
                          ),
                        ),
                        tooltip: 'Clear Selection', // l10n.clearSelection,
                      ),
                    ],
                  );
                }
                return IconButton(
                  onPressed: () => _showFilterDialog(context),
                  icon: Icon(
                    Iconsax.filter,
                    size: responsive.adaptiveValue<double>(
                      mobile: 20,
                      tablet: 24,
                      mobileLandscape: 22,
                      tabletLandscape: 26,
                    ),
                  ),
                  tooltip: 'Filter & Sort', // l10n.filterAndSort,
                );
              },
            ),
          ],
          bottom: TabBar(
            controller: _tabController,
            labelStyle: AppTextStyle.medium(context, size: 14),
            unselectedLabelStyle: AppTextStyle.regular(context, size: 14),
            tabs: [
              Tab(
                icon: Icon(
                  Icons.people,
                  size: responsive.adaptiveValue<double>(
                    mobile: 20,
                    tablet: 22,
                    mobileLandscape: 21,
                    tabletLandscape: 24,
                  ),
                ),
                text: 'Users', // l10n.adminUsers,
              ),
              Tab(
                icon: Icon(
                  Icons.analytics,
                  size: responsive.adaptiveValue<double>(
                    mobile: 20,
                    tablet: 22,
                    mobileLandscape: 21,
                    tabletLandscape: 24,
                  ),
                ),
                text: 'Statistics', // l10n.adminStatistics,
              ),
              Tab(
                icon: Icon(
                  Icons.settings,
                  size: responsive.adaptiveValue<double>(
                    mobile: 20,
                    tablet: 22,
                    mobileLandscape: 21,
                    tabletLandscape: 24,
                  ),
                ),
                text: 'Settings', // l10n.adminSettings,
              ),
            ],
          ),
        ),
        body: BlocBuilder<AdminUserCubit, AdminUserState>(
          builder: (context, state) {
            return TabBarView(
              controller: _tabController,
              children: [
                // Users Tab
                _buildUsersTab(context, state),

                // Statistics Tab
                _buildStatisticsTab(context, state),

                // Settings Tab
                _buildSettingsTab(context, state),
              ],
            );
          },
        ),
        floatingActionButton: responsive.isMobile
            ? FloatingActionButton(
                onPressed: () => _showCreateUserDialog(context),
                backgroundColor: Colors.green.shade600,
                foregroundColor: Colors.white,
                child: Icon(
                  Icons.person_add,
                  size: responsive.adaptiveValue<double>(
                    mobile: 24,
                    tablet: 28,
                    mobileLandscape: 26,
                    tabletLandscape: 30,
                  ),
                ),
              )
            : FloatingActionButton.extended(
                onPressed: () => _showCreateUserDialog(context),
                icon: Icon(
                  Icons.person_add,
                  size: responsive.adaptiveValue<double>(
                    mobile: 20,
                    tablet: 24,
                    mobileLandscape: 22,
                    tabletLandscape: 26,
                  ),
                ),
                label: Text(
                  'Add User', // l10n.addUser,
                  style: AppTextStyle.medium(
                    context,
                    size: 14,
                    color: Colors.white,
                  ),
                ),
                backgroundColor: Colors.green.shade600,
                foregroundColor: Colors.white,
              ),
      ),
    );
  }

  Widget _buildUsersTab(BuildContext context, AdminUserState state) {
    final responsive = context.responsive;

    if (state.isLoading && !state.hasUsers) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        // Filter Section
        const AdminUserFilterWidget(),

        // Selection Info
        if (state.hasSelection)
          Container(
            width: double.infinity,
            padding: responsive.responsivePadding,
            color: Colors.blue.shade50,
            child: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: Colors.blue.shade700,
                  size: responsive.adaptiveValue<double>(
                    mobile: 20,
                    tablet: 22,
                    mobileLandscape: 21,
                    tabletLandscape: 24,
                  ),
                ),
                SizedBox(width: responsive.scaleWidth(8)),
                Text(
                  '${state.selectedUserIds.length} users selected', // l10n.usersSelected(state.selectedUserIds.length),
                  style: AppTextStyle.medium(
                    context,
                    size: 14,
                    color: Colors.blue.shade700,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () =>
                      context.read<AdminUserCubit>().selectAllUsers(),
                  child: Text(
                    state.isAllSelected
                        ? 'Deselect All'
                        : 'Select All', // state.isAllSelected ? l10n.deselectAll : l10n.selectAll,
                    style: AppTextStyle.medium(context, size: 14),
                  ),
                ),
              ],
            ),
          ),

        // Users List
        Expanded(
          child: state.hasUsers
              ? Padding(
                  padding: responsive.responsivePadding,
                  child: AdminUserDataGrid(
                    users: state.users,
                    isLoading: state.isLoading,
                    selectedUserIds: state.selectedUserIds,
                    onUserTap: (user) => _showUserDetailsDialog(context, user),
                    onUserSelect: (userId) => context
                        .read<AdminUserCubit>()
                        .toggleUserSelection(userId),
                    onUserAction: (action, user) =>
                        _handleUserAction(context, action, user),
                  ),
                )
              : _buildEmptyState(context),
        ),
      ],
    );
  }

  Widget _buildStatisticsTab(BuildContext context, AdminUserState state) {
    final responsive = context.responsive;

    if (state.isLoadingStats) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.statistics == null) {
      return Center(
        child: Text(
          'No statistics available', // l10n.noStatisticsAvailable,
          style: AppTextStyle.regular(
            context,
            size: 16,
            color: Colors.grey[600],
          ),
        ),
      );
    }

    return SingleChildScrollView(
      padding: responsive.responsivePadding,
      child: AdminUserStatsWidget(statistics: state.statistics!),
    );
  }

  Widget _buildSettingsTab(BuildContext context, AdminUserState state) {
    return Center(
      child: Text(
        'Settings coming soon...', // l10n.settingsComingSoon,
        style: AppTextStyle.regular(context, size: 16, color: Colors.grey[600]),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final responsive = context.responsive;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: responsive.adaptiveValue<double>(
              mobile: 64,
              tablet: 80,
              mobileLandscape: 72,
              tabletLandscape: 96,
            ),
            color: Colors.grey[400],
          ),
          SizedBox(height: responsive.scaleHeight(16)),
          Text(
            'No users found', // l10n.noUsersFound,
            style: AppTextStyle.medium(
              context,
              size: 18,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: responsive.scaleHeight(8)),
          Text(
            'Try adjusting your filters or create a new user', // l10n.tryAdjustingFilters,
            style: AppTextStyle.regular(
              context,
              size: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    final cubit = context.read<AdminUserCubit>();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => BlocProvider.value(
        value: cubit,
        child: const AdminUserFilterWidget(isDialog: true),
      ),
    );
  }

  void _showBulkActionsDialog(BuildContext context) {
    final cubit = context.read<AdminUserCubit>();
    final selectedUsers = cubit.state.selectedUsers;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Bulk Actions (${selectedUsers.length} users)'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.delete_outline, color: Colors.red),
              title: const Text('Delete Selected'),
              onTap: () {
                Navigator.pop(context);
                _confirmBulkDelete(
                  context,
                  selectedUsers.map((u) => u.id).toList(),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.restore, color: Colors.green),
              title: const Text('Restore Selected'),
              onTap: () {
                Navigator.pop(context);
                cubit.bulkRestoreUsers(selectedUsers.map((u) => u.id).toList());
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _confirmBulkDelete(BuildContext context, List<String> userIds) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Bulk Delete'),
        content: Text(
          'Are you sure you want to delete ${userIds.length} users?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AdminUserCubit>().bulkDeleteUsers(userIds);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showCreateUserDialog(BuildContext context) {
    final cubit = context.read<AdminUserCubit>();
    showDialog(
      context: context,
      builder: (context) => MultiBlocProvider(
        providers: [
          BlocProvider.value(value: cubit),
          BlocProvider(create: (context) => di.sl<RoleCubit>()),
          BlocProvider(create: (context) => di.sl<DepartmentCubit>()),
        ],
        child: CreateUserDialog(
          onUserCreated: () {
            // Refresh user list after creating new user
            cubit.loadUsers();
          },
        ),
      ),
    );
  }

  void _showUserDetailsDialog(BuildContext context, AdminUserEntity user) {
    final cubit = context.read<AdminUserCubit>();
    showDialog(
      context: context,
      builder: (context) => BlocProvider.value(
        value: cubit,
        child: EditUserDialog(user: user),
      ),
    );
  }

  void _handleUserAction(
    BuildContext context,
    String action,
    AdminUserEntity user,
  ) {
    final cubit = context.read<AdminUserCubit>();

    switch (action) {
      case 'edit':
        _showUserDetailsDialog(context, user);
        break;
      case 'delete':
        _confirmDeleteUser(context, user);
        break;
      case 'restore':
        cubit.restoreUser(user.id);
        break;
      case 'toggle_status':
        cubit.toggleUserStatus(user.id);
        break;
      case 'reset_password':
        _showResetPasswordDialog(context, user);
        break;
    }
  }

  void _confirmDeleteUser(BuildContext context, AdminUserEntity user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Delete'),
        content: Text('Are you sure you want to delete ${user.displayName}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AdminUserCubit>().softDeleteUser(user.id);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showResetPasswordDialog(BuildContext context, AdminUserEntity user) {
    final passwordController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Reset Password for ${user.displayName}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Enter new password for ${user.email}'),
            const SizedBox(height: 16),
            TextField(
              controller: passwordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'New Password',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (passwordController.text.isNotEmpty) {
                Navigator.pop(context);
                context.read<AdminUserCubit>().resetUserPassword(
                  user.id,
                  passwordController.text,
                );
              }
            },
            child: const Text('Reset Password'),
          ),
        ],
      ),
    );
  }
}
