import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/core/responsive/responsive.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../shared/widgets/show_custom_snackBar.dart'; // Giả sử hàm showTopSnackBar ở đây
import 'logout_button.dart';

class ContactSupportDialog {
  static void show(BuildContext context) {
    final responsive = context.responsive;
    final theme = Theme.of(context);
    final l10n = context.l10n;

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      backgroundColor: theme.colorScheme.surface,
      isScrollControlled: true,
      // Thêm để tránh lỗi tràn màn hình trên các thiế<PERSON> bị nhỏ
      builder: (BuildContext context) {
        return Container(
          padding: responsive.padding(all: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                l10n.settingContactSupport,
                style: theme.textTheme.headlineMedium!.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: responsive.heightPercentage(2)),
              _buildContactOption(
                responsive,
                theme,
                l10n.settingEmailSupport,
                "<EMAIL>",
                Icons.email_outlined,
                () => _launchEmail(context, "<EMAIL>"),
                context,
              ),
              _buildContactOption(
                responsive,
                theme,
                l10n.settingPhoneSupport,
                "0933110895",
                Icons.phone_outlined,
                () => _launchPhone(context, "0933110895"),
                context,
              ),
              _buildContactOption(
                responsive,
                theme,
                l10n.settingMessageSupport,
                "https://www.facebook.com/GoldenLotusTechsolutions",
                Icons.chat_bubble_outline,
                () => _launchUrl(
                  context,
                  "https://www.facebook.com/GoldenLotusTechsolutions",
                ),
                context,
              ),
              SizedBox(height: responsive.heightPercentage(1)),
              LogoutButton(
                onPressed: () => context.pop(),
                title: context.l10n.settingCancel,
              ),
            ],
          ),
        );
      },
    );
  }

  static Widget _buildContactOption(
    Responsive responsive,
    ThemeData theme,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
    BuildContext context,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6),
      decoration: BoxDecoration(
        border: Border.all(color: theme.dividerColor, width: 1.0),
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        leading: Icon(icon, color: theme.colorScheme.primary),
        title: Text(title, style: theme.textTheme.titleMedium),
        subtitle: Text(
          subtitle,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.secondary,
          ),
          overflow: TextOverflow.ellipsis,
        ),
        onTap: () {
          context.pop(); // Đóng bottom sheet trước khi thực hiện hành động
          onTap();
        },
      ),
    );
  }

  static Future<void> _launchEmail(BuildContext context, String email) async {
    final l10n = context.l10n;
    final Uri emailUri = Uri(scheme: 'mailto', path: email.trim());

    // Kiểm tra context một lần ở đầu hàm async là đủ
    if (!context.mounted) return;

    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      showTopSnackBar(
        context,
        title: l10n.settingError, // Sử dụng biến l10n chung
        message: l10n.settingMessageErrorEmail,
        isError: true,
      );
    }
  }

  static Future<void> _launchPhone(BuildContext context, String phone) async {
    final l10n = context.l10n;
    final Uri phoneUri = Uri(scheme: 'tel', path: phone.trim());

    if (!context.mounted) return;

    // GHI CHÚ: Đã loại bỏ Permission.phone vì không cần thiết cho schema 'tel:'.
    // 'tel:' chỉ mở trình quay số, không thực hiện cuộc gọi.
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    } else {
      showTopSnackBar(
        context,
        title: l10n.settingError,
        message: l10n.settingMessageErrorPhone,
        isError: true,
      );
    }
  }

  static Future<void> _launchUrl(BuildContext context, String url) async {
    final l10n = context.l10n;
    final Uri uri = Uri.parse(url.trim());

    if (!context.mounted) return;

    if (await canLaunchUrl(uri)) {
      // Mở trong ứng dụng bên ngoài (trình duyệt, facebook,...) thay vì webview trong app
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      showTopSnackBar(
        context,
        title: l10n.settingError,
        message: l10n.settingMessageErrorSocial,
        isError: true,
      );
    }
  }
}
