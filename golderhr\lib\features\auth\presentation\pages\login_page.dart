import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/animation/auth_page_animation_mixin.dar.dart';
import 'package:golderhr/core/animation/fade_slide_animation.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/core/routes/app_routes.dart';
import 'package:golderhr/shared/widgets/gradient_background.dart';

import '../../../../core/responsive/responsive.dart';
import '../widgets/auth_card.dart';
import '../widgets/auth_redirect_row.dart';
import '../widgets/auth_welcome_section.dart';
import '../widgets/language_selector.dart';
import '../widgets/login_form.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage>
    with TickerProviderStateMixin, AuthPageAnimationMixin {
  @override
  void initState() {
    super.initState();
    initializeAnimations();
  }

  @override
  void dispose() {
    disposeAnimations();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final sizeBoxHeight = context.responsive.adaptiveValue(
      mobile: context.rh(15),
      tablet: context.rh(15),
      mobileLandscape: context.rh(15),
      tabletLandscape: responsive.scaleHeight(15, landscapeScaleFactor: 0.9),
    );
    return Scaffold(
      body: GradientBackground(
        child: SafeArea(
          child: LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                keyboardDismissBehavior:
                    ScrollViewKeyboardDismissBehavior.onDrag,
                child: ConstrainedBox(
                  constraints: BoxConstraints(minHeight: constraints.maxHeight),
                  child: IntrinsicHeight(
                    child: Padding(
                      padding: context.responsive.padding(
                        horizontal: 24,
                        vertical: 10,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          _buildHeader(context),
                          SizedBox(height: sizeBoxHeight),
                          _buildLoginCard(),
                          SizedBox(height: sizeBoxHeight),
                          _buildRedirectRow(),
                          SizedBox(height: sizeBoxHeight),
                          _buildLanguageSelector(),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return FadeSlideAnimation(
      fadeAnimation: fadeAnimation,
      slideAnimation: slideAnimation,
      child: AuthWelcomeSection(
        title: context.l10n.loginTitle,
        subtitle: context.l10n.loginSubtitle,
      ),
    );
  }

  Widget _buildLoginCard() {
    return FadeSlideAnimation(
      fadeAnimation: fadeAnimation,
      slideAnimation: slideAnimation,
      child: const AuthCard(child: LoginForm()),
    );
  }

  Widget _buildRedirectRow() {
    return FadeTransition(
      opacity: fadeAnimation,
      child: AuthRedirectRow(
        questionText: context.l10n.loginNoAccount,
        actionText: context.l10n.loginSignUp,
        onPressed: () => context.push(AppRoutes.register),
      ),
    );
  }

  Widget _buildLanguageSelector() {
    return FadeTransition(
      opacity: fadeAnimation,
      child: const LanguageSelector(),
    );
  }
}
