import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/monthly_details.dart';

class AttendanceSummaryCard extends StatelessWidget {
  final MonthlySummaryForHistory summary;

  const AttendanceSummaryCard({super.key, required this.summary});

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final theme = Theme.of(context);

    return Container(
      margin: responsive.padding(horizontal: 16.0, vertical: 8.0),
      padding: responsive.padding(all: 16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryBlue.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: AppColors.primaryBlue.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Header with title
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.analytics_rounded,
                  color: AppColors.primaryBlue,
                  size: 20,
                ),
              ),
              SizedBox(width: responsive.widthPercentage(2)),
              Text(
                'Monthly Summary',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          SizedBox(height: responsive.heightPercentage(2)),

          // Summary stats
          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  'Work Days',
                  '${summary.workDays}',
                  Icons.calendar_today_rounded,
                  AppColors.primaryBlue,
                  responsive,
                  theme,
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: AppColors.textSecondary.withValues(alpha: 0.2),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'Late Arrivals',
                  '${summary.lateArrivals}',
                  Icons.schedule_rounded,
                  AppColors.warning,
                  responsive,
                  theme,
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: AppColors.textSecondary.withValues(alpha: 0.2),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'Absences',
                  '${summary.absences}',
                  Icons.cancel_rounded,
                  AppColors.error,
                  responsive,
                  theme,
                ),
              ),
            ],
          ),

          SizedBox(height: responsive.heightPercentage(1.5)),

          // Additional stats
          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  'Holidays',
                  '${summary.holidays}',
                  Icons.celebration_rounded,
                  AppColors.success,
                  responsive,
                  theme,
                ),
              ),
              Container(
                width: 1,
                height: 40,
                color: AppColors.textSecondary.withValues(alpha: 0.2),
              ),
              Expanded(
                child: _buildSummaryItem(
                  'Attendance Rate',
                  '${_calculateAttendanceRate()}%',
                  Icons.trending_up_rounded,
                  AppColors.primaryBlue,
                  responsive,
                  theme,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(
    String title,
    String value,
    IconData icon,
    Color color,
    dynamic responsive,
    ThemeData theme,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        SizedBox(height: responsive.heightPercentage(0.5)),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: responsive.heightPercentage(0.3)),
        Text(
          title,
          style: theme.textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  int _calculateAttendanceRate() {
    if (summary.workDays == 0) return 0;
    final presentDays = summary.workDays - summary.absences;
    return ((presentDays / summary.workDays) * 100).round();
  }
}
