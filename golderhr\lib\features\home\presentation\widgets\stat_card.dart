import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

class StatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const StatCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;

    return Container(
      padding: responsive.padding(all: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade400),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            // <PERSON><PERSON><PERSON> viết tốt hơn cho opacity
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: responsive.padding(all: 8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: color, size: responsive.fontSize(24)),
          ),
          SizedBox(height: responsive.heightPercentage(1.0)),
          Text(
            value,
            style:
                Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ) ??
                const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: responsive.heightPercentage(0.5)),
          Text(
            title,
            style:
                Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade800) ??
                const TextStyle(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
