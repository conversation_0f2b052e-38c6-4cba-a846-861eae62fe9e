import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/theme/app_colors.dart';
import 'package:golderhr/shared/widgets/responsive_spacer.dart';
import 'package:iconsax/iconsax.dart';
import 'package:intl/intl.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../data/model/notification_model.dart';

class NotificationDetailPage extends StatelessWidget {
  final NotificationModel notification;

  const NotificationDetailPage({super.key, required this.notification});

  @override
  Widget build(BuildContext context) {
    final textTheme = context.lightTheme.textTheme;
    // Định dạng ngày tháng đầy đủ, ví dụ: "14:30, Thứ <PERSON>, ngày 28 tháng 11, 2023"
    final fullDateFormatter = DateFormat(
      'HH:mm, EEEE, \'ngày\' dd \'tháng\' MM, yyyy',
      'vi_VN',
    );

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          context.l10n.notificationDetail,
          style: context.lightTheme.textTheme.headlineLarge,
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {},
            icon: Icon(Iconsax.trash, color: Colors.red),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: context.responsive.padding(all: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Hero(
                  tag: 'notification_icon_${notification.id}',
                  child: Container(
                    padding: context.responsive.padding(all: 16),
                    decoration: BoxDecoration(
                      color: notification.color.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Icon(
                      notification.icon,
                      color: notification.color,
                      size: context.responsive.fontSize(32),
                    ),
                  ),
                ),
                ResponsiveSpacer(
                  mobileSize: 16,
                  tabletSize: 20,
                  mobileLandscapeSize: 16,
                  tabletLandscapeSize: 20,
                  axis: Axis.horizontal,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // HERO TITLE FROM CARD
                      Hero(
                        tag: 'notification_title_${notification.id}',
                        child: Material(
                          type: MaterialType.transparency,
                          child: Text(
                            notification.title,
                            style: textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      ResponsiveSpacer(
                        mobileSize: 4,
                        tabletSize: 6,
                        mobileLandscapeSize: 4,
                        tabletLandscapeSize: 4,
                      ),
                      // HERO TIME FROM CARD
                      Hero(
                        tag: 'notification_time_${notification.id}',
                        child: Material(
                          type: MaterialType.transparency,
                          child: Text(
                            timeago.format(
                              notification.timestamp,
                              locale: 'vi',
                            ),
                            style: textTheme.bodyMedium?.copyWith(
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            ResponsiveSpacer(
              mobileSize: 24,
              tabletSize: 26,
              mobileLandscapeSize: 24,
              tabletLandscapeSize: 26,
            ),
            Divider(color: Colors.grey.shade300, thickness: 1),
            ResponsiveSpacer(
              mobileSize: 24,
              tabletSize: 26,
              mobileLandscapeSize: 24,
              tabletLandscapeSize: 26,
            ),

            // --- CONTENT SECTION ---
            Text(
              'Nội dung chi tiết',
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            ResponsiveSpacer(
              mobileSize: 12,
              tabletSize: 14,
              mobileLandscapeSize: 12,
              tabletLandscapeSize: 14,
            ),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                // color: theme.cardColor,
                borderRadius: BorderRadius.circular(12),
                // border: Border.all(color: theme.dividerColor.withOpacity(0.5))
              ),
              child: Text(
                // Kết hợp cả message và một nội dung dài hơn để minh họa
                "${notification.message}\n\n"
                "Đây là nội dung chi tiết hơn của thông báo. "
                "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. "
                "Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. "
                "Vui lòng thực hiện các hành động cần thiết theo hướng dẫn.",
                style: textTheme.bodyLarge?.copyWith(
                  height: 1.5,
                  color: textTheme.bodyMedium?.color,
                ),
                textAlign: TextAlign.justify,
              ),
            ),
            ResponsiveSpacer(
              mobileSize: 24,
              tabletSize: 26,
              mobileLandscapeSize: 24,
              tabletLandscapeSize: 26,
            ),

            // --- METADATA SECTION ---
            _buildMetadataRow(
              context,
              icon: Icons.calendar_today_rounded,
              title: context.l10n.notificationReceivedTime,
              content: fullDateFormatter.format(notification.timestamp),
            ),
            ResponsiveSpacer(
              mobileSize: 12,
              tabletSize: 14,
              mobileLandscapeSize: 12,
              tabletLandscapeSize: 14,
            ),
            if (notification.isImportant)
              _buildMetadataRow(
                context,
                icon: Icons.star_rounded,
                title: context.l10n.notificationLevel,
                content: context.l10n.notificationImportant,
                contentColor: Colors.amber.shade700,
              ),
            ResponsiveSpacer(
              mobileSize: 12,
              tabletSize: 14,
              mobileLandscapeSize: 12,
              tabletLandscapeSize: 14,
            ),
            _buildMetadataRow(
              context,
              icon: Icons.category_rounded,
              title: context.l10n.notificationCategory,
              content: _getCategoryName(notification.category),
            ),
          ],
        ),
      ),
    );
  }

  // Helper widget để hiển thị các dòng metadata
  Widget _buildMetadataRow(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String content,
    Color? contentColor,
  }) {
    return Row(
      children: [
        Icon(icon, color: Colors.grey.shade500, size: 20),
        ResponsiveSpacer(
          mobileSize: 12,
          tabletSize: 14,
          mobileLandscapeSize: 12,
          tabletLandscapeSize: 14,
          axis: Axis.horizontal,
        ),
        Text('$title: ', style: context.lightTheme.textTheme.bodyMedium),
        Expanded(
          child: Text(
            content,
            style: context.lightTheme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: contentColor,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  // Helper để chuyển category enum sang text
  String _getCategoryName(NotificationCategory category) {
    switch (category) {
      case NotificationCategory.customer:
        return "Khách hàng";
      case NotificationCategory.promotion:
        return "Khuyến mãi";
      case NotificationCategory.system:
        return "Hệ thống";
      case NotificationCategory.attendance:
        return "Chấm công";
      case NotificationCategory.leave:
        return "Nghỉ phép";
      case NotificationCategory.announcement:
        return "Thông báo";
      case NotificationCategory.reminder:
        return "Nhắc nhở";
      case NotificationCategory.custom:
        return "Tùy chỉnh";
    }
  }
}
