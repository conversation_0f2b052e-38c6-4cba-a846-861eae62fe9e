import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/routes/app_routes.dart';
import 'package:golderhr/shared/theme/app_colors.dart';
import 'package:golderhr/shared/widgets/show_custom_snackBar.dart';

import '../../../auth/presentation/cubit/auth_cubit.dart';

class LogoutDialog {
  static void show(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Center(
            child: Text(
              context.l10n.logout,
              style: Theme.of(context).textTheme.titleLarge,
            ),
          ),
          content: Text(
            context.l10n.settingConfirmLogOut,
            style: Theme.of(context).textTheme.titleMedium,
          ),
          actions: [
            ElevatedButton(
              onPressed: () {
                context.pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.background,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: Text(
                context.l10n.settingCancel,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium!.copyWith(color: Colors.red),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                context.read<AuthCubit>().logout();
                context.pop();
                showTopSnackBar(
                  context,
                  title: context.l10n.logout,
                  message: context.l10n.logOutSuccess,
                );

                context.go(AppRoutes.login);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: Text(
                context.l10n.logout,
                style: Theme.of(
                  context,
                ).textTheme.titleMedium!.copyWith(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }
}
