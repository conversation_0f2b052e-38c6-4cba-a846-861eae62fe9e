import 'package:flutter/material.dart';
import 'app_text_styles.dart';

class AppTheme {
  static ThemeData light(BuildContext context) {
    return ThemeData(
      useMaterial3: true,
      fontFamily: 'Roboto',
      colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
      scaffoldBackgroundColor: Colors.white,
      textTheme: TextTheme(
        displayLarge: AppTextStyle.black(context, size: 32),
        displayMedium: AppTextStyle.bold(context, size: 28),
        headlineLarge: AppTextStyle.bold(context, size: 24),
        headlineMedium: AppTextStyle.medium(context, size: 20),
        titleLarge: AppTextStyle.medium(context, size: 18),
        titleMedium: AppTextStyle.regular(context, size: 16),
        bodyLarge: AppTextStyle.regular(context, size: 14),
        bodyMedium: AppTextStyle.light(context, size: 12),
        labelLarge: AppTextStyle.medium(context, size: 12),
        labelSmall: AppTextStyle.light(context, size: 10),
      ),
    );
  }

  static ThemeData dark(BuildContext context) {
    return ThemeData(
      useMaterial3: true,
      fontFamily: 'Roboto',
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.blue,
        brightness: Brightness.dark,
      ),
      scaffoldBackgroundColor: Colors.black,
      textTheme: TextTheme(
        displayLarge: AppTextStyle.black(
          context,
          size: 32,
          color: Colors.white,
        ),
        displayMedium: AppTextStyle.bold(
          context,
          size: 28,
          color: Colors.white,
        ),
        headlineLarge: AppTextStyle.bold(
          context,
          size: 24,
          color: Colors.white,
        ),
        headlineMedium: AppTextStyle.medium(
          context,
          size: 20,
          color: Colors.white,
        ),
        titleLarge: AppTextStyle.medium(context, size: 18, color: Colors.white),
        titleMedium: AppTextStyle.regular(
          context,
          size: 16,
          color: Colors.white,
        ),
        bodyLarge: AppTextStyle.regular(
          context,
          size: 14,
          color: Colors.white70,
        ),
        bodyMedium: AppTextStyle.light(
          context,
          size: 12,
          color: Colors.white60,
        ),
        labelLarge: AppTextStyle.medium(context, size: 12, color: Colors.white),
        labelSmall: AppTextStyle.light(context, size: 10, color: Colors.white),
      ),
    );
  }
}
