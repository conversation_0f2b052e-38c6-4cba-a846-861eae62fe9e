import 'dart:convert';
import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:golderhr/core/network/dio_client.dart';
import 'package:golderhr/injection_container.dart';
import 'package:device_info_plus/device_info_plus.dart';

class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  FirebaseMessaging? _messaging;
  FlutterLocalNotificationsPlugin? _localNotifications;
  String? _fcmToken;

  // Getters
  String? get fcmToken => _fcmToken;
  FirebaseMessaging? get messaging => _messaging;

  /// Khởi tạo Firebase và FCM
  Future<void> initialize() async {
    try {
      // Khởi tạo Firebase
      await Firebase.initializeApp();

      _messaging = FirebaseMessaging.instance;

      // Yêu cầu quyền notification
      await _requestPermission();

      // Khởi tạo local notifications
      await _initializeLocalNotifications();

      // Lấy FCM token
      await _getFCMToken();

      // Đăng ký FCM token với backend
      await _registerTokenWithBackend();

      // Setup message handlers
      _setupMessageHandlers();

      print('Firebase Service initialized successfully');
    } catch (e) {
      print('Error initializing Firebase Service: $e');
    }
  }

  /// Yêu cầu quyền notification
  Future<void> _requestPermission() async {
    if (_messaging == null) return;

    NotificationSettings settings = await _messaging!.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    print('User granted permission: ${settings.authorizationStatus}');
  }

  /// Khởi tạo local notifications
  Future<void> _initializeLocalNotifications() async {
    _localNotifications = FlutterLocalNotificationsPlugin();

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
        );

    const InitializationSettings initializationSettings =
        InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsIOS,
        );

    await _localNotifications!.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }

  /// Lấy FCM token
  Future<void> _getFCMToken() async {
    if (_messaging == null) return;

    try {
      _fcmToken = await _messaging!.getToken();
      print('FCM Token: $_fcmToken');

      // Lắng nghe token refresh
      _messaging!.onTokenRefresh.listen((newToken) {
        _fcmToken = newToken;
        print('FCM Token refreshed: $newToken');
        _registerTokenWithBackend();
      });
    } catch (e) {
      print('Error getting FCM token: $e');
    }
  }

  /// Đăng ký FCM token với backend
  Future<void> _registerTokenWithBackend() async {
    if (_fcmToken == null) return;

    try {
      final deviceInfo = await _getDeviceInfo();
      final dioClient = sl<DioClient>();

      await dioClient.post(
        '/api/notifications/fcm-token',
        data: {
          'token': _fcmToken,
          'deviceType': Platform.isAndroid ? 'android' : 'ios',
          'deviceId': deviceInfo['deviceId'],
          'deviceInfo': deviceInfo,
        },
      );

      print('FCM token registered with backend successfully');
    } catch (e) {
      print('Error registering FCM token with backend: $e');
    }
  }

  /// Lấy thông tin device
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    final deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      return {
        'deviceId': androidInfo.id,
        'model': androidInfo.model,
        'brand': androidInfo.brand,
        'osVersion': androidInfo.version.release,
        'appVersion': '1.0.0', // Có thể lấy từ package_info_plus
      };
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      return {
        'deviceId': iosInfo.identifierForVendor,
        'model': iosInfo.model,
        'brand': 'Apple',
        'osVersion': iosInfo.systemVersion,
        'appVersion': '1.0.0',
      };
    }

    return {
      'deviceId': 'unknown',
      'model': 'unknown',
      'brand': 'unknown',
      'osVersion': 'unknown',
      'appVersion': '1.0.0',
    };
  }

  /// Setup message handlers
  void _setupMessageHandlers() {
    if (_messaging == null) return;

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print('Got a message whilst in the foreground!');
      print('Message data: ${message.data}');

      if (message.notification != null) {
        print('Message also contained a notification: ${message.notification}');
        _showLocalNotification(message);
      }
    });

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print('A new onMessageOpenedApp event was published!');
      _handleNotificationTap(message);
    });

    // Handle notification tap when app is terminated
    _messaging!.getInitialMessage().then((RemoteMessage? message) {
      if (message != null) {
        print('App opened from terminated state via notification');
        _handleNotificationTap(message);
      }
    });
  }

  /// Hiển thị local notification khi app đang foreground
  Future<void> _showLocalNotification(RemoteMessage message) async {
    if (_localNotifications == null) return;

    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
          'high_importance_channel',
          'High Importance Notifications',
          channelDescription:
              'This channel is used for important notifications.',
          importance: Importance.high,
          priority: Priority.high,
          showWhen: true,
        );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _localNotifications!.show(
      message.hashCode,
      message.notification?.title ?? 'Thông báo',
      message.notification?.body ?? '',
      platformChannelSpecifics,
      payload: jsonEncode(message.data),
    );
  }

  /// Xử lý khi user tap vào notification
  void _onNotificationTapped(NotificationResponse response) {
    if (response.payload != null) {
      final data = jsonDecode(response.payload!);
      _handleNotificationData(data);
    }
  }

  /// Hiển thị local notification cho testing
  Future<void> showLocalNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    if (_localNotifications == null) return;

    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
          'test_channel',
          'Test Notifications',
          channelDescription: 'Channel for testing notifications',
          importance: Importance.high,
          priority: Priority.high,
          showWhen: true,
        );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _localNotifications!.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      platformChannelSpecifics,
      payload: data != null ? jsonEncode(data) : null,
    );
  }

  /// Xử lý khi tap notification từ FCM
  void _handleNotificationTap(RemoteMessage message) {
    _handleNotificationData(message.data);
  }

  /// Xử lý data của notification
  void _handleNotificationData(Map<String, dynamic> data) {
    print('Handling notification data: $data');

    // Có thể navigate đến trang cụ thể dựa trên data
    // Ví dụ: nếu có notificationId thì navigate đến notification detail
    if (data.containsKey('notificationId')) {
      // Navigate to notification detail page
      // NavigationService.navigateTo('/notification-detail', arguments: data);
    }
  }

  /// Xóa FCM token khỏi backend khi logout
  Future<void> removeTokenFromBackend() async {
    if (_fcmToken == null) return;

    try {
      final dioClient = sl<DioClient>();
      await dioClient.delete(
        '/api/notifications/fcm-token',
        data: {'token': _fcmToken},
      );

      print('FCM token removed from backend successfully');
    } catch (e) {
      print('Error removing FCM token from backend: $e');
    }
  }

  /// Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    if (_messaging == null) return;

    try {
      await _messaging!.subscribeToTopic(topic);
      print('Subscribed to topic: $topic');
    } catch (e) {
      print('Error subscribing to topic: $e');
    }
  }

  /// Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    if (_messaging == null) return;

    try {
      await _messaging!.unsubscribeFromTopic(topic);
      print('Unsubscribed from topic: $topic');
    } catch (e) {
      print('Error unsubscribing from topic: $e');
    }
  }
}

/// Background message handler (phải là top-level function)
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  print('Handling a background message: ${message.messageId}');
}
