import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/leave_request.dart';
import '../repositories/leave_admin_repository.dart';

class ApproveLeaveRequest
    implements UseCase<LeaveRequest, ApproveLeaveRequestParams> {
  final LeaveAdminRepository repository;

  ApproveLeaveRequest(this.repository);

  @override
  Future<Either<Failure, LeaveRequest>> call(
    ApproveLeaveRequestParams params,
  ) async {
    return await repository.approveLeaveRequest(params.requestId);
  }
}

class ApproveLeaveRequestParams extends Equatable {
  final String requestId;

  const ApproveLeaveRequestParams({required this.requestId});

  @override
  List<Object?> get props => [requestId];
}
