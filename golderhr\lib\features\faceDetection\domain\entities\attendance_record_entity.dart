import 'package:equatable/equatable.dart';
import 'check_in_out_detail_entity.dart';

class AttendanceRecordEntity extends Equatable {
  final String id;
  final String employeeId;
  final DateTime workDate;
  final String status;
  final CheckInOutDetailEntity? checkIn;
  final CheckInOutDetailEntity? checkOut;

  const AttendanceRecordEntity({
    required this.id,
    required this.employeeId,
    required this.workDate,
    required this.status,
    this.checkIn,
    this.checkOut,
  });

  @override
  List<Object?> get props => [
    id,
    employeeId,
    workDate,
    status,
    checkIn,
    checkOut,
  ];
}
