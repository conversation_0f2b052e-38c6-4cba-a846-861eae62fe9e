import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../core/usecases/usecase.dart';
import '../../../domain/entities/attendance_history.dart';
import '../../../domain/entities/monthly_summary.dart';
import '../../../domain/entities/today_summary.dart';
import '../../../domain/entities/weekly_summary.dart';
import '../../../domain/usecases/get_attendance_history.dart';
import '../../../domain/usecases/get_monthly_summary.dart';
import '../../../domain/usecases/get_today_summary.dart';
import '../../../domain/usecases/get_weekly_summary.dart';
import 'attendance_state.dart';

class AttendanceCubitV1 extends Cubit<AttendanceState> {
  final GetTodaySummary getTodaySummary;
  final GetWeeklySummary getWeeklySummary;
  final GetMonthlySummary getMonthlySummary;
  final GetAttendanceHistory getAttendanceHistory;

  AttendanceCubitV1({
    required this.getTodaySummary,
    required this.getWeeklySummary,
    required this.getMonthlySummary,
    required this.getAttendanceHistory,
  }) : super(AttendanceInitial());

  Future<void> loadInitialData() async {
    emit(AttendanceLoading());

    final results = await Future.wait([
      getTodaySummary(NoParams()),
      getWeeklySummary(NoParams()),
      getMonthlySummary(NoParams()),
      getAttendanceHistory(
        const HistoryParams(page: 1, limit: 5),
      ), // Lấy 5 dòng lịch sử gần nhất
    ]);

    final todayResult = results[0];
    final weeklyResult = results[1];
    final monthlyResult = results[2];
    final historyResult = results[3];

    todayResult.fold(
      (failure) => emit(const AttendanceError('Failed to load Today data')),
      (todaySummary) {
        (weeklyResult as dynamic).fold(
          (failure) =>
              emit(const AttendanceError('Failed to load Weekly data')),
          (weeklySummary) {
            monthlyResult.fold(
              (failure) =>
                  emit(const AttendanceError('Failed to load Monthly data')),
              (monthlySummary) {
                historyResult.fold(
                  (failure) => emit(
                    const AttendanceError('Failed to load History data'),
                  ),
                  (attendanceHistory) {
                    // Nếu tất cả thành công, phát ra state Loaded với đầy đủ dữ liệu
                    emit(
                      AttendanceLoaded(
                        todaySummary: todaySummary as TodaySummary,
                        weeklySummary: weeklySummary as WeeklySummary,
                        monthlySummary: monthlySummary as MonthlySummary,
                        recentHistory:
                            (attendanceHistory as PaginatedAttendanceHistory)
                                .history,
                      ),
                    );
                  },
                );
              },
            );
          },
        );
      },
    );
  }
}
