import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/theme/app_text_styles.dart';
import 'package:golderhr/shared/widgets/show_custom_snackBar.dart';
import 'package:iconsax/iconsax.dart';

import '../cubit/role_cubit.dart';
import '../widgets/create_role_dialog.dart';
import '../widgets/edit_role_dialog.dart';
import '../../domain/entities/admin_user_entity.dart';

class RoleManagementPage extends StatefulWidget {
  const RoleManagementPage({super.key});

  @override
  State<RoleManagementPage> createState() => _RoleManagementPageState();
}

class _RoleManagementPageState extends State<RoleManagementPage>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadInitialData();
    _setupScrollListener();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.1), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    // Start animations
    _fadeController.forward();
    _slideController.forward();
  }

  void _loadInitialData() {
    context.read<RoleCubit>().loadRoles();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent * 0.8) {
        context.read<RoleCubit>().loadMoreRoles(
          search: _searchController.text.trim().isEmpty
              ? null
              : _searchController.text.trim(),
        );
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.lightTheme;
    final responsive = context.responsive;
    final l10n = context.l10n;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Role Management', // TODO: Use l10n.roleManagement after running flutter gen-l10n
          style: theme.textTheme.displaySmall!.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () => context.read<RoleCubit>().refreshRoles(
              search: _searchController.text.trim().isEmpty
                  ? null
                  : _searchController.text.trim(),
            ),
            icon: const Icon(Iconsax.refresh),
            tooltip: 'Refresh',
          ),
          IconButton(
            onPressed: () => _showCreateRoleDialog(context),
            icon: const Icon(Iconsax.add),
            tooltip: 'Add Role',
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: BlocConsumer<RoleCubit, RoleState>(
            listener: (context, state) {
              if (state.hasError) {
                showTopSnackBar(
                  context,
                  title: l10n.error,
                  message: state.error!,
                  isError: true,
                );
                context.read<RoleCubit>().clearMessages();
              } else if (state.hasSuccess) {
                showTopSnackBar(
                  context,
                  title: l10n.success,
                  message: state.successMessage!,
                  isError: false,
                );
                context.read<RoleCubit>().clearMessages();
              }
            },
            builder: (context, state) {
              return Column(
                children: [
                  // Search and Filter Section
                  Container(
                    padding: responsive.padding(all: 16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Search roles by name...',
                        hintStyle: AppTextStyle.regular(
                          context,
                          size: 14,
                          color: Colors.grey[500],
                        ),
                        prefixIcon: Icon(
                          Iconsax.search_normal,
                          color: Colors.grey[600],
                          size: responsive.scaleRadius(20),
                        ),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? IconButton(
                                onPressed: () {
                                  _searchController.clear();
                                  context.read<RoleCubit>().refreshRoles();
                                },
                                icon: Icon(
                                  Iconsax.close_circle,
                                  color: Colors.grey[600],
                                  size: responsive.scaleRadius(18),
                                ),
                              )
                            : null,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(
                            responsive.defaultRadius,
                          ),
                          borderSide: BorderSide(
                            color: Colors.grey.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(
                            responsive.defaultRadius,
                          ),
                          borderSide: BorderSide(
                            color: Colors.grey.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(
                            responsive.defaultRadius,
                          ),
                          borderSide: BorderSide(
                            color: theme.primaryColor,
                            width: 2,
                          ),
                        ),
                        filled: true,
                        fillColor: Colors.grey.withValues(alpha: 0.05),
                        contentPadding: responsive.padding(
                          horizontal: 16,
                          vertical: 14,
                        ),
                      ),
                      style: AppTextStyle.regular(context, size: 14),
                      onChanged: (value) {
                        setState(() {}); // To update suffixIcon
                        // Debounce search
                        Future.delayed(const Duration(milliseconds: 500), () {
                          if (mounted && _searchController.text == value) {
                            context.read<RoleCubit>().refreshRoles(
                              search: value.trim().isEmpty
                                  ? null
                                  : value.trim(),
                            );
                          }
                        });
                      },
                    ),
                  ),

                  // Roles List
                  Expanded(
                    child: state.isLoading && state.roles.isEmpty
                        ? Center(
                            child: TweenAnimationBuilder<double>(
                              duration: const Duration(milliseconds: 800),
                              tween: Tween(begin: 0.0, end: 1.0),
                              curve: Curves.easeInOut,
                              builder: (context, value, child) {
                                return Transform.scale(
                                  scale: 0.8 + (0.2 * value),
                                  child: Opacity(
                                    opacity: value,
                                    child: const CircularProgressIndicator(),
                                  ),
                                );
                              },
                            ),
                          )
                        : state.roles.isEmpty
                        ? _buildEmptyState(context)
                        : RefreshIndicator(
                            onRefresh: () =>
                                context.read<RoleCubit>().refreshRoles(
                                  search: _searchController.text.trim().isEmpty
                                      ? null
                                      : _searchController.text.trim(),
                                ),
                            child: ListView.builder(
                              controller: _scrollController,
                              padding: responsive.padding(all: 16),
                              itemCount:
                                  state.roles.length +
                                  (state.isLoading ? 1 : 0),
                              itemBuilder: (context, index) {
                                if (index >= state.roles.length) {
                                  return Center(
                                    child: Padding(
                                      padding: const EdgeInsets.all(16),
                                      child: TweenAnimationBuilder<double>(
                                        duration: const Duration(
                                          milliseconds: 600,
                                        ),
                                        tween: Tween(begin: 0.0, end: 1.0),
                                        curve: Curves.easeInOut,
                                        builder: (context, value, child) {
                                          return Transform.scale(
                                            scale: 0.7 + (0.3 * value),
                                            child: Opacity(
                                              opacity: value,
                                              child:
                                                  const CircularProgressIndicator(),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  );
                                }

                                final role = state.roles[index];
                                return AnimatedContainer(
                                  duration: Duration(
                                    milliseconds: 300 + (index * 50),
                                  ),
                                  curve: Curves.easeOutCubic,
                                  child: TweenAnimationBuilder<double>(
                                    duration: Duration(
                                      milliseconds: 400 + (index * 100),
                                    ),
                                    tween: Tween(begin: 0.0, end: 1.0),
                                    curve: Curves.easeOutCubic,
                                    builder: (context, value, child) {
                                      return Transform.translate(
                                        offset: Offset(0, 20 * (1 - value)),
                                        child: Opacity(
                                          opacity: value,
                                          child: _buildRoleCard(context, role),
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
                          ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final responsive = context.responsive;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Iconsax.security_user,
            size: responsive.scaleRadius(64),
            color: Colors.grey[400],
          ),
          SizedBox(height: responsive.scaleHeight(16)),
          Text(
            'No roles found',
            style: AppTextStyle.bold(
              context,
              size: 18,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: responsive.scaleHeight(8)),
          Text(
            'Create your first role to get started',
            style: AppTextStyle.regular(
              context,
              size: 14,
              color: Colors.grey[500],
            ),
          ),
          SizedBox(height: responsive.scaleHeight(24)),
          ElevatedButton.icon(
            onPressed: () => _showCreateRoleDialog(context),
            icon: const Icon(Iconsax.add),
            label: const Text('Add Role'),
          ),
        ],
      ),
    );
  }

  Widget _buildRoleCard(BuildContext context, RoleEntity role) {
    final responsive = context.responsive;
    final theme = context.lightTheme;

    return Container(
      margin: responsive.padding(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(responsive.defaultRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.08),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.12),
          width: 1,
        ),
      ),
      child: Padding(
        padding: responsive.padding(all: 16),
        child: Row(
          children: [
            // Role Icon
            Container(
              width: responsive.scaleWidth(50),
              height: responsive.scaleHeight(50),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    theme.primaryColor.withValues(alpha: 0.15),
                    theme.primaryColor.withValues(alpha: 0.08),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(responsive.defaultRadius),
                border: Border.all(
                  color: theme.primaryColor.withValues(alpha: 0.2),
                  width: 1.5,
                ),
              ),
              child: Icon(
                Iconsax.security_user,
                color: theme.primaryColor,
                size: responsive.scaleRadius(24),
              ),
            ),

            SizedBox(width: responsive.scaleWidth(16)),

            // Role Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    role.name,
                    style: AppTextStyle.bold(
                      context,
                      size: responsive.adaptiveValue<double>(
                        mobile: 16,
                        tablet: 18,
                        mobileLandscape: 17,
                        tabletLandscape: 19,
                      ),
                      color: Colors.grey[800],
                    ),
                  ),
                  SizedBox(height: responsive.scaleHeight(6)),
                  if (role.createdAt != null)
                    Row(
                      children: [
                        Icon(
                          Iconsax.calendar,
                          size: responsive.scaleRadius(14),
                          color: Colors.grey[500],
                        ),
                        SizedBox(width: responsive.scaleWidth(6)),
                        Text(
                          'Created ${_formatDate(role.createdAt!)}',
                          style: AppTextStyle.regular(
                            context,
                            size: responsive.adaptiveValue<double>(
                              mobile: 12,
                              tablet: 13,
                              mobileLandscape: 12.5,
                              tabletLandscape: 14,
                            ),
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),

            // Action Menu
            Container(
              width: responsive.scaleWidth(40),
              height: responsive.scaleHeight(40),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(responsive.defaultRadius),
                border: Border.all(
                  color: Colors.grey.withValues(alpha: 0.2),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    spreadRadius: 0,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: PopupMenuButton<String>(
                onSelected: (value) => _handleRoleAction(context, role, value),
                icon: Icon(
                  Iconsax.more,
                  color: Colors.grey[700],
                  size: responsive.scaleRadius(18),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(responsive.defaultRadius),
                ),
                elevation: 8,
                shadowColor: Colors.grey.withValues(alpha: 0.3),
                offset: const Offset(0, 4),
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: theme.primaryColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Iconsax.edit,
                            size: 16,
                            color: theme.primaryColor,
                          ),
                        ),
                        SizedBox(width: 12),
                        Text(
                          'Edit Role',
                          style: AppTextStyle.medium(
                            context,
                            size: 14,
                            color: Colors.grey[800],
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: Colors.red.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Iconsax.trash,
                            size: 16,
                            color: Colors.red[600],
                          ),
                        ),
                        SizedBox(width: 12),
                        Text(
                          'Delete Role',
                          style: AppTextStyle.medium(
                            context,
                            size: 14,
                            color: Colors.red[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleRoleAction(BuildContext context, RoleEntity role, String action) {
    switch (action) {
      case 'edit':
        _showEditRoleDialog(context, role);
        break;
      case 'delete':
        _showDeleteConfirmation(context, role);
        break;
    }
  }

  void _showCreateRoleDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) => BlocProvider.value(
        value: context.read<RoleCubit>(),
        child: const CreateRoleDialog(),
      ),
    );
  }

  void _showEditRoleDialog(BuildContext context, RoleEntity role) {
    showDialog(
      context: context,
      builder: (dialogContext) => BlocProvider.value(
        value: context.read<RoleCubit>(),
        child: EditRoleDialog(role: role),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, RoleEntity role) {
    final l10n = context.l10n;
    final responsive = context.responsive;

    // Check if it's a system role
    final systemRoles = ['admin', 'hr', 'manager', 'user'];
    final isSystemRole = systemRoles.contains(role.name.toLowerCase());

    if (isSystemRole) {
      _showSystemRoleWarning(context, role);
      return;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(responsive.defaultRadius * 1.5),
        ),
        elevation: 10,
        child: Container(
          width: responsive.adaptiveValue<double>(
            mobile: 320,
            tablet: 400,
            mobileLandscape: 360,
            tabletLandscape: 440,
          ),
          padding: responsive.padding(all: 24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(responsive.defaultRadius * 1.5),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                spreadRadius: 0,
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Warning Icon
              Container(
                width: responsive.scaleWidth(64),
                height: responsive.scaleHeight(64),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.red.withValues(alpha: 0.15),
                      Colors.red.withValues(alpha: 0.05),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(
                    responsive.defaultRadius * 2,
                  ),
                  border: Border.all(
                    color: Colors.red.withValues(alpha: 0.2),
                    width: 2,
                  ),
                ),
                child: Icon(
                  Iconsax.warning_2,
                  color: Colors.red[600],
                  size: responsive.scaleRadius(32),
                ),
              ),

              SizedBox(height: responsive.scaleHeight(20)),

              // Title
              Text(
                'Delete Role',
                style: AppTextStyle.bold(
                  context,
                  size: responsive.adaptiveValue<double>(
                    mobile: 20,
                    tablet: 22,
                    mobileLandscape: 21,
                    tabletLandscape: 24,
                  ),
                  color: Colors.grey[800],
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: responsive.scaleHeight(12)),

              // Content
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  style: AppTextStyle.regular(
                    context,
                    size: responsive.adaptiveValue<double>(
                      mobile: 14,
                      tablet: 15,
                      mobileLandscape: 14.5,
                      tabletLandscape: 16,
                    ),
                    color: Colors.grey[600],
                  ),
                  children: [
                    const TextSpan(
                      text: 'Are you sure you want to delete the role ',
                    ),
                    TextSpan(
                      text: '"${role.name}"',
                      style: AppTextStyle.bold(
                        context,
                        size: responsive.adaptiveValue<double>(
                          mobile: 14,
                          tablet: 15,
                          mobileLandscape: 14.5,
                          tabletLandscape: 16,
                        ),
                        color: Colors.red[600],
                      ),
                    ),
                    const TextSpan(text: '?\n\n'),
                    const TextSpan(
                      text: '⚠️ Warning: ',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const TextSpan(
                      text:
                          'If users are assigned to this role, the deletion will fail. Please reassign users to other roles first.\n\n',
                    ),
                    const TextSpan(
                      text: 'This action cannot be undone.',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),

              SizedBox(height: responsive.scaleHeight(28)),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        padding: responsive.padding(vertical: 14),
                        side: BorderSide(
                          color: Colors.grey.withValues(alpha: 0.3),
                          width: 1.5,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            responsive.defaultRadius,
                          ),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        l10n.cancel,
                        style: AppTextStyle.medium(
                          context,
                          size: 15,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                  ),

                  SizedBox(width: responsive.scaleWidth(12)),

                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        context.read<RoleCubit>().deleteExistingRole(role.id);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red[600],
                        foregroundColor: Colors.white,
                        padding: responsive.padding(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            responsive.defaultRadius,
                          ),
                        ),
                        elevation: 2,
                        shadowColor: Colors.red.withValues(alpha: 0.3),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Iconsax.trash,
                            size: responsive.scaleRadius(16),
                            color: Colors.white,
                          ),
                          SizedBox(width: responsive.scaleWidth(8)),
                          Text(
                            'Delete',
                            style: AppTextStyle.medium(
                              context,
                              size: 15,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSystemRoleWarning(BuildContext context, RoleEntity role) {
    final responsive = context.responsive;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(responsive.defaultRadius * 1.5),
        ),
        elevation: 10,
        child: Container(
          width: responsive.adaptiveValue<double>(
            mobile: 320,
            tablet: 400,
            mobileLandscape: 360,
            tabletLandscape: 440,
          ),
          padding: responsive.padding(all: 24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(responsive.defaultRadius * 1.5),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                spreadRadius: 0,
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Warning Icon
              Container(
                width: responsive.scaleWidth(64),
                height: responsive.scaleHeight(64),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.orange.withValues(alpha: 0.15),
                      Colors.orange.withValues(alpha: 0.05),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(
                    responsive.defaultRadius * 2,
                  ),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.2),
                    width: 2,
                  ),
                ),
                child: Icon(
                  Iconsax.shield_security,
                  color: Colors.orange[600],
                  size: responsive.scaleRadius(32),
                ),
              ),

              SizedBox(height: responsive.scaleHeight(20)),

              // Title
              Text(
                'System Role Protected',
                style: AppTextStyle.bold(
                  context,
                  size: responsive.adaptiveValue<double>(
                    mobile: 20,
                    tablet: 22,
                    mobileLandscape: 21,
                    tabletLandscape: 24,
                  ),
                  color: Colors.grey[800],
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: responsive.scaleHeight(12)),

              // Content
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  style: AppTextStyle.regular(
                    context,
                    size: responsive.adaptiveValue<double>(
                      mobile: 14,
                      tablet: 15,
                      mobileLandscape: 14.5,
                      tabletLandscape: 16,
                    ),
                    color: Colors.grey[600],
                  ),
                  children: [
                    const TextSpan(text: 'The role '),
                    TextSpan(
                      text: '"${role.name.toUpperCase()}"',
                      style: AppTextStyle.bold(
                        context,
                        size: responsive.adaptiveValue<double>(
                          mobile: 14,
                          tablet: 15,
                          mobileLandscape: 14.5,
                          tabletLandscape: 16,
                        ),
                        color: Colors.orange[600],
                      ),
                    ),
                    const TextSpan(
                      text:
                          ' is a system role and cannot be deleted.\n\nSystem roles are essential for the application to function properly.',
                    ),
                  ],
                ),
              ),

              SizedBox(height: responsive.scaleHeight(28)),

              // OK Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange[600],
                    foregroundColor: Colors.white,
                    padding: responsive.padding(vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        responsive.defaultRadius,
                      ),
                    ),
                    elevation: 2,
                    shadowColor: Colors.orange.withValues(alpha: 0.3),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Iconsax.shield_tick,
                        size: responsive.scaleRadius(16),
                        color: Colors.white,
                      ),
                      SizedBox(width: responsive.scaleWidth(8)),
                      Text(
                        'I Understand',
                        style: AppTextStyle.medium(
                          context,
                          size: 15,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
