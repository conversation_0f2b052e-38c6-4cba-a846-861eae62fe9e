import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../cubit/calendar_cubit.dart';
import '../widgets/add_event_dialog.dart';
import '../widgets/search_events_dialog.dart';

/// Helper class cho các dialog của Calendar
class CalendarDialogs {
  /// Hiển thị dialog thêm event
  static void showAddEventDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => BlocProvider.value(
        value: context.read<CalendarCubit>(),
        child: const AddEventDialog(),
      ),
    );
  }

  /// Hiển thị dialog tìm kiếm
  static void showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) => BlocProvider.value(
        value: context.read<CalendarCubit>(),
        child: const SearchEventsDialog(),
      ),
    );
  }

  /// Hiển thị dialog xác nhận xóa
  static Future<bool> showDeleteConfirmDialog(
    BuildContext context,
    String eventTitle,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text(
          'Delete Event',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'Are you sure you want to delete "$eventTitle"?',
          style: const TextStyle(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
    return result ?? false;
  }
}
