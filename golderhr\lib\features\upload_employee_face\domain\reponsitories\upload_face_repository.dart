import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:golderhr/features/upload_employee_face/domain/entities/user_for_dropdown_entity.dart';

import '../../../../core/error/failures.dart'; // Th<PERSON> viện để xử lý Either

// Dùng dartz để trả về hoặc là Failure, hoặc là dữ liệu thành công
abstract class UploadFaceRepository {
  Future<Either<Failure, List<UserForDropdownEntity>>> getUsersForDropdown();

  Future<Either<Failure, String>> uploadEmployeeFace({
    required String userId,
    required File imageFile,
  });
}
