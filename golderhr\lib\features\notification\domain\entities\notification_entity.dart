import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../data/model/notification_model.dart';

class NotificationEntity extends Equatable {
  final String id;
  final String title;
  final String message;
  final DateTime timestamp;
  final IconData icon;
  final Color color;
  final bool isRead;
  final bool isImportant;
  final NotificationCategory category;

  const NotificationEntity({
    required this.id,
    required this.title,
    required this.message,
    required this.timestamp,
    required this.icon,
    required this.color,
    required this.isRead,
    required this.isImportant,
    required this.category,
  });

  @override
  List<Object?> get props => [
    id,
    title,
    message,
    timestamp,
    isRead,
    isImportant,
  ];
}
