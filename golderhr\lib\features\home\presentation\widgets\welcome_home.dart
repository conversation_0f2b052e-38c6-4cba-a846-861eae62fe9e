import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../../shared/theme/app_colors.dart';

class WelcomeHome extends StatelessWidget {
  final String title, subtitle, subtitle2;
  final IconData icon;

  const WelcomeHome({
    super.key,
    required this.title,
    required this.subtitle,
    required this.subtitle2,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    return Container(
      padding: responsive.padding(all: 20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [AppColors.primaryGreen, AppColors.primaryBlue],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryBlue.withAlpha((0.3 * 255).round()),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white.withValues(alpha: 0.85),
                    fontWeight: FontWeight.w600,
                    fontSize: responsive.fontSize(16),
                  ),
                ),
                SizedBox(height: responsive.heightPercentage(0.8)),
                Text(
                  "${context.l10n.registerFullName}: $subtitle",
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontSize: responsive.fontSize(
                      18,
                    ), // giảm từ 20 xuống để cân đối
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: responsive.heightPercentage(0.8)),
                Text(
                  "${context.l10n.profilePosition}: $subtitle2",
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontSize: responsive.fontSize(15),
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),

          Container(
            padding: responsive.padding(all: 12),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha((0.2 * 255).round()),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: responsive.fontSize(32),
            ),
          ),
        ],
      ),
    );
  }
}
