import 'package:golderhr/features/upload_employee_face/domain/entities/user_for_dropdown_entity.dart';

class UserForDropdownModel extends UserForDropdownEntity {
  const UserForDropdownModel({
    required super.id,
    required super.fullname,
    required super.email,
  });

  factory UserForDropdownModel.fromJson(Map<String, dynamic> json) {
    return UserForDropdownModel(
      id: json['id']?.toString() ?? '', // Đảm bảo id là string
      fullname: json['fullname'] ?? '',
      email: json['email'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'id': id, 'fullname': fullname, 'email': email};
  }
}
