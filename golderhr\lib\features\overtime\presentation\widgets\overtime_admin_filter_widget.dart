import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import '../../../../../shared/theme/app_colors.dart';
import '../cubit/overtime_admin_cubit.dart';

class OvertimeAdminFilterWidget extends StatefulWidget {
  const OvertimeAdminFilterWidget({super.key});

  @override
  State<OvertimeAdminFilterWidget> createState() =>
      _OvertimeAdminFilterWidgetState();
}

class _OvertimeAdminFilterWidgetState extends State<OvertimeAdminFilterWidget>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, -0.3), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    // Start animations
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final l10n = context.l10n;
    final theme = Theme.of(context);

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: BlocBuilder<OvertimeAdminCubit, OvertimeAdminState>(
          builder: (context, state) {
            return AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: responsive.padding(horizontal: 16, vertical: 8),
              padding: responsive.padding(all: 20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.white, Colors.grey.shade50],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primaryBlue.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                    spreadRadius: 0,
                  ),
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
                border: Border.all(
                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.primaryBlue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.filter_list_rounded,
                          color: AppColors.primaryBlue,
                          size: 20,
                        ),
                      ),
                      SizedBox(width: responsive.widthPercentage(3)),
                      Text(
                        l10n.filterByStatus,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: responsive.heightPercentage(2)),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        _FilterChip(
                          label: l10n.all,
                          isSelected: state.selectedStatus == null,
                          onTap: () {
                            context.read<OvertimeAdminCubit>().filterByStatus(
                              null,
                            );
                          },
                          color: Colors.grey.shade600,
                          icon: Icons.all_inclusive_rounded,
                        ),
                        SizedBox(width: responsive.widthPercentage(2)),
                        _FilterChip(
                          label: l10n.pending,
                          isSelected: state.selectedStatus == 'pending',
                          onTap: () {
                            context.read<OvertimeAdminCubit>().filterByStatus(
                              'pending',
                            );
                          },
                          color: Colors.orange,
                          icon: Icons.schedule_rounded,
                        ),
                        SizedBox(width: responsive.widthPercentage(2)),
                        _FilterChip(
                          label: l10n.approved,
                          isSelected: state.selectedStatus == 'approved',
                          onTap: () {
                            context.read<OvertimeAdminCubit>().filterByStatus(
                              'approved',
                            );
                          },
                          color: Colors.green,
                          icon: Icons.check_circle_rounded,
                        ),
                        SizedBox(width: responsive.widthPercentage(2)),
                        _FilterChip(
                          label: l10n.rejected,
                          isSelected: state.selectedStatus == 'rejected',
                          onTap: () {
                            context.read<OvertimeAdminCubit>().filterByStatus(
                              'rejected',
                            );
                          },
                          color: Colors.red,
                          icon: Icons.cancel_rounded,
                        ),
                      ],
                    ),
                  ),
                  if (state.requests.isNotEmpty) ...[
                    SizedBox(height: responsive.heightPercentage(2)),
                    Container(
                      padding: responsive.padding(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: AppColors.primaryBlue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: AppColors.primaryBlue.withValues(alpha: 0.2),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.analytics_rounded,
                            color: AppColors.primaryBlue,
                            size: 16,
                          ),
                          SizedBox(width: responsive.widthPercentage(1)),
                          Text(
                            '${l10n.totalRequests}: ${state.requests.length}',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: AppColors.primaryBlue,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}

class _FilterChip extends StatefulWidget {
  final String label;
  final bool isSelected;
  final VoidCallback onTap;
  final Color color;
  final IconData icon;

  const _FilterChip({
    required this.label,
    required this.isSelected,
    required this.onTap,
    required this.color,
    required this.icon,
  });

  @override
  State<_FilterChip> createState() => _FilterChipState();
}

class _FilterChipState extends State<_FilterChip>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _animationController.forward(),
      onTapUp: (_) => _animationController.reverse(),
      onTapCancel: () => _animationController.reverse(),
      onTap: widget.onTap,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            gradient: widget.isSelected
                ? LinearGradient(
                    colors: [widget.color, widget.color.withValues(alpha: 0.8)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : null,
            color: widget.isSelected ? null : Colors.transparent,
            border: Border.all(
              color: widget.color,
              width: widget.isSelected ? 2 : 1.5,
            ),
            borderRadius: BorderRadius.circular(25),
            boxShadow: widget.isSelected
                ? [
                    BoxShadow(
                      color: widget.color.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ]
                : null,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                widget.icon,
                size: 16,
                color: widget.isSelected ? Colors.white : widget.color,
              ),
              const SizedBox(width: 6),
              Text(
                widget.label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: widget.isSelected ? Colors.white : widget.color,
                  fontWeight: widget.isSelected
                      ? FontWeight.w700
                      : FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
