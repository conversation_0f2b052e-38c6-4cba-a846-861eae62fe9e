import 'dart:async';
import 'dart:io';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import 'package:image_picker/image_picker.dart';

import '../../../../core/usecases/usecase.dart';
import '../../../../l10n/app_localizations.dart';
import '../../domain/entities/attendance_status_entity.dart';
import '../../domain/entities/location_entity.dart';
import '../../domain/usecase/check_in_usecase.dart';
import '../../domain/usecase/check_out_usecase.dart';
import '../../domain/usecase/get_today_attendance_usecase.dart';
import 'face_checkin_state.dart';

class FaceDetectionCubit extends Cubit<FaceDetectionState> {
  final CheckInUseCase checkInUseCase;
  final CheckOutUseCase checkOutUseCase;
  final GetTodayAttendanceUseCase getTodayAttendanceUseCase;
  final AppLocalizations l10n;

  FaceDetectionCubit({
    required this.checkInUseCase,
    required this.checkOutUseCase,
    required this.getTodayAttendanceUseCase,
    required this.l10n,
  }) : super(const FaceDetectionState()) {
    initialize();
  }

  Future<void> initialize() async {
    if (state.attendanceStatus == null && !state.isLoading) {
      emit(state.copyWith(isLoading: true, status: l10n.initializing));
    } else if (!state.isLoading) {
      emit(state.copyWith(isLoading: true, status: l10n.refreshingStatus));
    } else {
      return;
    }

    await checkAttendanceStatus();
    if (state.currentLocation == null || state.locationStatus.isEmpty) {
      await getCurrentLocation();
    }

    if (state.isLoading) {
      emit(state.copyWith(isLoading: false));
    }
  }

  Future<void> checkAttendanceStatus() async {
    emit(state.copyWith(isLoading: true));
    final result = await getTodayAttendanceUseCase(NoParams());
    result.fold(
      (failure) {
        emit(
          state.copyWith(
            status: failure.message,
            attendanceStatus: null,
            isLoading: false,
          ),
        );
      },
      (statusEntity) {
        String newStatusMessage;
        if (statusEntity == null) {
          newStatusMessage = l10n.errorLoadingAttendanceStatus;
        } else if (statusEntity.nextAction == NextActionType.CHECK_IN &&
            statusEntity.canCheckIn) {
          newStatusMessage = l10n.detectFaceReadyForCheckIn;
        } else if (statusEntity.nextAction == NextActionType.CHECK_OUT &&
            statusEntity.canCheckOut) {
          newStatusMessage = l10n.detectFaceReadyForCheckOut;
        } else if (statusEntity.nextAction == NextActionType.UNKNOWN &&
            !statusEntity.canCheckIn &&
            !statusEntity.canCheckOut) {
          if (statusEntity.checkInsCount > 0 &&
              statusEntity.checkInsCount == statusEntity.checkOutsCount) {
            newStatusMessage = l10n.allSessionsCompleted;
          } else {
            newStatusMessage = l10n.noFurtherActionsAvailable;
          }
        } else {
          newStatusMessage = l10n.statusUpdated;
        }
        emit(
          state.copyWith(
            status: newStatusMessage,
            attendanceStatus: statusEntity,
            isLoading: false,
          ),
        );
      },
    );
  }

  Future<void> getCurrentLocation() async {
    if (!state.isLoading) {
      emit(state.copyWith(isLoading: true));
    }
    emit(state.copyWith(locationStatus: l10n.detectFaceGettingLocation));

    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        emit(
          state.copyWith(
            isLoading: false,
            locationStatus: l10n.locationServicesDisabled,
          ),
        );
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          emit(
            state.copyWith(
              isLoading: false,
              locationStatus: l10n.detectFaceLocationPermissionDenied,
            ),
          );
          return;
        }
      }
      if (permission == LocationPermission.deniedForever) {
        emit(
          state.copyWith(
            isLoading: false,
            locationStatus: l10n.detectFaceLocationPermissionPermanentlyDenied,
          ),
        );
        return;
      }

      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 15),
        ),
      );
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );
      Placemark place = placemarks[0];
      String address =
          '${place.street ?? ''}, ${place.subLocality ?? ''}, ${place.locality ?? ''}, ${place.administrativeArea ?? ''}, ${place.country ?? ''}'
              .replaceAll(RegExp(r'^,*\s*|,\s*,'), ', ')
              .replaceAll(RegExp(r',\s*$'), '');

      emit(
        state.copyWith(
          currentLocation: position,
          locationStatus: address,
          isLoading: false,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          locationStatus: l10n.errorGettingLocation(e.toString()),
          isLoading: false,
        ),
      );
    }
  }

  Future<void> captureImage() async {
    emit(
      state.copyWith(
        isLoading: true,
        status: l10n.detectFaceOpeningCamera,
        clearImageAndFaces: true,
      ),
    );
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 100,
        preferredCameraDevice: CameraDevice.front,
        maxWidth: 1024,
        maxHeight: 1024,
      );
      if (pickedFile != null) {
        final imageFile = File(pickedFile.path);
        emit(
          state.copyWith(
            isLoading: false,
            image: imageFile,
            status: l10n.detectFaceImageCapturedReady,
          ),
        );
        await _detectFaces(pickedFile.path);
      } else {
        emit(
          state.copyWith(
            isLoading: false,
            status: l10n.detectFaceImageNotCaptured,
            clearImageAndFaces: true,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          status: l10n.errorCapturingImage(e.toString()),
          clearImageAndFaces: true,
        ),
      );
    }
  }

  Future<void> _detectFaces(String imagePath) async {
    emit(
      state.copyWith(
        isLoading: true,
        status: l10n.detectFaceAnalyzingSecurity,
        image: state.image,
      ),
    );
    try {
      final inputImage = InputImage.fromFilePath(imagePath);
      final faceDetector = FaceDetector(options: FaceDetectorOptions());
      final detectedFaces = await faceDetector.processImage(inputImage);

      if (detectedFaces.isEmpty) {
        emit(
          state.copyWith(
            isLoading: false,
            faces: [],
            image: state.image,
            status: l10n.detectFaceFaceNotFound,
          ),
        );
      } else {
        emit(
          state.copyWith(
            isLoading: false,
            faces: detectedFaces,
            image: state.image,
            status: l10n.detectFaceFacesDetected(detectedFaces.length),
          ),
        );
      }
      await faceDetector.close();
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          faces: [],
          image: state.image,
          status: l10n.errorDetectingFaces(e.toString()),
        ),
      );
    }
  }

  Future<void> performCheckIn() async {
    if (!_isReadyToProcess()) {
      if (state.image == null) {
        emit(state.copyWith(status: l10n.pleaseCaptureImage));
      } else if (state.faces.isEmpty) {
        emit(state.copyWith(status: l10n.noFaceDetectedInImage));
      } else if (state.currentLocation == null ||
          state.locationStatus.trim().isEmpty ||
          state.locationStatus == l10n.detectFaceGettingLocation ||
          state.locationStatus.toLowerCase().contains(
            "error getting location",
          ) ||
          state.locationStatus == l10n.locationServicesDisabled ||
          state.locationStatus == l10n.detectFaceLocationPermissionDenied ||
          state.locationStatus ==
              l10n.detectFaceLocationPermissionPermanentlyDenied) {
        emit(state.copyWith(status: l10n.pleaseWaitForLocation));
      } else {
        emit(state.copyWith(status: l10n.notReadyForAction(l10n.checkIn)));
      }
      return;
    }

    emit(
      state.copyWith(
        isLoading: true,
        status: l10n.detectFaceProcessingCheckIn,
        image: state.image,
        currentLocation: state.currentLocation,
        locationStatus: state.locationStatus,
        faces: state.faces,
        attendanceStatus: state.attendanceStatus,
      ),
    );

    if (state.image == null || state.currentLocation == null) {
      emit(
        state.copyWith(
          isLoading: false,
          status:
              "${l10n.unexpectedErrorPleaseRetry} (State inconsistency during CheckIn)",
        ),
      );
      return;
    }

    final params = CheckInOutParams(
      image: state.image!,
      location: LocationEntity(
        coordinates: [
          state.currentLocation!.longitude,
          state.currentLocation!.latitude,
        ],
        address: state.locationStatus,
      ),
    );

    final result = await checkInUseCase(params);
    result.fold(
      (failure) {
        emit(
          state.copyWith(
            isLoading: false,
            status: l10n.detectFaceCheckInError(failure.message),
            image: state.image,
            currentLocation: state.currentLocation,
            locationStatus: state.locationStatus,
            faces: state.faces,
          ),
        );
      },
      (attendanceRecord) {
        emit(
          state.copyWith(
            isLoading: false,
            clearImageAndFaces: true,
            status: l10n.detectFaceCheckInSuccess,
          ),
        );
        checkAttendanceStatus();
      },
    );
  }

  Future<void> performCheckOut() async {
    if (!_isReadyToProcess()) {
      if (state.image == null) {
        emit(state.copyWith(status: l10n.pleaseCaptureImage));
      } else if (state.faces.isEmpty) {
        emit(state.copyWith(status: l10n.noFaceDetectedInImage));
      } else if (state.currentLocation == null ||
          state.locationStatus.trim().isEmpty ||
          state.locationStatus == l10n.detectFaceGettingLocation ||
          state.locationStatus.toLowerCase().contains(
            "error getting location",
          ) ||
          state.locationStatus == l10n.locationServicesDisabled ||
          state.locationStatus == l10n.detectFaceLocationPermissionDenied ||
          state.locationStatus ==
              l10n.detectFaceLocationPermissionPermanentlyDenied) {
        emit(state.copyWith(status: l10n.pleaseWaitForLocation));
      } else {
        emit(state.copyWith(status: l10n.notReadyForAction(l10n.checkOut)));
      }
      return;
    }

    final currentState = state;

    emit(
      state.copyWith(
        isLoading: true,
        status: l10n.detectFaceProcessingCheckOut,
        image: currentState.image,
        currentLocation: currentState.currentLocation,
        locationStatus: currentState.locationStatus,
        faces: currentState.faces,
        attendanceStatus: currentState.attendanceStatus,
      ),
    );

    if (state.image == null || state.currentLocation == null) {
      emit(
        state.copyWith(
          isLoading: false,
          status: "${l10n.unexpectedErrorPleaseRetry} (State inconsistency)",
        ),
      );
      return;
    }

    final params = CheckInOutParams(
      image: state.image!,
      location: LocationEntity(
        coordinates: [
          state.currentLocation!.longitude,
          state.currentLocation!.latitude,
        ],
        address: state.locationStatus,
      ),
    );

    final result = await checkOutUseCase(params);
    result.fold(
      (failure) {
        emit(
          state.copyWith(
            isLoading: false,
            status: l10n.detectFaceCheckOutError(failure.message),
            image: state.image,
            currentLocation: state.currentLocation,
            locationStatus: state.locationStatus,
            faces: state.faces,
          ),
        );
      },
      (attendanceRecord) {
        emit(
          state.copyWith(
            isLoading: false,
            clearImageAndFaces: true,
            status: l10n.detectFaceCheckOutSuccess,
          ),
        );
        checkAttendanceStatus();
      },
    );
  }

  bool _isReadyToProcess() {
    if (state.isLoading) return false;
    if (state.image == null) {
      return false;
    }
    if (state.faces.isEmpty) {
      return false;
    }
    if (state.currentLocation == null) {
      return false;
    }
    if (state.locationStatus.trim().isEmpty ||
        state.locationStatus == l10n.detectFaceGettingLocation ||
        state.locationStatus == l10n.locationServicesDisabled ||
        state.locationStatus == l10n.detectFaceLocationPermissionDenied ||
        state.locationStatus ==
            l10n.detectFaceLocationPermissionPermanentlyDenied ||
        state.locationStatus.toLowerCase().contains("error getting location")) {
      return false;
    }
    return true;
  }

  void retakePhoto() {
    captureImage();
  }

  void clearCapturedImage() {
    emit(state.copyWith(clearImageAndFaces: true, status: l10n.imageCleared));
  }
}
