import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/responsive/responsive.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../../../shared/widgets/error_display_widget.dart';
import '../cubit/leave_cubit.dart';
import '../cubit/leave_state.dart';
import '../pages/leave_history_page.dart';
import 'leave_stat_card.dart';
import 'leave_record_item.dart';
import 'leave_policy_summary.dart';

class MyLeaveTab extends StatelessWidget {
  const MyLeaveTab({super.key});

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final theme = Theme.of(context);

    return Padding(
      padding: responsive.padding(all: 16),
      child: Bloc<PERSON><PERSON>er<LeaveCubit, LeaveState>(
        builder: (context, state) {
          if (state is LeaveLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is LeaveLoaded) {
            return SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: responsive.heightPercentage(1)),

                  // Stats Cards
                  Row(
                    children: [
                      Expanded(
                        child: LeaveStatCard(
                          title: context.l10n.used,
                          value:
                              '${state.balance.usedDays} ${context.l10n.days}',
                          icon: Icons.event_busy_outlined,
                          color: AppColors.error,
                        ),
                      ),
                      SizedBox(width: responsive.widthPercentage(3)),
                      Expanded(
                        child: LeaveStatCard(
                          title: context.l10n.pending,
                          value:
                              '${state.balance.pendingDays} ${context.l10n.days}',
                          icon: Icons.pending_actions_outlined,
                          color: AppColors.warning,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: responsive.heightPercentage(1.5)),
                  Row(
                    children: [
                      Expanded(
                        child: LeaveStatCard(
                          title: context.l10n.approved,
                          value:
                              '${state.balance.approvedDays} ${context.l10n.days}',
                          icon: Icons.check_circle_outline_rounded,
                          color: AppColors.success,
                        ),
                      ),
                      SizedBox(width: responsive.widthPercentage(3)),
                      Expanded(
                        child: LeaveStatCard(
                          title: context.l10n.rejected,
                          value:
                              '${state.balance.rejectedDays} ${context.l10n.days}',
                          icon: Icons.cancel_outlined,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: responsive.heightPercentage(3)),

                  // Recent Requests Section
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        context.l10n.recentLeaveRequests,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontSize: responsive.fontSize(18),
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      TextButton(
                        onPressed: () => _navigateToHistory(context),
                        child: Text(
                          context.l10n.viewAll,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: AppColors.primaryBlue,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: responsive.heightPercentage(2)),

                  if (state.recentRequests.isEmpty)
                    Container(
                      width: double.infinity,
                      padding: responsive.padding(all: 32),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.textSecondary.withValues(
                              alpha: 0.05,
                            ),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.event_note_outlined,
                            size: 48,
                            color: AppColors.textSecondary.withValues(
                              alpha: 0.5,
                            ),
                          ),
                          SizedBox(height: responsive.heightPercentage(1)),
                          Text(
                            context.l10n.noLeaveRequestsYet,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    )
                  else
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.textSecondary.withValues(
                              alpha: 0.05,
                            ),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        children: state.recentRequests.asMap().entries.map((
                          entry,
                        ) {
                          final index = entry.key;
                          final request = entry.value;
                          return Column(
                            children: [
                              LeaveRecordItem(request: request),
                              if (index < state.recentRequests.length - 1)
                                Divider(
                                  height: 1,
                                  indent: responsive.widthPercentage(4),
                                  endIndent: responsive.widthPercentage(4),
                                ),
                            ],
                          );
                        }).toList(),
                      ),
                    ),

                  SizedBox(height: responsive.heightPercentage(3)),

                  // Leave Policy Section
                  LeavePolicyCard(policies: state.policies),

                  SizedBox(height: responsive.heightPercentage(3)),
                ],
              ),
            );
          }

          return ErrorDisplayWidget(
            errorMessage: state is LeaveError ? state.message : null,
            onRetry: () {
              context.read<LeaveCubit>().loadLeaveData(l10n: context.l10n);
            },
          );
        },
      ),
    );
  }

  void _navigateToHistory(BuildContext context) {
    final leaveCubit = context.read<LeaveCubit>();
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: leaveCubit,
          child: const LeaveHistoryPage(),
        ),
      ),
    );
  }
}
