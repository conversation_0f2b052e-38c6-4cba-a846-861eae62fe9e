// Cần thiết cho File

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

// Đảm bảo đường dẫn này ch<PERSON>h xác đến file AppLogger của bạn
import '../../../../core/logger/app_logger.dart';
import '../../../../core/responsive/responsive.dart';
import '../../../../l10n/app_localizations.dart';
import '../cubit/face_checkin_cubit.dart';
import '../cubit/face_checkin_state.dart';

class ImagePreviewCard extends StatelessWidget {
  const ImagePreviewCard({super.key});

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    return BlocBuilder<FaceDetectionCubit, FaceDetectionState>(
      buildWhen: (previous, current) {
        // Chỉ build lại nếu image thay đổi (để chuyển giữa placeholder và preview)
        // hoặc isLoading thay đổi (để _ImagePreview có thể hiển thị/ẩn overlay)
        // hoặc faces thay đổi (để _ImagePreview vẽ lại khuôn mặt)
        // Nếu ImagePreviewCard chỉ quyết định giữa _ImagePlaceholder và _ImagePreview dựa trên state.image,
        // thì có thể chỉ cần previous.image != current.image.
        // Nhưng vì _ImagePreview bên trong cũng lắng nghe state, việc cho phép build lại rộng hơn một chút ở đây có thể chấp nhận được
        // hoặc để _ImagePreview tự quản lý buildWhen của nó chặt chẽ hơn.
        // Hiện tại, để đơn giản và bao quát, chúng ta có thể giữ như sau hoặc chỉ dùng:
        // return previous.image != current.image;
        return previous.image != current.image ||
            previous.isLoading !=
                current.isLoading || // Cần cho _ImagePreview bên trong
            previous.faces != current.faces; // Cần cho _ImagePreview bên trong
      },
      builder: (context, state) {
        AppLogger.info(
          'ImagePreviewCard - BUILDER - state.image: ${state.image?.path}, state.isLoading: ${state.isLoading}, faces count: ${state.faces.length}',
        );
        return Container(
          height: responsive.scaleHeight(300),
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(responsive.scaleRadius(20)),
            border: state.image != null
                ? Border.all(color: Colors.blue.shade300, width: 2)
                : null,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(20),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(responsive.scaleRadius(19)),
            child: state.image == null
                ? const _ImagePlaceholder()
                : const _ImagePreview(), // Đã sửa: Thêm const
          ),
        );
      },
    );
  }
}

class _ImagePlaceholder extends StatelessWidget {
  const _ImagePlaceholder(); // Đã sửa: Thêm const

  @override
  Widget build(BuildContext context) {
    AppLogger.info(
      '_ImagePlaceholder - BUILDER',
    ); // Log khi placeholder được build
    final responsive = Responsive.of(context);
    final l10n = AppLocalizations.of(context)!;
    return Container(
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: responsive.padding(all: 20),
            decoration: BoxDecoration(
              color: Colors.grey.withAlpha(25),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.camera_alt_outlined,
              size: responsive.fontSize(50),
              color: Colors.grey.shade400,
            ),
          ),
          SizedBox(height: responsive.scaleHeight(16)),
          Text(
            l10n.detectFaceCaptureToStart,
            style: TextStyle(
              fontSize: responsive.fontSize(16),
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade600,
            ),
          ),
          SizedBox(height: responsive.scaleHeight(8)),
          Text(
            l10n.detectFaceSystemWillCheck,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: responsive.fontSize(12),
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }
}

class _ImagePreview extends StatelessWidget {
  const _ImagePreview(); // Đã sửa: Thêm const

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final l10n = AppLocalizations.of(context)!;
    return BlocBuilder<FaceDetectionCubit, FaceDetectionState>(
      buildWhen: (previous, current) {
        // _ImagePreview cần build lại khi ảnh thay đổi (dù ít xảy ra khi nó đã được hiển thị),
        // khi trạng thái loading thay đổi (để hiển thị/ẩn overlay),
        // và khi danh sách khuôn mặt thay đổi (để vẽ lại CustomPaint).
        return previous.image !=
                current
                    .image || // Để an toàn, dù state.image nên đã có giá trị khi widget này build
            previous.isLoading != current.isLoading ||
            previous.faces != current.faces;
      },
      builder: (context, state) {
        AppLogger.info(
          '_ImagePreview - BUILDER - state.image: ${state.image?.path}, state.isLoading: ${state.isLoading}, faces count: ${state.faces.length}',
        );

        // Kiểm tra an toàn trước khi truy cập state.image!
        // Mặc dù ImagePreviewCard đã kiểm tra state.image != null trước khi hiển thị _ImagePreview,
        // nhưng state có thể thay đổi giữa các lần build.
        // Nếu vì lý do nào đó state.image trở lại null trong một lần build của _ImagePreview,
        // Image.file(state.image!) sẽ gây lỗi.
        if (state.image == null) {
          AppLogger.info(
            '_ImagePreview - BUILDER - state.image became NULL unexpectedly! Rendering placeholder instead.',
          );
          // Trở về placeholder hoặc một widget rỗng để tránh lỗi runtime.
          // Hoặc có thể ImagePreviewCard nên quản lý chặt hơn việc state.image là null
          // và không render _ImagePreview nếu nó null.
          return const _ImagePlaceholder(); // Hoặc SizedBox.shrink();
        }

        return Stack(
          fit: StackFit.expand,
          children: [
            Image.file(
              state.image!,
              fit: BoxFit.cover,
            ), // Đã an toàn hơn nhờ kiểm tra ở trên
            if (state.isLoading)
              Container(
                color: Colors.black.withAlpha(153),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                      SizedBox(height: responsive.scaleHeight(16)),
                      Text(
                        l10n.detectFaceAnalyzingSecurity,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: responsive.fontSize(16),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            if (state.faces.isNotEmpty && !state.isLoading)
              CustomPaint(
                size: Size.infinite,
                painter: FaceDetectionPainter(
                  faces: state.faces,
                  strokeWidth: responsive.fontSize(3.0),
                  radius: responsive.scaleRadius(8),
                ),
              ),
          ],
        );
      },
    );
  }
}

class FaceDetectionPainter extends CustomPainter {
  final List<Face> faces;
  final double strokeWidth;
  final double radius;

  FaceDetectionPainter({
    required this.faces,
    this.strokeWidth = 3.0,
    this.radius = 8.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // AppLogger.info('FaceDetectionPainter - PAINT - faces count: ${faces.length}'); // Bỏ log này nếu quá nhiều
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..color = Colors.green.shade400;

    final fillPaint = Paint()
      ..style = PaintingStyle.fill
      ..color = Colors.green.shade200.withAlpha(51);

    for (final face in faces) {
      final rect = face.boundingBox;
      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, Radius.circular(radius)),
        fillPaint,
      );
      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, Radius.circular(radius)),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant FaceDetectionPainter oldDelegate) {
    bool SRepaint =
        oldDelegate.faces != faces ||
        oldDelegate.strokeWidth != strokeWidth ||
        oldDelegate.radius != radius;
    // AppLogger.info('FaceDetectionPainter - shouldRepaint: $SRepaint'); // Bỏ log này nếu quá nhiều
    return SRepaint;
  }
}
