class ApproverModel {
  final String id;
  final String fullname;
  final String department;
  final String position;
  final String email;
  final String roleName;

  const ApproverModel({
    required this.id,
    required this.fullname,
    required this.department,
    required this.position,
    required this.email,
    required this.roleName,
  });

  factory ApproverModel.fromJson(Map<String, dynamic> json) {
    return ApproverModel(
      id: json['_id'] ?? '',
      fullname: json['fullname'] ?? '',
      department: json['department'] ?? '',
      position: json['position'] ?? '',
      email: json['email'] ?? '',
      roleName: json['role']?['name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'fullname': fullname,
      'department': department,
      'position': position,
      'email': email,
      'role': {'name': roleName},
    };
  }

  String get displayName => '$fullname ($position)';
  String get displayInfo => '$fullname - $department';
}
