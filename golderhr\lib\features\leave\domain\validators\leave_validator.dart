import '../entities/leave_request.dart';

/// Leave validation rules following HRM business logic
class LeaveValidator {
  /// Validate leave request according to business rules
  static LeaveValidationResult validateLeaveRequest({
    required LeaveType type,
    required DateTime startDate,
    required DateTime endDate,
    required String reason,
    int? remainingDays,
  }) {
    final errors = <String>[];
    
    // Basic validation
    if (reason.trim().isEmpty) {
      errors.add('Reason is required');
    } else if (reason.trim().length < 10) {
      errors.add('Reason must be at least 10 characters long');
    } else if (reason.trim().length > 500) {
      errors.add('Reason must not exceed 500 characters');
    }
    
    // Date validation
    final dateValidation = _validateDates(startDate, endDate);
    if (dateValidation != null) {
      errors.add(dateValidation);
    }
    
    // Type-specific validation
    final typeValidation = _validateLeaveType(type, startDate, endDate);
    if (typeValidation != null) {
      errors.add(typeValidation);
    }
    
    // Balance validation
    if (remainingDays != null && type == LeaveType.annual) {
      final duration = _calculateDuration(startDate, endDate);
      if (duration > remainingDays) {
        errors.add('Insufficient leave balance. You have $remainingDays days remaining');
      }
    }
    
    return LeaveValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }
  
  /// Validate dates
  static String? _validateDates(DateTime startDate, DateTime endDate) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final startDateOnly = DateTime(startDate.year, startDate.month, startDate.day);
    final endDateOnly = DateTime(endDate.year, endDate.month, endDate.day);
    
    // Check if end date is after or equal to start date
    if (endDateOnly.isBefore(startDateOnly)) {
      return 'End date must be after or equal to start date';
    }
    
    // Check if start date is not in the past (allow today)
    if (startDateOnly.isBefore(today)) {
      return 'Cannot submit leave request for past dates';
    }
    
    // Check if dates are not too far in the future (1 year)
    final oneYearFromNow = DateTime(now.year + 1, now.month, now.day);
    if (startDateOnly.isAfter(oneYearFromNow) || endDateOnly.isAfter(oneYearFromNow)) {
      return 'Cannot submit leave request more than 1 year in advance';
    }
    
    // Check maximum duration (30 days for most leave types)
    final duration = _calculateDuration(startDate, endDate);
    if (duration > 30) {
      return 'Leave duration cannot exceed 30 days per request';
    }
    
    return null;
  }
  
  /// Validate leave type specific rules
  static String? _validateLeaveType(LeaveType type, DateTime startDate, DateTime endDate) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final startDateOnly = DateTime(startDate.year, startDate.month, startDate.day);
    final duration = _calculateDuration(startDate, endDate);
    
    switch (type) {
      case LeaveType.sick:
        // Sick leave can be immediate (today)
        return null;
        
      case LeaveType.annual:
        // Annual leave should be planned in advance (at least 1 day notice for short leaves)
        if (duration > 5) {
          final threeDaysFromNow = DateTime(now.year, now.month, now.day + 3);
          if (startDateOnly.isBefore(threeDaysFromNow)) {
            return 'Annual leave longer than 5 days requires at least 3 days advance notice';
          }
        }
        return null;
        
      case LeaveType.maternity:
        // Maternity leave can be longer
        if (duration > 180) {
          return 'Maternity leave cannot exceed 180 days per request';
        }
        return null;
        
      case LeaveType.personal:
      case LeaveType.unpaid:
        // Personal and unpaid leave should have reasonable advance notice
        if (duration > 3) {
          final twoDaysFromNow = DateTime(now.year, now.month, now.day + 2);
          if (startDateOnly.isBefore(twoDaysFromNow)) {
            return 'Personal/unpaid leave longer than 3 days requires at least 2 days advance notice';
          }
        }
        return null;
    }
  }
  
  /// Calculate duration in days (inclusive)
  static int _calculateDuration(DateTime startDate, DateTime endDate) {
    final startDateOnly = DateTime(startDate.year, startDate.month, startDate.day);
    final endDateOnly = DateTime(endDate.year, endDate.month, endDate.day);
    final difference = endDateOnly.difference(startDateOnly).inDays;
    return difference + 1; // +1 to include both start and end dates
  }
  
  /// Check if leave request overlaps with existing requests
  static bool hasOverlappingRequests(
    DateTime startDate,
    DateTime endDate,
    List<LeaveRequest> existingRequests,
  ) {
    final startDateOnly = DateTime(startDate.year, startDate.month, startDate.day);
    final endDateOnly = DateTime(endDate.year, endDate.month, endDate.day);
    
    for (final request in existingRequests) {
      // Only check pending and approved requests
      if (request.status == LeaveStatus.rejected || request.status == LeaveStatus.cancelled) {
        continue;
      }
      
      final existingStart = DateTime(
        request.startDate.year,
        request.startDate.month,
        request.startDate.day,
      );
      final existingEnd = DateTime(
        request.endDate.year,
        request.endDate.month,
        request.endDate.day,
      );
      
      // Check for overlap
      if (startDateOnly.isBefore(existingEnd.add(const Duration(days: 1))) &&
          endDateOnly.isAfter(existingStart.subtract(const Duration(days: 1)))) {
        return true;
      }
    }
    
    return false;
  }
  
  /// Get advance notice requirements for leave type
  static int getAdvanceNoticeDays(LeaveType type) {
    switch (type) {
      case LeaveType.sick:
        return 0; // Can be immediate
      case LeaveType.annual:
        return 1; // At least 1 day for short leaves, 3 days for long leaves
      case LeaveType.personal:
      case LeaveType.unpaid:
        return 2; // At least 2 days for long leaves
      case LeaveType.maternity:
        return 7; // At least 1 week notice
    }
  }
  
  /// Get maximum duration for leave type
  static int getMaxDuration(LeaveType type) {
    switch (type) {
      case LeaveType.sick:
        return 30;
      case LeaveType.annual:
        return 30;
      case LeaveType.personal:
        return 10;
      case LeaveType.unpaid:
        return 30;
      case LeaveType.maternity:
        return 180;
    }
  }
}

/// Result of leave validation
class LeaveValidationResult {
  final bool isValid;
  final List<String> errors;
  
  const LeaveValidationResult({
    required this.isValid,
    required this.errors,
  });
  
  String get firstError => errors.isNotEmpty ? errors.first : '';
  String get allErrors => errors.join('\n');
}
