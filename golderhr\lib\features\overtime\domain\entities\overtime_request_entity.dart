import 'package:equatable/equatable.dart';

enum OvertimeStatus { pending, approved, rejected }

enum OvertimeType { regular, holiday, weekend }

class ApproverEntity extends Equatable {
  final String id;
  final String fullname;
  final String department;
  final String position;
  final String email;
  final String roleName;

  const ApproverEntity({
    required this.id,
    required this.fullname,
    required this.department,
    required this.position,
    required this.email,
    required this.roleName,
  });

  @override
  List<Object?> get props => [
    id,
    fullname,
    department,
    position,
    email,
    roleName,
  ];
}

class OvertimeRequestEntity extends Equatable {
  final String id;
  final String employeeId;
  final String employeeName;
  final DateTime date;
  final DateTime startTime;
  final DateTime endTime;
  final double hours;
  final String reason;
  final OvertimeType type;
  final OvertimeStatus status;
  final String? assignedApproverId;
  final String? approvedBy;
  final DateTime? approvedAt;
  final String? rejectionReason;
  final DateTime createdAt;
  final DateTime updatedAt;

  const OvertimeRequestEntity({
    required this.id,
    required this.employeeId,
    required this.employeeName,
    required this.date,
    required this.startTime,
    required this.endTime,
    required this.hours,
    required this.reason,
    required this.type,
    required this.status,
    this.assignedApproverId,
    this.approvedBy,
    this.approvedAt,
    this.rejectionReason,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
    id,
    employeeId,
    employeeName,
    date,
    startTime,
    endTime,
    hours,
    reason,
    type,
    status,
    assignedApproverId,
    approvedBy,
    approvedAt,
    rejectionReason,
    createdAt,
    updatedAt,
  ];

  OvertimeRequestEntity copyWith({
    String? id,
    String? employeeId,
    String? employeeName,
    DateTime? date,
    DateTime? startTime,
    DateTime? endTime,
    double? hours,
    String? reason,
    OvertimeType? type,
    OvertimeStatus? status,
    String? assignedApproverId,
    String? approvedBy,
    DateTime? approvedAt,
    String? rejectionReason,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return OvertimeRequestEntity(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      employeeName: employeeName ?? this.employeeName,
      date: date ?? this.date,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      hours: hours ?? this.hours,
      reason: reason ?? this.reason,
      type: type ?? this.type,
      status: status ?? this.status,
      assignedApproverId: assignedApproverId ?? this.assignedApproverId,
      approvedBy: approvedBy ?? this.approvedBy,
      approvedAt: approvedAt ?? this.approvedAt,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
