import 'package:flutter/material.dart';
import 'package:golderhr/core/responsive/responsive.dart';

class ResponsiveSpacer extends StatelessWidget {
  final Axis axis;
  final double mobileSize;
  final double tabletSize;
  final double? mobileLandscapeSize;
  final double? tabletLandscapeSize;

  const ResponsiveSpacer({
    super.key,
    this.axis = Axis.vertical,
    required this.mobileSize,
    required this.tabletSize,
    this.mobileLandscapeSize,
    this.tabletLandscapeSize,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final scaledSize = responsive.adaptiveValue<double>(
      mobile: axis == Axis.vertical
          ? responsive.scaleHeight(mobileSize)
          : responsive.scaleWidth(mobileSize),
      tablet: axis == Axis.vertical
          ? responsive.scaleHeight(tabletSize)
          : responsive.scaleWidth(tabletSize),
      mobileLandscape: axis == Axis.vertical
          ? responsive.scaleHeight(mobileLandscapeSize ?? mobileSize)
          : responsive.scaleWidth(mobileLandscapeSize ?? mobileSize),
      tabletLandscape: axis == Axis.vertical
          ? responsive.scaleHeight(tabletLandscapeSize ?? tabletSize)
          : responsive.scaleWidth(tabletLandscapeSize ?? tabletSize),
    );

    return SizedBox(
      height: axis == Axis.vertical ? scaledSize : null,
      width: axis == Axis.horizontal ? scaledSize : null,
    );
  }
}
