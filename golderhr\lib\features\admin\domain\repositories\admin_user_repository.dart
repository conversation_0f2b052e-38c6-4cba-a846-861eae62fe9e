import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/admin_user_entity.dart';

abstract class AdminUserRepository {
  Future<Either<Failure, AdminUserListResult>> getAllUsers({
    int page = 1,
    int limit = 10,
    String? search,
    String? role,
    String? department,
    bool includeDeleted = false,
    String sortBy = 'createdAt',
    String sortOrder = 'desc',
  });

  Future<Either<Failure, AdminUserEntity>> getUserById(String userId);

  Future<Either<Failure, AdminUserEntity>> createUser({
    required String fullname,
    required String email,
    required String password,
    String? phone,
    String? department,
    String? position,
    required String role,
    String? organization,
  });

  Future<Either<Failure, AdminUserEntity>> updateUser(
    String userId, {
    String? fullname,
    String? email,
    String? phone,
    String? department,
    String? position,
    String? role,
    String? organization,
    bool? isdisable,
  });

  Future<Either<Failure, AdminUserEntity>> softDeleteUser(String userId);
  Future<Either<Failure, AdminUserEntity>> restoreUser(String userId);
  Future<Either<Failure, AdminUserEntity>> toggleUserStatus(String userId);
  Future<Either<Failure, void>> resetUserPassword(String userId, String newPassword);
  Future<Either<Failure, UserStatistics>> getUserStatistics();
  
  Future<Either<Failure, BulkOperationResult>> bulkDeleteUsers(List<String> userIds);
  Future<Either<Failure, BulkOperationResult>> bulkRestoreUsers(List<String> userIds);
}
