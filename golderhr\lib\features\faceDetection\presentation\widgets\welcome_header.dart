import 'package:flutter/material.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../../l10n/app_localizations.dart';

class WelcomeHeader extends StatelessWidget {
  const WelcomeHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final l10n = AppLocalizations.of(context)!;
    return Container(
      padding: responsive.padding(all: 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade600, Colors.blue.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(responsive.fontSize(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withAlpha(76),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: responsive.padding(all: 12),
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51),
              borderRadius: BorderRadius.circular(responsive.fontSize(12)),
            ),
            child: Icon(
              Icons.security,
              color: Colors.white,
              size: responsive.fontSize(32),
            ),
          ),
          SizedBox(width: responsive.scaleWidth(16)),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  l10n.detectFaceSecureCheckInTitle,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: responsive.fontSize(18),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: responsive.scaleHeight(4)),
                Text(
                  DateTime.now().toString().substring(0, 10),
                  style: TextStyle(
                    color: Colors.white.withAlpha(230),
                    fontSize: responsive.fontSize(14),
                  ),
                ),
                SizedBox(height: responsive.scaleHeight(4)),
                Text(
                  l10n.detectFaceSecureCheckInSystem,
                  style: TextStyle(
                    color: Colors.white.withAlpha(204),
                    fontSize: responsive.fontSize(12),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
