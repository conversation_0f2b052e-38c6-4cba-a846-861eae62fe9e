// Path: lib/features/auth/data/repositories/auth_repository_impl.dart

import 'package:dartz/dartz.dart';
import 'package:golderhr/core/error/exceptions.dart';
import 'package:golderhr/core/error/failures.dart';
import 'package:golderhr/core/network/dio_client.dart';
import 'package:golderhr/features/auth/data/datasources/locals/auth_local_data_source.dart';
import 'package:golderhr/features/auth/data/datasources/remotes/auth_remote_data_source.dart';
import 'package:golderhr/features/auth/domain/entities/user_entity.dart';
import 'package:golderhr/features/auth/domain/repositories/auth_repository.dart';

/// Lớp triển khai của AuthRepository, điều phối dữ liệu giữa remote và local.
/// Lớp này không biết gì về BLoC/Cubit hay UI.
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;
  final AuthLocalDataSource localDataSource;
  final DioClient dioClient;

  AuthRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.dioClient,
  });

  /// Đăng nhập người dùng bằng API.
  /// Chỉ chịu trách nhiệm lấy token và thông tin người dùng từ server.
  /// Việc cache user sẽ do lớp Presentation (Cubit) quyết định.
  @override
  Future<Either<Failure, UserEntity>> login(
    Map<String, dynamic> credentials,
  ) async {
    try {
      final loginResponse = await remoteDataSource.login(credentials);

      // Cache token và thiết lập cho DioClient ngay lập tức là hợp lý
      // vì các request tiếp theo có thể cần đến nó ngay.
      final token = loginResponse.token;
      await localDataSource.cacheToken(token);
      await dioClient.setToken(token);

      // Trả về đối tượng UserEntity để lớp trên xử lý.
      return Right(loginResponse.user);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, void>> logout() async {
    try {
      await remoteDataSource.logout();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, UserEntity?>> getCachedUser() async {
    try {
      final userModel = localDataSource.getUser();
      return Right(userModel);
    } catch (e) {
      return Left(CacheFailure("Could not retrieve user from cache."));
    }
  }

  // --- Các hàm còn lại được giữ nguyên vì đã khá tốt ---

  @override
  Future<Either<Failure, UserEntity>> register(
    Map<String, dynamic> userData,
  ) async {
    try {
      final response = await remoteDataSource.register(userData);
      return Right(response.user);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, UserEntity?>> getAuthStatus() async {
    try {
      final user = await remoteDataSource.getAuthStatus();
      return Right(user);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, void>> forgotPassword(String email) async {
    try {
      await remoteDataSource.forgotPassword(email);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, String>> verifyOtp(String otp) async {
    try {
      final resetToken = await remoteDataSource.verifyOtp(otp);
      return Right(resetToken);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, void>> resetPassword({
    required String resetToken,
    required String newPassword,
  }) async {
    try {
      await remoteDataSource.resetPassword(
        resetToken: resetToken,
        newPassword: newPassword,
      );
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, void>> resendOtp() async {
    try {
      await remoteDataSource.resendOtp();
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    }
  }
}
