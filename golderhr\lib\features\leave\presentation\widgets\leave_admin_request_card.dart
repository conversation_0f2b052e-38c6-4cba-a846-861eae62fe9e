import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:intl/intl.dart';
import '../../../../../shared/theme/app_colors.dart';
import '../../domain/entities/leave_request.dart';
import '../cubit/leave_admin_cubit.dart';
import 'leave_rejection_dialog.dart';

class LeaveAdminRequestCard extends StatefulWidget {
  final LeaveRequest request;
  final bool isProcessing;
  final Duration animationDelay;

  const LeaveAdminRequestCard({
    super.key,
    required this.request,
    required this.isProcessing,
    this.animationDelay = Duration.zero,
  });

  @override
  State<LeaveAdminRequestCard> createState() => _LeaveAdminRequestCardState();
}

class _LeaveAdminRequestCardState extends State<LeaveAdminRequestCard>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 700),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeOutBack),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0.3, 0), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    // Start animations with delay
    Future.delayed(widget.animationDelay, () {
      if (mounted) {
        _fadeController.forward();
        _scaleController.forward();
        _slideController.forward();
      }
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final l10n = context.l10n;
    final theme = Theme.of(context);

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Container(
            margin: responsive.padding(vertical: 4, horizontal: 2),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.white, Colors.grey.shade50],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: _getStatusColor(
                    widget.request.status,
                  ).withValues(alpha: 0.15),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                  spreadRadius: 0,
                ),
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.08),
                  blurRadius: 15,
                  offset: const Offset(0, 4),
                ),
              ],
              border: Border.all(
                color: _getStatusColor(
                  widget.request.status,
                ).withValues(alpha: 0.2),
                width: 1.5,
              ),
            ),
            child: Padding(
              padding: responsive.padding(all: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with employee name and status
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.request.employeeName,
                              style: theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.w800,
                                color: AppColors.textPrimary,
                                fontSize: 22,
                              ),
                            ),
                            SizedBox(height: responsive.heightPercentage(0.8)),
                            Container(
                              padding: responsive.padding(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: _getLeaveTypeColor(
                                  widget.request.type,
                                ).withValues(alpha: 0.15),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: _getLeaveTypeColor(
                                    widget.request.type,
                                  ).withValues(alpha: 0.3),
                                ),
                              ),
                              child: Text(
                                _getLeaveTypeLabel(widget.request.type),
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: _getLeaveTypeColor(
                                    widget.request.type,
                                  ),
                                  fontWeight: FontWeight.w700,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      _StatusChip(status: widget.request.status),
                    ],
                  ),

                  SizedBox(height: responsive.heightPercentage(2.5)),

                  // Date range and duration
                  Row(
                    children: [
                      Expanded(
                        flex: 3,
                        child: _InfoItem(
                          icon: Icons.calendar_today_rounded,
                          label: 'Date Range',
                          value: widget.request.dateRange,
                          iconColor: Colors.blue,
                        ),
                      ),
                      SizedBox(width: responsive.widthPercentage(3)),
                      Expanded(
                        flex: 2,
                        child: _InfoItem(
                          icon: Icons.schedule_rounded,
                          label: 'Duration',
                          value:
                              '${widget.request.duration} day${widget.request.duration > 1 ? 's' : ''}',
                          iconColor: Colors.green,
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: responsive.heightPercentage(2)),

                  // Reason
                  _InfoItem(
                    icon: Icons.description_rounded,
                    label: 'Reason',
                    value: widget.request.reason,
                    maxLines: 3,
                    iconColor: Colors.orange,
                  ),

                  // Rejection reason if exists
                  if (widget.request.rejectionReason != null) ...[
                    SizedBox(height: responsive.heightPercentage(2)),
                    _InfoItem(
                      icon: Icons.cancel_rounded,
                      label: 'Rejection Reason',
                      value: widget.request.rejectionReason!,
                      maxLines: 3,
                      valueColor: Colors.red[700],
                      iconColor: Colors.red,
                    ),
                  ],

                  SizedBox(height: responsive.heightPercentage(2.5)),

                  // Action buttons (only for pending requests)
                  if (widget.request.status == LeaveStatus.pending) ...[
                    Row(
                      children: [
                        Expanded(
                          child: _ActionButton(
                            onPressed: widget.isProcessing
                                ? null
                                : () => _showRejectDialog(context),
                            icon: Icons.close_rounded,
                            label: 'Reject',
                            color: Colors.red,
                            isOutlined: true,
                          ),
                        ),
                        SizedBox(width: responsive.widthPercentage(3)),
                        Expanded(
                          child: _ActionButton(
                            onPressed: widget.isProcessing
                                ? null
                                : () => _approveRequest(context),
                            icon: Icons.check_rounded,
                            label: 'Approve',
                            color: Colors.green,
                            isOutlined: false,
                          ),
                        ),
                      ],
                    ),
                  ],

                  // Assigned Approver info for pending requests
                  if (widget.request.status == LeaveStatus.pending &&
                      widget.request.assignedApproverId != null) ...[
                    SizedBox(height: responsive.heightPercentage(2)),
                    Container(
                      padding: responsive.padding(all: 12),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.blue.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.person_outline_rounded,
                            color: Colors.blue[700],
                            size: 20,
                          ),
                          SizedBox(width: responsive.widthPercentage(2)),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Assigned Approver',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: Colors.blue[700],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                Text(
                                  widget.request.approverName ?? 'Unknown',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],

                  // Approval info for processed requests
                  if (widget.request.status != LeaveStatus.pending &&
                      widget.request.approvedAt != null) ...[
                    Container(
                      margin: EdgeInsets.only(
                        top: responsive.heightPercentage(2),
                      ),
                      padding: responsive.padding(all: 16),
                      decoration: BoxDecoration(
                        color: widget.request.status == LeaveStatus.approved
                            ? Colors.green.withValues(alpha: 0.1)
                            : Colors.red.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: widget.request.status == LeaveStatus.approved
                              ? Colors.green.withValues(alpha: 0.3)
                              : Colors.red.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color:
                                  widget.request.status == LeaveStatus.approved
                                  ? Colors.green.withValues(alpha: 0.2)
                                  : Colors.red.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Icon(
                              widget.request.status == LeaveStatus.approved
                                  ? Icons.check_circle_rounded
                                  : Icons.cancel_rounded,
                              size: 20,
                              color:
                                  widget.request.status == LeaveStatus.approved
                                  ? Colors.green[700]
                                  : Colors.red[700],
                            ),
                          ),
                          SizedBox(width: responsive.widthPercentage(3)),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.request.status == LeaveStatus.approved
                                      ? 'Approved by ${_getApproverDisplayName()}'
                                      : 'Rejected by ${_getApproverDisplayName()}',
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    color:
                                        widget.request.status ==
                                            LeaveStatus.approved
                                        ? Colors.green.shade700
                                        : Colors.red.shade700,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  _formatDateTime(widget.request.approvedAt!),
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color:
                                        widget.request.status ==
                                            LeaveStatus.approved
                                        ? Colors.green.shade600
                                        : Colors.red.shade600,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _approveRequest(BuildContext context) {
    context.read<LeaveAdminCubit>().approveRequest(widget.request.id);
  }

  void _showRejectDialog(BuildContext context) {
    final cubit = context.read<LeaveAdminCubit>();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => LeaveRejectionDialog(
        onReject: (reason) {
          cubit.rejectRequest(widget.request.id, reason);
        },
      ),
    );
  }

  // Helper methods
  String _getApproverDisplayName() {
    // Try to get approver name from different fields
    if (widget.request.approverName != null &&
        widget.request.approverName!.isNotEmpty) {
      return widget.request.approverName!;
    }
    // Fallback to generic name based on status
    return 'Admin';
  }

  Color _getStatusColor(LeaveStatus status) {
    switch (status) {
      case LeaveStatus.pending:
        return Colors.orange;
      case LeaveStatus.approved:
        return Colors.green;
      case LeaveStatus.rejected:
        return Colors.red;
      case LeaveStatus.cancelled:
        return Colors.grey;
    }
  }

  Color _getLeaveTypeColor(LeaveType type) {
    switch (type) {
      case LeaveType.annual:
        return Colors.blue;
      case LeaveType.sick:
        return Colors.red;
      case LeaveType.personal:
        return Colors.purple;
      case LeaveType.maternity:
        return Colors.pink;
      case LeaveType.unpaid:
        return Colors.grey;
    }
  }

  String _getLeaveTypeLabel(LeaveType type) {
    switch (type) {
      case LeaveType.annual:
        return 'Annual Leave';
      case LeaveType.sick:
        return 'Sick Leave';
      case LeaveType.personal:
        return 'Personal Leave';
      case LeaveType.maternity:
        return 'Maternity Leave';
      case LeaveType.unpaid:
        return 'Unpaid Leave';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
  }
}

class _StatusChip extends StatelessWidget {
  final LeaveStatus status;

  const _StatusChip({required this.status});

  @override
  Widget build(BuildContext context) {
    Color color;
    IconData icon;
    String statusText;

    switch (status) {
      case LeaveStatus.pending:
        color = Colors.orange;
        icon = Icons.schedule_rounded;
        statusText = 'Pending';
        break;
      case LeaveStatus.approved:
        color = Colors.green;
        icon = Icons.check_circle_rounded;
        statusText = 'Approved';
        break;
      case LeaveStatus.rejected:
        color = Colors.red;
        icon = Icons.cancel_rounded;
        statusText = 'Rejected';
        break;
      case LeaveStatus.cancelled:
        color = Colors.grey;
        icon = Icons.block_rounded;
        statusText = 'Cancelled';
        break;
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color.withValues(alpha: 0.15),
            color.withValues(alpha: 0.08),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.4), width: 1.5),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.25),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 18, color: color),
          const SizedBox(width: 8),
          Text(
            statusText,
            style: TextStyle(
              color: color,
              fontSize: 14,
              fontWeight: FontWeight.w700,
            ),
          ),
        ],
      ),
    );
  }
}

class _InfoItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final int maxLines;
  final Color? valueColor;
  final Color iconColor;

  const _InfoItem({
    required this.icon,
    required this.label,
    required this.value,
    this.maxLines = 1,
    this.valueColor,
    required this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.02),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, size: 20, color: iconColor),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w600,
                    fontSize: 13,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  value,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: valueColor ?? AppColors.textPrimary,
                    fontWeight: FontWeight.w700,
                    fontSize: 16,
                  ),
                  maxLines: maxLines,
                  overflow: maxLines > 1
                      ? TextOverflow.visible
                      : TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _ActionButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final IconData icon;
  final String label;
  final Color color;
  final bool isOutlined;

  const _ActionButton({
    required this.onPressed,
    required this.icon,
    required this.label,
    required this.color,
    required this.isOutlined,
  });

  @override
  State<_ActionButton> createState() => _ActionButtonState();
}

class _ActionButtonState extends State<_ActionButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;

    return GestureDetector(
      onTapDown: (_) => _controller.forward(),
      onTapUp: (_) => _controller.reverse(),
      onTapCancel: () => _controller.reverse(),
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          child: widget.isOutlined
              ? OutlinedButton.icon(
                  onPressed: widget.onPressed,
                  icon: Icon(widget.icon, size: 18),
                  label: Text(widget.label),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: widget.color,
                    side: BorderSide(color: widget.color, width: 2),
                    padding: responsive.padding(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    textStyle: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                )
              : ElevatedButton.icon(
                  onPressed: widget.onPressed,
                  icon: Icon(widget.icon, size: 18),
                  label: Text(widget.label),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: widget.color,
                    foregroundColor: Colors.white,
                    padding: responsive.padding(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 4,
                    shadowColor: widget.color.withValues(alpha: 0.4),
                    textStyle: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
        ),
      ),
    );
  }
}
