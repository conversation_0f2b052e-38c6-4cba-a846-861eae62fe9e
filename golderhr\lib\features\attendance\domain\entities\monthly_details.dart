// lib/features/attendance/domain/entities/monthly_details.dart
import 'package:equatable/equatable.dart';

// <PERSON> tiết check-in/check-out của một session
class AttendanceSessionDetail extends Equatable {
  final String time;
  final String fullTime;
  final String location;
  final String imageUrl;

  const AttendanceSessionDetail({
    required this.time,
    required this.fullTime,
    required this.location,
    required this.imageUrl,
  });

  @override
  List<Object?> get props => [time, fullTime, location, imageUrl];
}

// Chi tiết của một session chấm công
class AttendanceSession extends Equatable {
  final int sessionNumber;
  final AttendanceSessionDetail checkIn;
  final AttendanceSessionDetail? checkOut;
  final String duration;
  final String status; // 'Completed', 'In Progress'

  const AttendanceSession({
    required this.sessionNumber,
    required this.checkIn,
    this.checkOut,
    required this.duration,
    required this.status,
  });

  @override
  List<Object?> get props => [
    sessionNumber,
    checkIn,
    checkOut,
    duration,
    status,
  ];
}

// Chi tiết của một ngày trong lịch
class AttendanceDayDetail extends Equatable {
  final DateTime date;
  final String status; // 'On Time', 'Late', 'Absent', 'Weekend', ...
  final String checkIn;
  final String checkOut;
  final String totalHours;
  final String overtime;
  final int sessionsCount;
  final bool hasMultipleSessions;
  final List<AttendanceSession> sessions; // Thêm danh sách sessions

  const AttendanceDayDetail({
    required this.date,
    required this.status,
    required this.checkIn,
    required this.checkOut,
    required this.totalHours,
    required this.overtime,
    required this.sessionsCount,
    required this.hasMultipleSessions,
    required this.sessions,
  });

  @override
  List<Object?> get props => [
    date,
    status,
    checkIn,
    checkOut,
    totalHours,
    overtime,
    sessionsCount,
    hasMultipleSessions,
    sessions,
  ];
}

// Entity cho cả màn hình Lịch, bao gồm cả tóm tắt tháng
class MonthlyDetails extends Equatable {
  final List<AttendanceDayDetail> dailyDetails;
  final MonthlySummaryForHistory summary;

  const MonthlyDetails({required this.dailyDetails, required this.summary});

  @override
  List<Object?> get props => [dailyDetails, summary];
}

// Entity tóm tắt riêng cho trang lịch
class MonthlySummaryForHistory extends Equatable {
  final int workDays;
  final int lateArrivals;
  final int absences;
  final int holidays;

  const MonthlySummaryForHistory({
    required this.workDays,
    required this.lateArrivals,
    required this.absences,
    required this.holidays,
  });

  @override
  List<Object?> get props => [workDays, lateArrivals, absences, holidays];
}
