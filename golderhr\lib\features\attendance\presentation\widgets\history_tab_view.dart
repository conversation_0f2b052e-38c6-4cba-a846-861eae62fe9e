import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:intl/intl.dart';

import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/monthly_details.dart';

class HistoryTabView extends StatefulWidget {
  final MonthlyDetails monthlyDetails;

  const HistoryTabView({super.key, required this.monthlyDetails});

  @override
  State<HistoryTabView> createState() => _HistoryTabViewState();
}

class _HistoryTabViewState extends State<HistoryTabView>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final responsive = context.responsive;
    final theme = Theme.of(context);

    // Sort attendance records by date (newest first)
    final sortedDetails = List<AttendanceDayDetail>.from(
      widget.monthlyDetails.dailyDetails,
    )..sort((a, b) => b.date.compareTo(a.date));

    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFF8FAFC), Color(0xFFE2E8F0)],
        ),
      ),
      child: sortedDetails.isEmpty
          ? _buildEmptyState(responsive, theme)
          : ListView.builder(
              padding: responsive.padding(all: 16.0),
              itemCount: sortedDetails.length,
              itemBuilder: (context, index) {
                final detail = sortedDetails[index];
                return _buildHistoryItem(detail, responsive, theme, index);
              },
            ),
    );
  }

  Widget _buildEmptyState(dynamic responsive, ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history_rounded,
            size: 64,
            color: AppColors.textSecondary.withValues(alpha: 0.5),
          ),
          SizedBox(height: responsive.heightPercentage(2.0)),
          Text(
            'No attendance records found',
            style: theme.textTheme.titleMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: responsive.heightPercentage(1.0)),
          Text(
            'Your attendance history will appear here',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryItem(
    AttendanceDayDetail detail,
    dynamic responsive,
    ThemeData theme,
    int index,
  ) {
    final isToday = _isToday(detail.date);
    final statusColor = _getStatusColor(detail);
    final statusIcon = _getStatusIcon(detail);

    return Container(
      margin: responsive.padding(bottom: 12.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        border: isToday
            ? Border.all(
                color: AppColors.primaryBlue.withValues(alpha: 0.3),
                width: 2,
              )
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _showDetailDialog(detail),
          child: Padding(
            padding: responsive.padding(all: 16.0),
            child: Column(
              children: [
                // Header row
                Row(
                  children: [
                    // Date and day
                    Expanded(
                      flex: 2,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                DateFormat('dd').format(detail.date),
                                style: theme.textTheme.titleLarge?.copyWith(
                                  color: AppColors.textPrimary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(width: responsive.widthPercentage(2.0)),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    DateFormat('MMM').format(detail.date),
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: AppColors.textSecondary,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    DateFormat('EEEE').format(detail.date),
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                                ],
                              ),
                              if (isToday) ...[
                                SizedBox(
                                  width: responsive.widthPercentage(2.0),
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColors.primaryBlue.withValues(
                                      alpha: 0.1,
                                    ),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    'Today',
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: AppColors.primaryBlue,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ),
                    ),

                    // Status
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: statusColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(statusIcon, size: 16, color: statusColor),
                          const SizedBox(width: 4),
                          Text(
                            _getStatusText(detail),
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: statusColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                if (detail.checkIn.isNotEmpty ||
                    detail.checkOut.isNotEmpty) ...[
                  SizedBox(height: responsive.heightPercentage(1.5)),

                  // Working hours summary
                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoChip(
                          'Check In',
                          detail.checkIn.isEmpty ? 'N/A' : detail.checkIn,
                          Icons.login_rounded,
                          AppColors.success,
                          responsive,
                          theme,
                        ),
                      ),
                      SizedBox(width: responsive.widthPercentage(2.0)),
                      Expanded(
                        child: _buildInfoChip(
                          'Check Out',
                          detail.checkOut.isEmpty ? 'N/A' : detail.checkOut,
                          Icons.logout_rounded,
                          AppColors.primaryBlue,
                          responsive,
                          theme,
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: responsive.heightPercentage(1.0)),

                  // Total hours and overtime
                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoChip(
                          'Total Hours',
                          detail.totalHours.isEmpty ? '0h' : detail.totalHours,
                          Icons.access_time_rounded,
                          AppColors.primaryBlue,
                          responsive,
                          theme,
                        ),
                      ),
                      SizedBox(width: responsive.widthPercentage(2.0)),
                      Expanded(
                        child: _buildInfoChip(
                          'Overtime',
                          detail.overtime.isEmpty ? '0h' : detail.overtime,
                          Icons.schedule_rounded,
                          AppColors.warning,
                          responsive,
                          theme,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(
    String label,
    String value,
    IconData icon,
    Color color,
    dynamic responsive,
    ThemeData theme,
  ) {
    return Container(
      padding: responsive.padding(all: 8.0),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, size: 16, color: color),
          SizedBox(width: responsive.widthPercentage(1.0)),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  Color _getStatusColor(AttendanceDayDetail detail) {
    switch (detail.status.toLowerCase()) {
      case 'absent':
        return AppColors.error;
      case 'late':
        return AppColors.warning;
      case 'on time':
        return AppColors.success;
      case 'weekend':
        return AppColors.textSecondary;
      default:
        return AppColors.primaryBlue;
    }
  }

  IconData _getStatusIcon(AttendanceDayDetail detail) {
    switch (detail.status.toLowerCase()) {
      case 'absent':
        return Icons.cancel_rounded;
      case 'late':
        return Icons.schedule_rounded;
      case 'on time':
        return Icons.check_circle_rounded;
      case 'weekend':
        return Icons.weekend_rounded;
      default:
        return Icons.help_outline_rounded;
    }
  }

  String _getStatusText(AttendanceDayDetail detail) {
    return detail.status;
  }

  void _showDetailDialog(AttendanceDayDetail detail) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Attendance Details'),
        content: Text(
          'Details for ${DateFormat('MMM dd, yyyy').format(detail.date)}',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }
}
