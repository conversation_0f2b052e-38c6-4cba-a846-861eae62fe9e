import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/calendar_event.dart';
import '../repositories/calendar_repository.dart';

/// Use case để thêm calendar event mới
class AddCalendarEvent
    implements UseCase<CalendarEvent, AddCalendarEventParams> {
  final CalendarRepository repository;

  AddCalendarEvent(this.repository);

  @override
  Future<Either<Failure, CalendarEvent>> call(
    AddCalendarEventParams params,
  ) async {
    return await repository.addEvent(params.event);
  }
}

/// Parameters cho AddCalendarEvent
class AddCalendarEventParams extends Equatable {
  final CalendarEvent event;

  const AddCalendarEventParams({required this.event});

  @override
  List<Object> get props => [event];
}
