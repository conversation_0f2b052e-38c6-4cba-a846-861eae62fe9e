// Import file Entity từ tầng Domain
import '../../domain/entities/monthly_details.dart';

//==============================================================
// MODEL CHO SESSION DETAIL (check-in/check-out)
//==============================================================
class AttendanceSessionDetailModel extends AttendanceSessionDetail {
  const AttendanceSessionDetailModel({
    required super.time,
    required super.fullTime,
    required super.location,
    required super.imageUrl,
  });

  factory AttendanceSessionDetailModel.fromJson(Map<String, dynamic> json) {
    return AttendanceSessionDetailModel(
      time: json['time'] ?? '--:--',
      fullTime: json['fullTime'] ?? '',
      location: json['location'] ?? 'Unknown location',
      imageUrl: json['imageUrl'] ?? '',
    );
  }
}

//==============================================================
// MODEL CHO SESSION
//==============================================================
class AttendanceSessionModel extends AttendanceSession {
  const AttendanceSessionModel({
    required super.sessionNumber,
    required super.checkIn,
    super.checkOut,
    required super.duration,
    required super.status,
  });

  factory AttendanceSessionModel.fromJson(Map<String, dynamic> json) {
    return AttendanceSessionModel(
      sessionNumber: json['sessionNumber'] ?? 1,
      checkIn: AttendanceSessionDetailModel.fromJson(
        json['checkIn'] as Map<String, dynamic>? ?? {},
      ),
      checkOut: json['checkOut'] != null
          ? AttendanceSessionDetailModel.fromJson(
              json['checkOut'] as Map<String, dynamic>,
            )
          : null,
      duration: json['duration'] ?? '--',
      status: json['status'] ?? 'Unknown',
    );
  }
}

//==============================================================
// MODEL CON: Đại diện cho chi tiết của MỘT ngày trên lịch
//==============================================================
class AttendanceDayDetailModel extends AttendanceDayDetail {
  const AttendanceDayDetailModel({
    required super.date,
    required super.status,
    required super.checkIn,
    required super.checkOut,
    required super.totalHours,
    required super.overtime,
    required super.sessionsCount,
    required super.hasMultipleSessions,
    required super.sessions,
  });

  /// Hàm factory để parse một mảnh JSON (tương ứng với một ngày)
  /// thành đối tượng `AttendanceDayDetailModel`.
  factory AttendanceDayDetailModel.fromJson(Map<String, dynamic> json) {
    // Parse sessions array
    final sessionsListFromJson = json['sessions'] as List? ?? [];
    final List<AttendanceSessionModel> parsedSessions = sessionsListFromJson
        .map(
          (sessionJson) => AttendanceSessionModel.fromJson(
            sessionJson as Map<String, dynamic>,
          ),
        )
        .toList();

    return AttendanceDayDetailModel(
      // Bắt buộc parse 'date' vì nó là cốt lõi
      date: DateTime.parse(json['date'] as String),
      status: json['status'] ?? 'Unknown',
      checkIn: json['checkIn'] ?? '--:--',
      checkOut: json['checkOut'] ?? '--:--',
      totalHours: json['totalHours'] ?? '--',
      overtime: json['overtime'] ?? '--',
      sessionsCount: json['sessionsCount'] ?? 0,
      hasMultipleSessions: json['hasMultipleSessions'] ?? false,
      sessions: parsedSessions,
    );
  }
}

//==============================================================
// MODEL CON: Đại diện cho phần tóm tắt ở cuối trang
//==============================================================
class MonthlySummaryForHistoryModel extends MonthlySummaryForHistory {
  const MonthlySummaryForHistoryModel({
    required super.workDays,
    required super.lateArrivals,
    required super.absences,
    required super.holidays,
  });

  /// Hàm factory để parse JSON cho phần 'summary'
  factory MonthlySummaryForHistoryModel.fromJson(Map<String, dynamic> json) {
    return MonthlySummaryForHistoryModel(
      workDays: json['workDays'] ?? 0,
      lateArrivals: json['lateArrivals'] ?? 0,
      absences: json['absences'] ?? 0,
      holidays: json['holidays'] ?? 0,
    );
  }
}

//==============================================================
// MODEL CHÍNH: Đại diện cho TOÀN BỘ đối tượng JSON trả về từ API
//               /api/attendance/monthly-details
//==============================================================
class MonthlyDetailsModel extends MonthlyDetails {
  // Constructor gọi đến constructor của lớp cha `MonthlyDetails`
  const MonthlyDetailsModel({
    required List<AttendanceDayDetailModel>
    dailyDetails, // Phải là list của Model con
    required MonthlySummaryForHistoryModel summary, // Phải là Model con
  }) : super(dailyDetails: dailyDetails, summary: summary);

  /// Hàm factory chính để parse toàn bộ JSON
  factory MonthlyDetailsModel.fromJson(Map<String, dynamic> json) {
    // 1. Xử lý danh sách `dailyDetails`
    // Lấy ra mảng JSON từ key 'dailyDetails', nếu không có thì trả về mảng rỗng
    final dailyDetailsListFromJson = json['dailyDetails'] as List? ?? [];

    // Dùng `map` để duyệt qua từng item trong mảng JSON
    // và gọi `AttendanceDayDetailModel.fromJson` để tạo đối tượng cho từng ngày
    final List<AttendanceDayDetailModel> parsedDailyDetails =
        dailyDetailsListFromJson
            .map(
              (itemJson) => AttendanceDayDetailModel.fromJson(
                itemJson as Map<String, dynamic>,
              ),
            )
            .toList();

    // 2. Xử lý đối tượng `summary`
    // Lấy ra đối tượng JSON từ key 'summary', nếu không có thì trả về object rỗng
    final summaryJson = json['summary'] as Map<String, dynamic>? ?? {};
    final MonthlySummaryForHistoryModel parsedSummary =
        MonthlySummaryForHistoryModel.fromJson(summaryJson);

    // 3. Tạo đối tượng `MonthlyDetailsModel` cuối cùng
    return MonthlyDetailsModel(
      dailyDetails: parsedDailyDetails,
      summary: parsedSummary,
    );
  }
}
