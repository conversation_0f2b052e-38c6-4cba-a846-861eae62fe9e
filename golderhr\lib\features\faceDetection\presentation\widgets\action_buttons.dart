// (File: lib/features/faceDetection/presentation/widgets/action_buttons.dart)
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/shared/widgets/responsive_spacer.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../../l10n/app_localizations.dart';
import '../../../../shared/widgets/show_custom_snackBar.dart';
import '../../domain/entities/attendance_status_entity.dart';
import '../cubit/face_checkin_cubit.dart';
import '../cubit/face_checkin_state.dart';
import 'package:iconsax/iconsax.dart';

class ActionButtons extends StatelessWidget {
  const ActionButtons({super.key});

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final l10n = AppLocalizations.of(context)!;

    return BlocConsumer<FaceDetectionCubit, FaceDetectionState>(
      listenWhen: (previous, current) {
        if (previous.status == current.status || current.status.isEmpty) {
          return false;
        }
        final nonSnackBarMessages = [
          l10n.initializing,
          l10n.refreshingStatus,
          l10n.statusUpdated,
          l10n.detectFaceOpeningCamera,
          l10n.detectFaceImageCapturedReady,
          l10n.detectFaceAnalyzingSecurity,
          l10n.detectFaceGettingLocation,
          l10n.imageCleared,
          l10n.notReadyForAction(l10n.checkIn),
          l10n.notReadyForAction(l10n.checkOut),

          l10n.detectFaceReadyForCheckIn,
          l10n.detectFaceReadyForCheckOut,
        ];
        if (nonSnackBarMessages.contains(current.status) ||
            current.status.startsWith(
              l10n.detectFaceFaceFoundCount(0).split(' ')[0],
            )) {
          return false;
        }
        return true;
      },
      listener: (context, state) {
        final bool isErrorStatus =
            state.status == l10n.detectFaceFaceNotFound ||
            state.status.toLowerCase().contains('error') ||
            state.status.toLowerCase().contains('fail') ||
            state.status.toLowerCase().contains('thất bại') ||
            state.status == l10n.locationServicesDisabled ||
            state.status == l10n.detectFaceLocationPermissionDenied ||
            state.status ==
                l10n.detectFaceLocationPermissionPermanentlyDenied ||
            state.status == l10n.detectFaceImageNotCaptured ||
            state.status.startsWith(l10n.notReadyForAction('').split(':')[0]);

        showTopSnackBar(
          context,
          title: isErrorStatus ? l10n.error : l10n.notification,
          message: state.status,
          // Giả sử hàm showTopSnackBar có tham số isError để đổi màu
          isError: isErrorStatus, // Đảm bảo truyền đúng
        );
      },
      buildWhen: (previous, current) =>
          previous.isLoading != current.isLoading ||
          previous.image != current.image ||
          previous.faces != current.faces ||
          previous.attendanceStatus != current.attendanceStatus ||
          previous.currentLocation != current.currentLocation ||
          previous.status != current.status,
      builder: (context, state) {
        final attendanceStatus = state.attendanceStatus;

        // 1. Loading ban đầu, chưa có dữ liệu gì
        if (state.isLoading &&
            attendanceStatus == null &&
            state.image == null) {
          return const Center(child: CircularProgressIndicator.adaptive());
        }

        // 2. Lỗi nghiêm trọng khi tải trạng thái ban đầu
        if (!state.isLoading &&
            attendanceStatus == null &&
            (state.status.toLowerCase().contains('error') ||
                state.status.toLowerCase().contains('fail'))) {
          return _buildErrorState(
            context,
            l10n,
            state.status.isNotEmpty ? state.status : l10n.errorLoadingData,
          );
        }

        // 3. Xử lý hiển thị dựa trên attendanceStatus
        if (attendanceStatus != null) {
          bool canPerformAnyAction =
              (attendanceStatus.nextAction == NextActionType.CHECK_IN &&
                  attendanceStatus.canCheckIn) ||
              (attendanceStatus.nextAction == NextActionType.CHECK_OUT &&
                  attendanceStatus.canCheckOut);

          if (canPerformAnyAction) {
            // Sẵn sàng cho Check In hoặc Check Out
            return _buildActualActionButtons(
              context: context,
              l10n: l10n,
              responsive: responsive,
              state: state,
              // Xác định isCheckInMode dựa trên nextAction và canCheckIn
              isCheckInMode:
                  (attendanceStatus.nextAction == NextActionType.CHECK_IN &&
                  attendanceStatus.canCheckIn),
            );
          } else {
            // Không có hành động CHECK_IN hoặc CHECK_OUT nào được phép
            // Hiển thị thông báo trạng thái từ Cubit (ví dụ: "Đã hoàn thành", "Không có hành động", lỗi,...)
            if (state.isLoading) {
              // Nếu vẫn đang loading trạng thái (ví dụ sau khi checkin/out xong)
              return const Center(child: CircularProgressIndicator.adaptive());
            }
            return _buildInfoOrErrorStateFromStatus(
              context,
              l10n,
              state.status.isNotEmpty ? state.status : l10n.noActionAvailable,
            );
          }
        }

        // 4. Nếu attendanceStatus là null và không loading, không có lỗi rõ ràng từ status
        // (Trường hợp này ít khi xảy ra nếu Cubit xử lý tốt, nhưng để phòng)
        if (!state.isLoading) {
          return _buildErrorState(
            context,
            l10n,
            l10n.errorLoadingAttendanceStatus,
          ); // Hoặc một thông báo chung
        }

        // 5. Fallback: Mặc định là loading nếu các điều kiện trên không được đáp ứng
        return const Center(child: CircularProgressIndicator.adaptive());
      },
    );
  }

  // Widget _buildInfoOrErrorStateFromStatus đã có ở câu trả lời trước, giữ nguyên
  Widget _buildInfoOrErrorStateFromStatus(
    BuildContext context,
    AppLocalizations l10n,
    String statusMessage,
  ) {
    bool isError =
        statusMessage.toLowerCase().contains('error') ||
        statusMessage.toLowerCase().contains('fail');
    // Đặc biệt xử lý các thông báo không phải lỗi
    if (statusMessage == l10n.allSessionsCompleted ||
        statusMessage ==
            l10n.detectFaceAttendanceCompleted || // Thêm key này nếu có
        statusMessage == l10n.noFurtherActionsAvailable ||
        statusMessage ==
            l10n.detectFaceReadyForCheckIn || // Hiển thị dưới dạng thông tin
        statusMessage == l10n.detectFaceReadyForCheckOut) {
      isError = false;
    }

    Color messageColor = isError
        ? Colors.red.shade800
        : Theme.of(context).textTheme.bodyLarge?.color ?? Colors.black;
    IconData statusIcon = isError ? Iconsax.warning_2 : Iconsax.info_circle;
    Color iconColor = isError ? Colors.red : Colors.blue;

    if (statusMessage == l10n.allSessionsCompleted ||
        statusMessage == l10n.detectFaceAttendanceCompleted) {
      statusIcon = Iconsax.task_square;
      iconColor = Colors.green;
      messageColor = Colors.green[700]!;
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(statusIcon, color: iconColor, size: 48),
          ResponsiveSpacer(mobileSize: 16, tabletSize: 20),
          Text(
            statusMessage,
            textAlign: TextAlign.center,
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(color: messageColor),
          ),
          if (isError) ...[
            // Chỉ hiển thị nút retry nếu là lỗi thực sự
            ResponsiveSpacer(mobileSize: 20, tabletSize: 24),
            ElevatedButton.icon(
              icon: const Icon(Iconsax.refresh),
              onPressed: () => context.read<FaceDetectionCubit>().initialize(),
              label: Text(l10n.retry),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber.shade700,
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // _buildErrorState đã có ở câu trả lời trước, giữ nguyên
  Widget _buildErrorState(
    BuildContext context,
    AppLocalizations l10n,
    String errorMessage,
  ) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Iconsax.warning_2, color: Colors.red, size: 48),
          ResponsiveSpacer(mobileSize: 16, tabletSize: 20),
          Text(
            errorMessage,
            textAlign: TextAlign.center,
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(color: Colors.red.shade800),
          ),
          ResponsiveSpacer(mobileSize: 20, tabletSize: 24),
          ElevatedButton.icon(
            icon: const Icon(Iconsax.refresh),
            onPressed: () => context.read<FaceDetectionCubit>().initialize(),
            label: Text(l10n.retry),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.amber.shade700,
              foregroundColor: Colors.black,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  // _buildActualActionButtons đã có ở câu trả lời trước, gần như giữ nguyên
  Widget _buildActualActionButtons({
    required BuildContext context,
    required FaceDetectionState state,
    required AppLocalizations l10n,
    required Responsive responsive,
    required bool isCheckInMode,
  }) {
    final attendanceStatus = state.attendanceStatus; // Lấy ra để dễ dùng
    final isCaptureButtonEnabled = !state.isLoading;
    final isConfirmButtonEnabled =
        !state.isLoading &&
        state.image != null &&
        state.faces.isNotEmpty &&
        state.currentLocation != null &&
        state.locationStatus.trim().isNotEmpty &&
        state.locationStatus != l10n.detectFaceGettingLocation;

    String confirmButtonText = isCheckInMode
        ? l10n.detectFaceConfirmCheckIn
        : l10n.detectFaceConfirmCheckOut;
    // Điều chỉnh text của nút confirm check-in nếu đây là một phiên mới sau các phiên đã hoàn thành
    if (isCheckInMode &&
        (attendanceStatus?.checkInsCount ?? 0) > 0 &&
        attendanceStatus?.checkInsCount == attendanceStatus?.checkOutsCount) {
      confirmButtonText = l10n.startNewSession;
    } else if (isCheckInMode &&
        (attendanceStatus?.checkInsCount ?? 0) > 0 &&
        (attendanceStatus?.checkOutsCount ?? 0) <
            (attendanceStatus?.checkInsCount ?? 0)) {
      // Đã check-in nhưng chưa check-out, và giờ lại hiện nút Check In? Lạ, nhưng nếu API cho phép thì hiện text check-in
      confirmButtonText = l10n.detectFaceConfirmCheckIn;
    }

    return Padding(
      padding: responsive.padding(horizontal: 16, vertical: 8),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: double.infinity,
            height: responsive.scaleHeight(56),
            child: ElevatedButton.icon(
              onPressed: isCaptureButtonEnabled
                  ? () => context.read<FaceDetectionCubit>().captureImage()
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.secondary,
                foregroundColor: Theme.of(context).colorScheme.onSecondary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                disabledBackgroundColor: Colors.grey.shade300,
              ),
              icon: Icon(
                state.image == null ? Iconsax.camera5 : Iconsax.repeat,
              ),
              label: Text(
                state.image == null
                    ? l10n.detectFaceCaptureImage
                    : l10n.detectFaceRetakeImage,
                style: TextStyle(
                  fontSize: responsive.fontSize(16),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          ResponsiveSpacer(mobileSize: 16, tabletSize: 18),
          SizedBox(
            width: double.infinity,
            height: responsive.scaleHeight(56),
            child: ElevatedButton.icon(
              onPressed: isConfirmButtonEnabled
                  ? () {
                      final cubit = context.read<FaceDetectionCubit>();
                      if (isCheckInMode) {
                        cubit.performCheckIn();
                      } else {
                        cubit.performCheckOut();
                      }
                    }
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: isCheckInMode
                    ? Colors.green.shade600
                    : Colors.orange.shade700,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                disabledBackgroundColor: Colors.grey.shade400,
              ),
              icon: Icon(isCheckInMode ? Iconsax.login_1 : Iconsax.logout_1),
              label: Text(
                confirmButtonText, // Sử dụng text đã được điều chỉnh
                style: TextStyle(
                  fontSize: responsive.fontSize(16),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          if (state.image != null && !state.isLoading) ...[
            ResponsiveSpacer(mobileSize: 8, tabletSize: 10),
            TextButton.icon(
              icon: Icon(Iconsax.trash, color: Colors.red.shade600, size: 20),
              onPressed: () =>
                  context.read<FaceDetectionCubit>().clearCapturedImage(),
              label: Text(
                l10n.clearImage,
                style: TextStyle(
                  color: Colors.red.shade700,
                  fontSize: responsive.fontSize(14),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
