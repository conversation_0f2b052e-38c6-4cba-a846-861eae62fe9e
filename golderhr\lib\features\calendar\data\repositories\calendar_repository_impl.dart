import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/calendar_event.dart';
import '../../domain/entities/calendar_summary.dart';
import '../../domain/repositories/calendar_repository.dart';
import '../datasources/calendar_local_data_source.dart';
import '../datasources/calendar_remote_data_source.dart';
import '../models/calendar_event_model.dart';

/// Implementation của CalendarRepository
class CalendarRepositoryImpl implements CalendarRepository {
  final CalendarRemoteDataSource remoteDataSource;
  final CalendarLocalDataSource localDataSource;
  final NetworkInfo networkInfo;

  CalendarRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, List<CalendarEvent>>> getAllEvents() async {
    try {
      if (await networkInfo.isConnected) {
        final remoteEvents = await remoteDataSource.getAllEvents();
        await localDataSource.cacheEvents(remoteEvents);
        return Right(remoteEvents.map((model) => model.toEntity()).toList());
      } else {
        final localEvents = await localDataSource.getCachedEvents();
        return Right(localEvents.map((model) => model.toEntity()).toList());
      }
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<CalendarEvent>>> getEventsByDate(
    DateTime date,
  ) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteEvents = await remoteDataSource.getEventsByDate(date);
        return Right(remoteEvents.map((model) => model.toEntity()).toList());
      } else {
        final localEvents = await localDataSource.getEventsByDate(date);
        return Right(localEvents.map((model) => model.toEntity()).toList());
      }
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<CalendarEvent>>> getEventsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteEvents = await remoteDataSource.getEventsByDateRange(
          startDate,
          endDate,
        );
        return Right(remoteEvents.map((model) => model.toEntity()).toList());
      } else {
        final localEvents = await localDataSource.getEventsByDateRange(
          startDate,
          endDate,
        );
        return Right(localEvents.map((model) => model.toEntity()).toList());
      }
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<CalendarEvent>>> getEventsByMonth(
    int year,
    int month,
  ) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteEvents = await remoteDataSource.getEventsByMonth(
          year,
          month,
        );
        return Right(remoteEvents.map((model) => model.toEntity()).toList());
      } else {
        final localEvents = await localDataSource.getEventsByMonth(year, month);
        return Right(localEvents.map((model) => model.toEntity()).toList());
      }
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<CalendarEvent>>> getEventsByWeek(
    DateTime startOfWeek,
  ) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteEvents = await remoteDataSource.getEventsByWeek(
          startOfWeek,
        );
        return Right(remoteEvents.map((model) => model.toEntity()).toList());
      } else {
        final localEvents = await localDataSource.getEventsByWeek(startOfWeek);
        return Right(localEvents.map((model) => model.toEntity()).toList());
      }
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<CalendarEvent>>> getUpcomingEvents({
    int limit = 10,
  }) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteEvents = await remoteDataSource.getUpcomingEvents(
          limit: limit,
        );
        return Right(remoteEvents.map((model) => model.toEntity()).toList());
      } else {
        final localEvents = await localDataSource.getUpcomingEvents(
          limit: limit,
        );
        return Right(localEvents.map((model) => model.toEntity()).toList());
      }
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<CalendarEvent>>> getEventsByType(
    EventType type,
  ) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteEvents = await remoteDataSource.getEventsByType(type);
        return Right(remoteEvents.map((model) => model.toEntity()).toList());
      } else {
        final localEvents = await localDataSource.getEventsByType(type);
        return Right(localEvents.map((model) => model.toEntity()).toList());
      }
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, CalendarEvent>> getEventById(String id) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteEvent = await remoteDataSource.getEventById(id);
        return Right(remoteEvent.toEntity());
      } else {
        final localEvent = await localDataSource.getEventById(id);
        return Right(localEvent.toEntity());
      }
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, CalendarEvent>> addEvent(CalendarEvent event) async {
    try {
      final eventModel = CalendarEventModel.fromEntity(event);
      if (await networkInfo.isConnected) {
        final remoteEvent = await remoteDataSource.addEvent(eventModel);
        await localDataSource.cacheEvent(remoteEvent);
        return Right(remoteEvent.toEntity());
      } else {
        final localEvent = await localDataSource.addEvent(eventModel);
        return Right(localEvent.toEntity());
      }
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, CalendarEvent>> updateEvent(
    CalendarEvent event,
  ) async {
    try {
      final eventModel = CalendarEventModel.fromEntity(event);
      if (await networkInfo.isConnected) {
        final remoteEvent = await remoteDataSource.updateEvent(eventModel);
        await localDataSource.updateEvent(remoteEvent);
        return Right(remoteEvent.toEntity());
      } else {
        final localEvent = await localDataSource.updateEvent(eventModel);
        return Right(localEvent.toEntity());
      }
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> deleteEvent(String id) async {
    try {
      if (await networkInfo.isConnected) {
        await remoteDataSource.deleteEvent(id);
        await localDataSource.deleteEvent(id);
      } else {
        await localDataSource.deleteEvent(id);
      }
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> deleteEvents(List<String> ids) async {
    try {
      if (await networkInfo.isConnected) {
        await remoteDataSource.deleteEvents(ids);
        await localDataSource.deleteEvents(ids);
      } else {
        await localDataSource.deleteEvents(ids);
      }
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<CalendarEvent>>> searchEvents(
    String query,
  ) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteEvents = await remoteDataSource.searchEvents(query);
        return Right(remoteEvents.map((model) => model.toEntity()).toList());
      } else {
        final localEvents = await localDataSource.searchEvents(query);
        return Right(localEvents.map((model) => model.toEntity()).toList());
      }
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, CalendarSummary>> getCalendarSummary() async {
    try {
      if (await networkInfo.isConnected) {
        final remoteSummary = await remoteDataSource.getCalendarSummary();
        return Right(remoteSummary.toEntity());
      } else {
        final localSummary = await localDataSource.getCalendarSummary();
        return Right(localSummary.toEntity());
      }
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, CalendarSummary>> getMonthlySummary(
    int year,
    int month,
  ) async {
    try {
      if (await networkInfo.isConnected) {
        final remoteSummary = await remoteDataSource.getMonthlySummary(
          year,
          month,
        );
        return Right(remoteSummary.toEntity());
      } else {
        final localSummary = await localDataSource.getMonthlySummary(
          year,
          month,
        );
        return Right(localSummary.toEntity());
      }
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<CalendarEvent>>> syncEvents() async {
    try {
      final remoteEvents = await remoteDataSource.getAllEvents();
      await localDataSource.clearCache();
      await localDataSource.cacheEvents(remoteEvents);
      return Right(remoteEvents.map((model) => model.toEntity()).toList());
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> cacheEvents(List<CalendarEvent> events) async {
    try {
      final eventModels = events
          .map((event) => CalendarEventModel.fromEntity(event))
          .toList();
      await localDataSource.cacheEvents(eventModels);
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<CalendarEvent>>> getCachedEvents() async {
    try {
      final localEvents = await localDataSource.getCachedEvents();
      return Right(localEvents.map((model) => model.toEntity()).toList());
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> clearCache() async {
    try {
      await localDataSource.clearCache();
      return const Right(null);
    } catch (e) {
      return Left(CacheFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> hasEventsOnDate(DateTime date) async {
    try {
      final events = await getEventsByDate(date);
      return events.fold(
        (failure) => Left(failure),
        (eventList) => Right(eventList.isNotEmpty),
      );
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<EventType, int>>> getEventCountByType() async {
    try {
      if (await networkInfo.isConnected) {
        final remoteCount = await remoteDataSource.getEventCountByType();
        return Right(remoteCount);
      } else {
        final localCount = await localDataSource.getEventCountByType();
        return Right(localCount);
      }
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, String>> exportEvents({
    DateTime? startDate,
    DateTime? endDate,
    List<EventType>? types,
  }) async {
    try {
      final filePath = await localDataSource.exportEvents(
        startDate: startDate,
        endDate: endDate,
        types: types,
      );
      return Right(filePath);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<CalendarEvent>>> importEvents(
    String filePath,
  ) async {
    try {
      final eventModels = await localDataSource.importEvents(filePath);
      final events = eventModels.map((model) => model.toEntity()).toList();

      // Sync với server nếu có kết nối
      if (await networkInfo.isConnected) {
        for (final event in events) {
          await addEvent(event);
        }
      }

      return Right(events);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}
