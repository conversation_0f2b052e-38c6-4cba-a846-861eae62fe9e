import 'package:flutter/material.dart';
import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/extensions/responsive_extension.dart';
import '../../../../shared/theme/app_colors.dart';

/// Widget TabBar cho Calendar với design hiện đại
class CalendarTabBarWidget extends StatelessWidget {
  final TabController tabController;

  const CalendarTabBarWidget({super.key, required this.tabController});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return Container(
      margin: context.responsive.padding(horizontal: 16, bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: TabBar(
          controller: tabController,
          labelColor: AppColors.primaryBlue,
          unselectedLabelColor: AppColors.textSecondary,
          labelStyle: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
          unselectedLabelStyle: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.normal),
          indicator: BoxDecoration(
            color: AppColors.primaryBlue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          indicatorPadding: EdgeInsets.all(context.rw(4)),
          dividerColor: Colors.transparent,
          tabs: [
            _buildTab(context, l10n.calendarTabMonth, Icons.calendar_month),
            _buildTab(context, l10n.calendarTabWeek, Icons.view_week),
            _buildTab(context, l10n.calendarTabAgenda, Icons.list_alt),
          ],
        ),
      ),
    );
  }

  Widget _buildTab(BuildContext context, String text, IconData icon) {
    return Tab(
      height: context.rh(48),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: context.rf(18)),
          SizedBox(width: context.rw(8)),
          Text(text),
        ],
      ),
    );
  }
}
