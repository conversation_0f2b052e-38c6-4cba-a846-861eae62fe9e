import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/overtime_request_entity.dart';
import '../repositories/overtime_repository.dart';

class SubmitOvertimeRequest
    implements UseCase<OvertimeRequestEntity, SubmitOvertimeParams> {
  final OvertimeRepository repository;

  SubmitOvertimeRequest(this.repository);

  @override
  Future<Either<Failure, OvertimeRequestEntity>> call(
    SubmitOvertimeParams params,
  ) async {
    return await repository.submitOvertimeRequest(
      date: params.date,
      startTime: params.startTime,
      endTime: params.endTime,
      reason: params.reason,
      type: params.type,
      approverId: params.approverId,
    );
  }
}

class SubmitOvertimeParams extends Equatable {
  final DateTime date;
  final DateTime startTime;
  final DateTime endTime;
  final String reason;
  final OvertimeType type;
  final String? approverId;

  const SubmitOvertimeParams({
    required this.date,
    required this.startTime,
    required this.endTime,
    required this.reason,
    required this.type,
    this.approverId,
  });

  @override
  List<Object?> get props => [
    date,
    startTime,
    endTime,
    reason,
    type,
    approverId,
  ];
}
