import 'dart:io';

import 'package:dio/dio.dart'; // <PERSON><PERSON><PERSON> bảo import dio
import 'package:golderhr/core/network/dio_client.dart';

import '../../../../core/error/exceptions.dart';
import '../../../../core/logger/app_logger.dart';
import '../models/attendance_record_model.dart';
import '../models/attendance_status_model.dart';
import '../models/location_model.dart';

abstract class AttendanceRemoteDataSource {
  Future<AttendanceRecordModel> checkIn({
    required File image,
    required LocationModel location,
  });

  Future<AttendanceRecordModel> checkOut({
    required File image,
    required LocationModel location,
  });

  Future<AttendanceStatusModel?> getTodayAttendance();
}

class AttendanceRemoteDataSourceImpl implements AttendanceRemoteDataSource {
  final DioClient dioClient;

  AttendanceRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<AttendanceRecordModel> checkIn({
    required File image,
    required LocationModel location,
  }) async {
    final formData = FormData.fromMap({
      'image': await MultipartFile.fromFile(
        image.path,
        filename: image.path.split('/').last,
      ),
      'location': location.toJsonString(),
    });

    AppLogger.info("image path: ${image.path}");
    AppLogger.info("location JSON: ${location.toJsonString()}");

    try {
      final response = await dioClient.post(
        "/api/attendance/check-in",
        data: formData,
      );

      final attendanceData = response.data?['data']?['attendance'];
      if (attendanceData != null) {
        return AttendanceRecordModel.fromJson(attendanceData);
      } else {
        // Nếu cấu trúc không đúng như mong đợi, ném lỗi
        throw ServerException('Invalid data structure from check-in response.');
      }
    } on DioException catch (e) {
      AppLogger.error("DioException during check-in: ${e.response?.data}");
      // Ném lỗi đã được chuẩn hóa
      throw ServerException(
        e.response?.data['message'] ?? 'Check-in failed. Please try again.',
      );
    } catch (e) {
      AppLogger.error("Unexpected error during check-in: $e");
      throw ServerException('An unexpected error occurred during check-in.');
    }
  }

  @override
  Future<AttendanceRecordModel> checkOut({
    required File image,
    required LocationModel location,
  }) async {
    // Tương tự cho check-out
    final formData = FormData.fromMap({
      'image': await MultipartFile.fromFile(
        image.path,
        filename: image.path.split('/').last,
      ),
      'location': location.toJsonString(),
    });

    try {
      final response = await dioClient.post(
        "/api/attendance/check-out",
        data: formData,
      );
      return AttendanceRecordModel.fromJson(
        response.data['data']['attendance'],
      );
    } on DioException catch (e) {
      throw ServerException(
        e.response?.data['message'] ?? 'An unknown server error occurred.',
      );
    } catch (e) {
      throw ServerException('An unexpected error occurred: $e');
    }
  }

  @override
  Future<AttendanceStatusModel?> getTodayAttendance() async {
    try {
      final response = await dioClient.get("/api/attendance/check-status");

      // SỬA LỖI PARSING Ở ĐÂY
      // Dữ liệu nằm trong response.data['data'], không phải response.data['data']['attendance']
      final statusData = response.data['data'];

      if (statusData != null) {
        // response.data['success'] == true cũng có thể là một điều kiện tốt
        return AttendanceStatusModel.fromJson(statusData);
      }
      // API trả về 200 nhưng không có data thì cũng coi như chưa có record
      return null;
    } on DioException catch (e) {
      // API trả về 404 hoặc các lỗi khác không thành công thì coi như chưa có record.
      // Backend nên trả về 200 với hasCheckedIn: false thay vì 404.
      // Nhưng với logic hiện tại của bạn, chúng ta coi 404 là chưa check-in.
      AppLogger.error("DioException in getTodayAttendance: ${e.message}");
      return null;
    } catch (e) {
      AppLogger.error("Unexpected error in getTodayAttendance: $e");
      // Ném lỗi để Repository/UseCase có thể bắt và trả về Failure
      throw ServerException('An unexpected error occurred: $e');
    }
  }
}
