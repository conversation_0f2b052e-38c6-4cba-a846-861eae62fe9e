import 'dart:io';
import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/user_for_dropdown_entity.dart';
import '../../domain/reponsitories/upload_face_repository.dart';

import '../datasources/upload_face_remote_data_source.dart';

class UploadFaceRepositoryImpl implements UploadFaceRepository {
  final UploadFaceRemoteDataSource remoteDataSource;

  UploadFaceRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<UserForDropdownEntity>>>
  getUsersForDropdown() async {
    try {
      final remoteUsers = await remoteDataSource.getUsersForDropdown();
      return Right(remoteUsers);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }

  @override
  Future<Either<Failure, String>> uploadEmployeeFace({
    required String userId,
    required File imageFile,
  }) async {
    try {
      final imageUrl = await remoteDataSource.uploadEmployeeFace(
        userId: userId,
        imageFile: imageFile,
      );
      return Right(imageUrl);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error: $e'));
    }
  }
}
