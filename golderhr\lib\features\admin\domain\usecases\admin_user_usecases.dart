import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/admin_user_entity.dart';
import '../repositories/admin_user_repository.dart';

// Get User By ID
class GetUserByIdUseCase implements UseCase<AdminUserEntity, String> {
  final AdminUserRepository repository;

  GetUserByIdUseCase(this.repository);

  @override
  Future<Either<Failure, AdminUserEntity>> call(String userId) async {
    return await repository.getUserById(userId);
  }
}

// Update User
class UpdateUserUseCase implements UseCase<AdminUserEntity, UpdateUserParams> {
  final AdminUserRepository repository;

  UpdateUserUseCase(this.repository);

  @override
  Future<Either<Failure, AdminUserEntity>> call(UpdateUserParams params) async {
    return await repository.updateUser(
      params.userId,
      fullname: params.fullname,
      email: params.email,
      phone: params.phone,
      department: params.department,
      position: params.position,
      role: params.role,
      organization: params.organization,
      isdisable: params.isdisable,
    );
  }
}

class UpdateUserParams {
  final String userId;
  final String? fullname;
  final String? email;
  final String? phone;
  final String? department;
  final String? position;
  final String? role;
  final String? organization;
  final bool? isdisable;

  UpdateUserParams({
    required this.userId,
    this.fullname,
    this.email,
    this.phone,
    this.department,
    this.position,
    this.role,
    this.organization,
    this.isdisable,
  });
}

// Soft Delete User
class SoftDeleteUserUseCase implements UseCase<AdminUserEntity, String> {
  final AdminUserRepository repository;

  SoftDeleteUserUseCase(this.repository);

  @override
  Future<Either<Failure, AdminUserEntity>> call(String userId) async {
    return await repository.softDeleteUser(userId);
  }
}

// Restore User
class RestoreUserUseCase implements UseCase<AdminUserEntity, String> {
  final AdminUserRepository repository;

  RestoreUserUseCase(this.repository);

  @override
  Future<Either<Failure, AdminUserEntity>> call(String userId) async {
    return await repository.restoreUser(userId);
  }
}

// Toggle User Status
class ToggleUserStatusUseCase implements UseCase<AdminUserEntity, String> {
  final AdminUserRepository repository;

  ToggleUserStatusUseCase(this.repository);

  @override
  Future<Either<Failure, AdminUserEntity>> call(String userId) async {
    return await repository.toggleUserStatus(userId);
  }
}

// Reset User Password
class ResetUserPasswordUseCase implements UseCase<void, ResetPasswordParams> {
  final AdminUserRepository repository;

  ResetUserPasswordUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(ResetPasswordParams params) async {
    return await repository.resetUserPassword(params.userId, params.newPassword);
  }
}

class ResetPasswordParams {
  final String userId;
  final String newPassword;

  ResetPasswordParams({
    required this.userId,
    required this.newPassword,
  });
}

// Get User Statistics
class GetUserStatisticsUseCase implements UseCase<UserStatistics, NoParams> {
  final AdminUserRepository repository;

  GetUserStatisticsUseCase(this.repository);

  @override
  Future<Either<Failure, UserStatistics>> call(NoParams params) async {
    return await repository.getUserStatistics();
  }
}

// Bulk Delete Users
class BulkDeleteUsersUseCase implements UseCase<BulkOperationResult, List<String>> {
  final AdminUserRepository repository;

  BulkDeleteUsersUseCase(this.repository);

  @override
  Future<Either<Failure, BulkOperationResult>> call(List<String> userIds) async {
    return await repository.bulkDeleteUsers(userIds);
  }
}

// Bulk Restore Users
class BulkRestoreUsersUseCase implements UseCase<BulkOperationResult, List<String>> {
  final AdminUserRepository repository;

  BulkRestoreUsersUseCase(this.repository);

  @override
  Future<Either<Failure, BulkOperationResult>> call(List<String> userIds) async {
    return await repository.bulkRestoreUsers(userIds);
  }
}
