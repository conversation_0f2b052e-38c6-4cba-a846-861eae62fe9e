import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../domain/entities/role_entity.dart';
import '../../domain/usecases/role_usecases.dart';
import '../../data/models/role_model.dart';
import '../../../../core/usecases/usecase.dart';

// Role State
class RoleState extends Equatable {
  final bool isLoading;
  final bool isProcessing;
  final List<RoleEntity> roles;
  final List<RoleEntity> dropdownRoles;
  final RoleListResult? roleListResult;
  final RoleEntity? selectedRole;
  final String? error;
  final String? successMessage;
  final int currentPage;
  final bool hasReachedMax;

  const RoleState({
    this.isLoading = false,
    this.isProcessing = false,
    this.roles = const [],
    this.dropdownRoles = const [],
    this.roleListResult,
    this.selectedRole,
    this.error,
    this.successMessage,
    this.currentPage = 1,
    this.hasReachedMax = false,
  });

  RoleState copyWith({
    bool? isLoading,
    bool? isProcessing,
    List<RoleEntity>? roles,
    List<RoleEntity>? dropdownRoles,
    RoleListResult? roleListResult,
    RoleEntity? selectedRole,
    String? error,
    String? successMessage,
    int? currentPage,
    bool? hasReachedMax,
  }) {
    return RoleState(
      isLoading: isLoading ?? this.isLoading,
      isProcessing: isProcessing ?? this.isProcessing,
      roles: roles ?? this.roles,
      dropdownRoles: dropdownRoles ?? this.dropdownRoles,
      roleListResult: roleListResult ?? this.roleListResult,
      selectedRole: selectedRole ?? this.selectedRole,
      error: error,
      successMessage: successMessage,
      currentPage: currentPage ?? this.currentPage,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
    );
  }

  bool get hasError => error != null;
  bool get hasSuccess => successMessage != null;

  @override
  List<Object?> get props => [
    isLoading,
    isProcessing,
    roles,
    dropdownRoles,
    roleListResult,
    selectedRole,
    error,
    successMessage,
    currentPage,
    hasReachedMax,
  ];
}

// Role Cubit
class RoleCubit extends Cubit<RoleState> {
  final GetAllRoles getAllRoles;
  final GetRoleById getRoleById;
  final CreateRole createRole;
  final UpdateRole updateRole;
  final DeleteRole deleteRole;
  final GetRolesForDropdown getRolesForDropdown;

  RoleCubit({
    required this.getAllRoles,
    required this.getRoleById,
    required this.createRole,
    required this.updateRole,
    required this.deleteRole,
    required this.getRolesForDropdown,
  }) : super(const RoleState());

  // Get all roles with pagination
  Future<void> loadRoles({
    int page = 1,
    int limit = 10,
    String? search,
    bool refresh = false,
  }) async {
    if (refresh) {
      emit(
        state.copyWith(
          isLoading: true,
          error: null,
          currentPage: 1,
          hasReachedMax: false,
        ),
      );
    } else if (page == 1) {
      emit(state.copyWith(isLoading: true, error: null));
    }

    final result = await getAllRoles(
      GetAllRolesParams(page: page, limit: limit, search: search),
    );

    result.fold(
      (failure) =>
          emit(state.copyWith(isLoading: false, error: failure.message)),
      (roleListResult) {
        final newRoles = page == 1
            ? roleListResult.roles.cast<RoleEntity>()
            : [...state.roles, ...roleListResult.roles.cast<RoleEntity>()];

        emit(
          state.copyWith(
            isLoading: false,
            roles: newRoles,
            roleListResult: roleListResult,
            currentPage: page,
            hasReachedMax: roleListResult.roles.length < limit,
            error: null,
          ),
        );
      },
    );
  }

  // Load more roles for pagination
  Future<void> loadMoreRoles({String? search}) async {
    if (state.hasReachedMax || state.isLoading) return;

    await loadRoles(page: state.currentPage + 1, search: search);
  }

  // Get role by ID
  Future<void> loadRoleById(String roleId) async {
    emit(state.copyWith(isLoading: true, error: null));

    final result = await getRoleById(roleId);

    result.fold(
      (failure) =>
          emit(state.copyWith(isLoading: false, error: failure.message)),
      (role) => emit(
        state.copyWith(isLoading: false, selectedRole: role, error: null),
      ),
    );
  }

  // Create new role
  Future<void> createNewRole({required String name}) async {
    emit(state.copyWith(isProcessing: true, error: null));

    final result = await createRole(CreateRoleParams(name: name));

    result.fold(
      (failure) =>
          emit(state.copyWith(isProcessing: false, error: failure.message)),
      (role) {
        final updatedRoles = [role, ...state.roles];
        emit(
          state.copyWith(
            isProcessing: false,
            roles: updatedRoles,
            successMessage: 'Role created successfully',
            error: null,
          ),
        );
      },
    );
  }

  // Update role
  Future<void> updateExistingRole({
    required String roleId,
    String? name,
  }) async {
    emit(state.copyWith(isProcessing: true, error: null));

    final result = await updateRole(
      UpdateRoleParams(roleId: roleId, name: name),
    );

    result.fold(
      (failure) =>
          emit(state.copyWith(isProcessing: false, error: failure.message)),
      (updatedRole) {
        final updatedRoles = state.roles.map((role) {
          return role.id == roleId ? updatedRole : role;
        }).toList();

        emit(
          state.copyWith(
            isProcessing: false,
            roles: updatedRoles,
            selectedRole: updatedRole,
            successMessage: 'Role updated successfully',
            error: null,
          ),
        );
      },
    );
  }

  // Delete role
  Future<void> deleteExistingRole(String roleId) async {
    emit(state.copyWith(isProcessing: true, error: null));

    final result = await deleteRole(roleId);

    result.fold(
      (failure) =>
          emit(state.copyWith(isProcessing: false, error: failure.message)),
      (_) {
        final updatedRoles = state.roles
            .where((role) => role.id != roleId)
            .toList();
        emit(
          state.copyWith(
            isProcessing: false,
            roles: updatedRoles,
            successMessage: 'Role deleted successfully',
            error: null,
          ),
        );
      },
    );
  }

  // Load roles for dropdown
  Future<void> loadRolesForDropdown() async {
    final result = await getRolesForDropdown(NoParams());

    result.fold(
      (failure) => emit(state.copyWith(error: failure.message)),
      (roles) => emit(state.copyWith(dropdownRoles: roles, error: null)),
    );
  }

  // Clear messages
  void clearMessages() {
    emit(state.copyWith(error: null, successMessage: null));
  }

  // Clear selected role
  void clearSelectedRole() {
    emit(state.copyWith(selectedRole: null));
  }

  // Refresh roles
  Future<void> refreshRoles({String? search}) async {
    await loadRoles(page: 1, search: search, refresh: true);
  }
}
