import 'package:flutter/material.dart';
import '../../core/responsive/responsive.dart';

/// Widget để tạo layout responsive cho nhiều thiết bị
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? mobileLandscape;
  final Widget? tabletLandscape;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.mobileLandscape,
    this.tabletLandscape,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    
    return responsive.adaptiveValue<Widget>(
      mobile: mobile,
      tablet: tablet ?? mobile,
      mobileLandscape: mobileLandscape ?? mobile,
      tabletLandscape: tabletLandscape ?? tablet ?? mobile,
    );
  }
}

/// Widget để tạo grid responsive
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double spacing;
  final double runSpacing;
  final EdgeInsets? padding;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.spacing = 16,
    this.runSpacing = 16,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final columns = responsive.gridColumns;
    
    return Padding(
      padding: padding ?? responsive.responsivePadding,
      child: responsive.isLandscape 
        ? _buildLandscapeLayout(responsive, columns)
        : _buildPortraitLayout(responsive, columns),
    );
  }

  Widget _buildPortraitLayout(Responsive responsive, int columns) {
    if (columns == 1) {
      return Column(
        children: children.map((child) => 
          Padding(
            padding: EdgeInsets.only(bottom: runSpacing),
            child: child,
          )
        ).toList(),
      );
    }
    
    return Wrap(
      spacing: spacing,
      runSpacing: runSpacing,
      children: children.map((child) => 
        SizedBox(
          width: (responsive.width - responsive.responsivePadding.horizontal - spacing * (columns - 1)) / columns,
          child: child,
        )
      ).toList(),
    );
  }

  Widget _buildLandscapeLayout(Responsive responsive, int columns) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left panel cho landscape
        if (responsive.isTablet)
          Expanded(
            flex: 2,
            child: Column(
              children: children.take(children.length ~/ 2).map((child) => 
                Padding(
                  padding: EdgeInsets.only(bottom: runSpacing),
                  child: child,
                )
              ).toList(),
            ),
          ),
        if (responsive.isTablet) SizedBox(width: spacing),
        // Right panel cho landscape
        Expanded(
          flex: responsive.isTablet ? 3 : 1,
          child: Column(
            children: (responsive.isTablet 
              ? children.skip(children.length ~/ 2) 
              : children
            ).map((child) => 
              Padding(
                padding: EdgeInsets.only(bottom: runSpacing),
                child: child,
              )
            ).toList(),
          ),
        ),
      ],
    );
  }
}

/// Widget cho responsive card
class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Color? color;
  final double? elevation;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.color,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    
    return Container(
      margin: margin ?? EdgeInsets.symmetric(
        vertical: responsive.defaultSpacing / 2,
      ),
      child: Card(
        elevation: elevation ?? (responsive.isTablet ? 4 : 2),
        color: color,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(responsive.defaultRadius),
        ),
        child: Padding(
          padding: padding ?? responsive.cardPadding,
          child: child,
        ),
      ),
    );
  }
}

/// Widget cho responsive text
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final ResponsiveTextType type;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.type = ResponsiveTextType.body,
  });

  const ResponsiveText.title(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  }) : type = ResponsiveTextType.title;

  const ResponsiveText.subtitle(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  }) : type = ResponsiveTextType.subtitle;

  const ResponsiveText.caption(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  }) : type = ResponsiveTextType.caption;

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final fontSize = _getFontSize(responsive);
    
    return Text(
      text,
      style: (style ?? const TextStyle()).copyWith(
        fontSize: fontSize,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }

  double _getFontSize(Responsive responsive) {
    switch (type) {
      case ResponsiveTextType.title:
        return responsive.titleFontSize;
      case ResponsiveTextType.subtitle:
        return responsive.subtitleFontSize;
      case ResponsiveTextType.body:
        return responsive.bodyFontSize;
      case ResponsiveTextType.caption:
        return responsive.captionFontSize;
    }
  }
}

enum ResponsiveTextType {
  title,
  subtitle,
  body,
  caption,
}
