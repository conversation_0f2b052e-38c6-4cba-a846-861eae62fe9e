import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../../l10n/app_localizations.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/overtime_request_entity.dart';
import '../cubit/overtime_cubit.dart';
import '../cubit/overtime_state.dart';
import '../pages/overtime_history_page.dart';
import '../pages/overtime_detail_page.dart';

class MyOvertimeTab extends StatefulWidget {
  const MyOvertimeTab({super.key});

  @override
  State<MyOvertimeTab> createState() => _MyOvertimeTabState();
}

class _MyOvertimeTabState extends State<MyOvertimeTab>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    // Start animations
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final theme = Theme.of(context);
    final l10n = context.l10n;

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: SingleChildScrollView(
          padding: responsive.padding(all: 16),
          child: Column(
            children: [
              SizedBox(height: responsive.heightPercentage(1)),

              // Summary Stats Cards
              BlocBuilder<OvertimeCubit, OvertimeState>(
                builder: (context, state) {
                  return _buildSummaryCards(responsive, theme, l10n, state);
                },
              ),

              SizedBox(height: responsive.heightPercentage(2)),

              // Recent Requests Preview
              BlocBuilder<OvertimeCubit, OvertimeState>(
                builder: (context, state) {
                  if (state.isLoadingHistory && state.history.isEmpty) {
                    return _buildLoadingState(responsive, l10n);
                  }

                  if (state.history.isEmpty) {
                    return _buildEmptyState(responsive, theme, l10n);
                  }

                  return _buildRecentRequestsSection(
                    responsive,
                    theme,
                    l10n,
                    state,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryCards(
    Responsive responsive,
    ThemeData theme,
    AppLocalizations l10n,
    OvertimeState state,
  ) {
    final totalRequests = state.history.length;
    final pendingRequests = state.history
        .where((r) => r.status == OvertimeStatus.pending)
        .length;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: l10n.totalRequests,
            value: totalRequests.toString(),
            icon: Icons.schedule_rounded,
            color: AppColors.primaryBlue,
            responsive: responsive,
            theme: theme,
          ),
        ),
        SizedBox(width: responsive.widthPercentage(3)),
        Expanded(
          child: _buildStatCard(
            title: l10n.pending,
            value: pendingRequests.toString(),
            icon: Icons.pending_actions_rounded,
            color: AppColors.warning,
            responsive: responsive,
            theme: theme,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required Responsive responsive,
    required ThemeData theme,
  }) {
    return Container(
      padding: responsive.padding(all: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.1), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
              Text(
                value,
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          SizedBox(height: responsive.heightPercentage(1)),
          Text(
            title,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentRequestsSection(
    Responsive responsive,
    ThemeData theme,
    AppLocalizations l10n,
    OvertimeState state,
  ) {
    final recentRequests = state.history.take(5).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                l10n.recentRequests,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            TextButton.icon(
              onPressed: () {
                // Get the cubit before navigation
                final overtimeCubit = context.read<OvertimeCubit>();

                // Navigate to history page with BlocProvider
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => BlocProvider.value(
                      value: overtimeCubit,
                      child: const OvertimeHistoryPage(),
                    ),
                  ),
                );
              },
              icon: const Icon(Icons.arrow_forward_rounded, size: 16),
              label: Text(l10n.viewAll),
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primaryBlue,
                textStyle: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: responsive.heightPercentage(1)),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: AppColors.primaryBlue.withValues(alpha: 0.08),
                blurRadius: 20,
                offset: const Offset(0, 4),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: recentRequests.asMap().entries.map((entry) {
              final index = entry.key;
              final request = entry.value;
              return Column(
                children: [
                  _buildSimpleRequestItem(request, responsive, theme, l10n),
                  if (index < recentRequests.length - 1)
                    Divider(
                      height: 1,
                      indent: responsive.widthPercentage(4),
                      endIndent: responsive.widthPercentage(4),
                      color: AppColors.textSecondary.withValues(alpha: 0.1),
                    ),
                ],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildSimpleRequestItem(
    OvertimeRequestEntity request,
    Responsive responsive,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    final statusColor = _getStatusColor(request.status);
    final statusText = _getStatusText(request.status, l10n);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _navigateToDetail(request),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: responsive.padding(all: 16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.schedule_rounded,
                  color: statusColor,
                  size: 20,
                ),
              ),
              SizedBox(width: responsive.widthPercentage(3)),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${request.date.day}/${request.date.month}/${request.date.year}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: responsive.heightPercentage(0.5)),
                    Text(
                      '${_formatTime(request.startTime)} - ${_formatTime(request.endTime)}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: statusColor.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      statusText,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: statusColor,
                        fontWeight: FontWeight.bold,
                        fontSize: responsive.fontSize(10),
                      ),
                    ),
                  ),
                  SizedBox(height: responsive.heightPercentage(0.5)),
                  Text(
                    '${request.hours.toStringAsFixed(1)}h',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              SizedBox(width: responsive.widthPercentage(2)),
              Icon(
                Icons.arrow_forward_ios_rounded,
                size: 14,
                color: AppColors.textSecondary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToDetail(OvertimeRequestEntity request) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OvertimeDetailPage(request: request),
      ),
    );
  }

  Widget _buildLoadingState(Responsive responsive, AppLocalizations l10n) {
    return Container(
      padding: responsive.padding(all: 40),
      child: Column(
        children: [
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 1000),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.scale(
                scale: 0.8 + (0.2 * value),
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppColors.primaryBlue.withValues(alpha: value),
                  ),
                ),
              );
            },
          ),
          SizedBox(height: responsive.heightPercentage(2)),
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 800),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Opacity(
                opacity: value,
                child: Text(
                  l10n.loadingOvertimeHistory,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(
    Responsive responsive,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1200),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              padding: responsive.padding(all: 40),
              child: Column(
                children: [
                  TweenAnimationBuilder<double>(
                    duration: const Duration(milliseconds: 2000),
                    tween: Tween(begin: 0.0, end: 1.0),
                    builder: (context, iconValue, child) {
                      return Transform.scale(
                        scale: 0.8 + (0.2 * iconValue),
                        child: Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: AppColors.primaryBlue.withValues(alpha: 0.1),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.schedule_rounded,
                            size: responsive.scaleWidth(60),
                            color: AppColors.primaryBlue,
                          ),
                        ),
                      );
                    },
                  ),
                  SizedBox(height: responsive.heightPercentage(3)),
                  Text(
                    l10n.noOvertimeRequests,
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: responsive.heightPercentage(1)),
                  Text(
                    l10n.submitYourFirstOvertimeRequest,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: responsive.heightPercentage(3)),
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    child: ElevatedButton.icon(
                      onPressed: () {
                        // Navigate to new request tab (index 0)
                        DefaultTabController.of(context).animateTo(0);
                      },
                      icon: const Icon(Icons.add_rounded),
                      label: Text(l10n.createRequest),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryBlue,
                        foregroundColor: Colors.white,
                        padding: responsive.padding(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Color _getStatusColor(OvertimeStatus status) {
    switch (status) {
      case OvertimeStatus.approved:
        return Colors.green;
      case OvertimeStatus.rejected:
        return Colors.red;
      case OvertimeStatus.pending:
        return Colors.orange;
    }
  }

  String _getStatusText(OvertimeStatus status, AppLocalizations l10n) {
    switch (status) {
      case OvertimeStatus.approved:
        return l10n.approved;
      case OvertimeStatus.rejected:
        return l10n.rejected;
      case OvertimeStatus.pending:
        return l10n.pending;
    }
  }

  String _formatTime(DateTime time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
}
