import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/auth_repository.dart';

// === SỬA LẠI ĐỊNH NGHĨA USECASE ===
// Nó trả về một String (resetToken) và nhận vào VerifyOtpParams
class VerifyOtpUseCase implements UseCase<String, VerifyOtpParams> {
  final AuthRepository repository;

  VerifyOtpUseCase(this.repository);

  @override
  Future<Either<Failure, String>> call(VerifyOtpParams params) async {
    return await repository.verifyOtp(params.otp);
  }
}

// Params không đổi
class VerifyOtpParams extends Equatable {
  final String otp;
  const VerifyOtpParams({required this.otp});
  @override
  List<Object> get props => [otp];
}
