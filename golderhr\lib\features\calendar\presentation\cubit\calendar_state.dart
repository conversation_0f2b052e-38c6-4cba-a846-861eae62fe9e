import 'package:equatable/equatable.dart';
import '../../domain/entities/calendar_event.dart';
import '../../domain/entities/calendar_summary.dart';

/// Base state cho Calendar
abstract class CalendarState extends Equatable {
  const CalendarState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class CalendarInitial extends CalendarState {}

/// Loading state
class CalendarLoading extends CalendarState {}

/// State khi load events thành công
class CalendarEventsLoaded extends CalendarState {
  final List<CalendarEvent> events;
  final DateTime selectedDate;
  final DateTime currentMonth;
  final CalendarSummary? summary;

  const CalendarEventsLoaded({
    required this.events,
    required this.selectedDate,
    required this.currentMonth,
    this.summary,
  });

  @override
  List<Object?> get props => [events, selectedDate, currentMonth, summary];

  CalendarEventsLoaded copyWith({
    List<CalendarEvent>? events,
    DateTime? selectedDate,
    DateTime? currentMonth,
    CalendarSummary? summary,
  }) {
    return CalendarEventsLoaded(
      events: events ?? this.events,
      selectedDate: selectedDate ?? this.selectedDate,
      currentMonth: currentMonth ?? this.currentMonth,
      summary: summary ?? this.summary,
    );
  }
}

/// State khi thêm event thành công
class CalendarEventAdded extends CalendarState {
  final CalendarEvent event;
  final List<CalendarEvent> allEvents;

  const CalendarEventAdded({required this.event, required this.allEvents});

  @override
  List<Object> get props => [event, allEvents];
}

/// State khi cập nhật event thành công
class CalendarEventUpdated extends CalendarState {
  final CalendarEvent event;
  final List<CalendarEvent> allEvents;

  const CalendarEventUpdated({required this.event, required this.allEvents});

  @override
  List<Object> get props => [event, allEvents];
}

/// State khi xóa event thành công
class CalendarEventDeleted extends CalendarState {
  final String eventId;
  final List<CalendarEvent> allEvents;

  const CalendarEventDeleted({required this.eventId, required this.allEvents});

  @override
  List<Object> get props => [eventId, allEvents];
}

/// State khi tìm kiếm events
class CalendarEventsSearched extends CalendarState {
  final List<CalendarEvent> searchResults;
  final String query;

  const CalendarEventsSearched({
    required this.searchResults,
    required this.query,
  });

  @override
  List<Object> get props => [searchResults, query];
}

/// State khi load summary thành công
class CalendarSummaryLoaded extends CalendarState {
  final CalendarSummary summary;

  const CalendarSummaryLoaded({required this.summary});

  @override
  List<Object> get props => [summary];
}

/// State khi sync events thành công
class CalendarEventsSynced extends CalendarState {
  final List<CalendarEvent> events;
  final DateTime syncTime;

  const CalendarEventsSynced({required this.events, required this.syncTime});

  @override
  List<Object> get props => [events, syncTime];
}

/// Error state
class CalendarError extends CalendarState {
  final String message;
  final String? errorCode;

  const CalendarError({required this.message, this.errorCode});

  @override
  List<Object?> get props => [message, errorCode];
}

/// State khi đang thêm event
class CalendarAddingEvent extends CalendarState {}

/// State khi đang cập nhật event
class CalendarUpdatingEvent extends CalendarState {}

/// State khi đang xóa event
class CalendarDeletingEvent extends CalendarState {}

/// State khi đang tìm kiếm
class CalendarSearching extends CalendarState {}

/// State khi đang sync
class CalendarSyncing extends CalendarState {}

/// State khi export thành công
class CalendarEventsExported extends CalendarState {
  final String filePath;

  const CalendarEventsExported({required this.filePath});

  @override
  List<Object> get props => [filePath];
}

/// State khi import thành công
class CalendarEventsImported extends CalendarState {
  final List<CalendarEvent> importedEvents;
  final List<CalendarEvent> allEvents;

  const CalendarEventsImported({
    required this.importedEvents,
    required this.allEvents,
  });

  @override
  List<Object> get props => [importedEvents, allEvents];
}
