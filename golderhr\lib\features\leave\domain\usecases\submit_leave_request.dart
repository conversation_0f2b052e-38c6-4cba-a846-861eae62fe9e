import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../entities/leave_request.dart';
import '../repositories/leave_repository.dart';
import 'usecase.dart';

class SubmitLeaveRequest implements UseCase<LeaveRequest, SubmitLeaveRequestParams> {
  final LeaveRepository repository;

  SubmitLeaveRequest(this.repository);

  @override
  Future<Either<Failure, LeaveRequest>> call(SubmitLeaveRequestParams params) async {
    return await repository.submitLeaveRequest(
      type: params.type,
      startDate: params.startDate,
      endDate: params.endDate,
      reason: params.reason,
      approverId: params.approverId,
    );
  }
}

class SubmitLeaveRequestParams extends Equatable {
  final LeaveType type;
  final DateTime startDate;
  final DateTime endDate;
  final String reason;
  final String? approverId;

  const SubmitLeaveRequestParams({
    required this.type,
    required this.startDate,
    required this.endDate,
    required this.reason,
    this.approverId,
  });

  @override
  List<Object?> get props => [type, startDate, endDate, reason, approverId];
}
