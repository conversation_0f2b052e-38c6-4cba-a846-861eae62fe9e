import 'package:dio/dio.dart';

import '../../../../../core/error/exceptions.dart';
import '../../../../../core/network/dio_client.dart';
import '../../models/login_model.dart';
import '../../models/register_model.dart';
import '../../models/user_model.dart';

abstract class AuthRemoteDataSource {
  Future<LoginResponseModel> login(Map<String, dynamic> credentials);

  Future<RegisterResponseModel> register(Map<String, dynamic> userData);

  Future<void> logout();

  Future<UserModel?> getAuthStatus();

  Future<void> forgotPassword(String email);

  Future<String> verifyOtp(String otp);

  Future<void> resendOtp();

  Future<void> resetPassword({
    required String resetToken,
    required String newPassword,
  });
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final DioClient dioClient;

  AuthRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<LoginResponseModel> login(Map<String, dynamic> credentials) async {
    try {
      final response = await dioClient.post(
        '/api/auth/login',
        data: credentials,
      );
      final loginResponse = LoginResponseModel.fromJson(response.data);
      if (loginResponse.success) {
        await dioClient.setToken(loginResponse.token);
      }
      return loginResponse;
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Server error');
    } catch (e) {
      throw NetworkException('Network error: $e');
    }
  }

  @override
  Future<RegisterResponseModel> register(Map<String, dynamic> userData) async {
    try {
      final response = await dioClient.post(
        '/api/auth/register',
        data: userData,
      );
      return RegisterResponseModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Server error');
    } catch (e) {
      throw NetworkException('Network error: $e');
    }
  }

  @override
  Future<void> logout() async {
    try {
      await dioClient.post('/api/auth/logout');
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Server error');
    } catch (e) {
      throw NetworkException('Network error: $e');
    }
  }

  @override
  Future<UserModel?> getAuthStatus() async {
    try {
      final response = await dioClient.get('/auth/status');
      if (response.data['success']) {
        return UserModel.fromJson(response.data['data']['user']);
      }
      return null;
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Server error');
    } catch (e) {
      throw NetworkException('Network error: $e');
    }
  }

  @override
  Future<void> forgotPassword(String email) async {
    try {
      await dioClient.post('/api/auth/forgot-password', data: {'email': email});
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Server error');
    } catch (e) {
      throw NetworkException('Network error: $e');
    }
  }

  @override
  Future<void> resetPassword({
    required String resetToken,
    required String newPassword,
  }) async {
    try {
      await dioClient.post(
        '/api/auth/reset-password',
        data: {'resetToken': resetToken, "newPassword": newPassword},
      );
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Server error');
    } catch (e) {
      throw NetworkException('Network error: $e');
    }
  }

  @override
  Future<String> verifyOtp(String otp) async {
    try {
      // 1. Gọi đúng API và đúng phương thức POST
      final response = await dioClient.post(
        '/api/auth/verify-otp', // Đảm bảo URL đúng
        data: {'otp': otp},
      );

      // 2. Kiểm tra response và parse dữ liệu
      if (response.statusCode == 200 &&
          response.data['data']['resetToken'] != null) {
        // 3. Trả về resetToken nếu thành công
        return response.data['data']['resetToken'] as String;
      } else {
        // Ném ra lỗi nếu response không như mong đợi
        throw ServerException('Invalid response from server.');
      }
    } on DioException catch (e) {
      // Xử lý lỗi từ Dio
      throw ServerException(
        e.response?.data['message'] ?? 'OTP verification failed',
      );
    } catch (e) {
      throw NetworkException('A network error occurred.');
    }
  }

  @override
  Future<void> resendOtp() async {
    try {
      await dioClient.post('/resend-otp');
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Server error');
    } catch (e) {
      throw NetworkException('Network error: $e');
    }
  }
}
