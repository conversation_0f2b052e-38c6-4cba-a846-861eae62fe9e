import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/extensions/responsive_extension.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/calendar_event.dart';
import '../cubit/calendar_cubit.dart';

/// Dialog để thêm event mới
class AddEventDialog extends StatefulWidget {
  final CalendarEvent? eventToEdit;

  const AddEventDialog({super.key, this.eventToEdit});

  @override
  State<AddEventDialog> createState() => _AddEventDialogState();
}

class _AddEventDialogState extends State<AddEventDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _timeController = TextEditingController();
  final _locationController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  EventType _selectedEventType = EventType.event;
  bool _isAllDay = false;
  bool _isRecurring = false;

  @override
  void initState() {
    super.initState();
    _initializeFields();
  }

  void _initializeFields() {
    if (widget.eventToEdit != null) {
      final event = widget.eventToEdit!;
      _titleController.text = event.title;
      _descriptionController.text = event.description;
      _timeController.text = event.time;
      _locationController.text = event.location ?? '';
      _selectedDate = event.date;
      _selectedEventType = event.type;
      _isAllDay = event.isAllDay;
      _isRecurring = event.isRecurring;
    } else {
      final cubit = context.read<CalendarCubit>();
      _selectedDate = cubit.selectedDate;
      _timeController.text = '09:00 AM';
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _timeController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final isEditing = widget.eventToEdit != null;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: 400,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(context, l10n, isEditing),
            Flexible(
              child: SingleChildScrollView(
                padding: context.responsive.padding(all: 20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      _buildTitleField(context, l10n),
                      SizedBox(height: context.rh(16)),
                      _buildDescriptionField(context, l10n),
                      SizedBox(height: context.rh(16)),
                      _buildDateTimeFields(context, l10n),
                      SizedBox(height: context.rh(16)),
                      _buildEventTypeField(context, l10n),
                      SizedBox(height: context.rh(16)),
                      _buildLocationField(context, l10n),
                      SizedBox(height: context.rh(16)),
                      _buildOptionsFields(context, l10n),
                    ],
                  ),
                ),
              ),
            ),
            _buildActions(context, l10n, isEditing),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, dynamic l10n, bool isEditing) {
    return Container(
      padding: context.responsive.padding(all: 20),
      decoration: BoxDecoration(
        color: AppColors.primaryBlue.withOpacity(0.1),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Row(
        children: [
          Icon(
            isEditing ? Icons.edit : Icons.add,
            color: AppColors.primaryBlue,
            size: context.rf(24),
          ),
          SizedBox(width: context.rw(12)),
          Expanded(
            child: Text(
              isEditing ? l10n.editEvent : l10n.addEvent,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildTitleField(BuildContext context, dynamic l10n) {
    return TextFormField(
      controller: _titleController,
      decoration: InputDecoration(
        labelText: l10n.eventTitle,
        prefixIcon: const Icon(Icons.title),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please enter event title';
        }
        return null;
      },
    );
  }

  Widget _buildDescriptionField(BuildContext context, dynamic l10n) {
    return TextFormField(
      controller: _descriptionController,
      maxLines: 3,
      decoration: InputDecoration(
        labelText: l10n.eventDescription,
        prefixIcon: const Icon(Icons.description),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  Widget _buildDateTimeFields(BuildContext context, dynamic l10n) {
    return Row(
      children: [
        Expanded(
          child: InkWell(
            onTap: _selectDate,
            child: InputDecorator(
              decoration: InputDecoration(
                labelText: l10n.eventDate,
                prefixIcon: const Icon(Icons.calendar_today),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(_formatDate(_selectedDate)),
            ),
          ),
        ),
        SizedBox(width: context.rw(12)),
        Expanded(
          child: TextFormField(
            controller: _timeController,
            decoration: InputDecoration(
              labelText: l10n.eventTime,
              prefixIcon: const Icon(Icons.access_time),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            enabled: !_isAllDay,
            validator: (value) {
              if (!_isAllDay && (value == null || value.trim().isEmpty)) {
                return 'Please enter time';
              }
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildEventTypeField(BuildContext context, dynamic l10n) {
    return DropdownButtonFormField<EventType>(
      value: _selectedEventType,
      decoration: InputDecoration(
        labelText: l10n.eventType,
        prefixIcon: Icon(_selectedEventType.icon),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      items: EventType.values.map((type) {
        return DropdownMenuItem(
          value: type,
          child: Row(
            children: [
              Icon(type.icon, size: context.rf(18)),
              SizedBox(width: context.rw(8)),
              Text(type.displayName),
            ],
          ),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedEventType = value;
          });
        }
      },
    );
  }

  Widget _buildLocationField(BuildContext context, dynamic l10n) {
    return TextFormField(
      controller: _locationController,
      decoration: InputDecoration(
        labelText: l10n.eventLocation,
        prefixIcon: const Icon(Icons.location_on),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  Widget _buildOptionsFields(BuildContext context, dynamic l10n) {
    return Column(
      children: [
        CheckboxListTile(
          title: Text(l10n.allDay),
          value: _isAllDay,
          onChanged: (value) {
            setState(() {
              _isAllDay = value ?? false;
              if (_isAllDay) {
                _timeController.text = 'All Day';
              } else {
                _timeController.text = '09:00 AM';
              }
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
        ),
        CheckboxListTile(
          title: Text(l10n.recurring),
          value: _isRecurring,
          onChanged: (value) {
            setState(() {
              _isRecurring = value ?? false;
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
        ),
      ],
    );
  }

  Widget _buildActions(BuildContext context, dynamic l10n, bool isEditing) {
    return Container(
      padding: context.responsive.padding(all: 20),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.05),
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(16)),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                l10n.cancel,
                style: const TextStyle(color: AppColors.textSecondary),
              ),
            ),
          ),
          SizedBox(width: context.rw(12)),
          Expanded(
            child: ElevatedButton(
              onPressed: _saveEvent,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryBlue,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(isEditing ? l10n.save : l10n.addEvent),
            ),
          ),
        ],
      ),
    );
  }

  void _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );

    if (picked != null) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _saveEvent() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final cubit = context.read<CalendarCubit>();

    if (widget.eventToEdit != null) {
      // Update existing event
      final updatedEvent = widget.eventToEdit!.copyWith(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        date: _selectedDate,
        time: _timeController.text.trim(),
        type: _selectedEventType,
        location: _locationController.text.trim().isEmpty
            ? null
            : _locationController.text.trim(),
        isAllDay: _isAllDay,
        isRecurring: _isRecurring,
      );
      cubit.updateEvent(updatedEvent);
    } else {
      // Create new event
      final newEvent = CalendarEvent.create(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        date: _selectedDate,
        time: _timeController.text.trim(),
        type: _selectedEventType,
        color: cubit.getColorForEventType(_selectedEventType),
        location: _locationController.text.trim().isEmpty
            ? null
            : _locationController.text.trim(),
        isAllDay: _isAllDay,
        isRecurring: _isRecurring,
      );
      cubit.addEvent(newEvent);
    }

    Navigator.of(context).pop();
  }

  String _formatDate(DateTime date) {
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${date.day} ${monthNames[date.month - 1]} ${date.year}';
  }
}
