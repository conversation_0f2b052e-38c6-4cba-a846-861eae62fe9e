import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import '../../../../core/usecases/usecase.dart';
import '../../domain/entities/calendar_event.dart';
import '../../domain/entities/calendar_summary.dart';
import '../../domain/usecases/get_calendar_events.dart';
import '../../domain/usecases/add_calendar_event.dart';
import '../../domain/usecases/update_calendar_event.dart';
import '../../domain/usecases/delete_calendar_event.dart';
import '../../domain/usecases/get_calendar_summary.dart';
import 'calendar_state.dart';

/// Cubit để quản lý state của Calendar feature
class CalendarCubit extends Cubit<CalendarState> {
  final GetCalendarEvents _getCalendarEvents;
  final AddCalendarEvent _addCalendarEvent;
  final UpdateCalendarEvent _updateCalendarEvent;
  final DeleteCalendarEvent _deleteCalendarEvent;
  final GetCalendarSummary _getCalendarSummary;

  // Current state data
  List<CalendarEvent> _allEvents = [];
  DateTime _selectedDate = DateTime.now();
  DateTime _currentMonth = DateTime.now();
  CalendarSummary? _currentSummary;

  CalendarCubit({
    required GetCalendarEvents getCalendarEvents,
    required AddCalendarEvent addCalendarEvent,
    required UpdateCalendarEvent updateCalendarEvent,
    required DeleteCalendarEvent deleteCalendarEvent,
    required GetCalendarSummary getCalendarSummary,
  }) : _getCalendarEvents = getCalendarEvents,
       _addCalendarEvent = addCalendarEvent,
       _updateCalendarEvent = updateCalendarEvent,
       _deleteCalendarEvent = deleteCalendarEvent,
       _getCalendarSummary = getCalendarSummary,
       super(CalendarInitial());

  // Getters
  List<CalendarEvent> get allEvents => _allEvents;
  DateTime get selectedDate => _selectedDate;
  DateTime get currentMonth => _currentMonth;
  CalendarSummary? get currentSummary => _currentSummary;

  /// Khởi tạo calendar với dữ liệu ban đầu
  Future<void> initialize() async {
    emit(CalendarLoading());
    try {
      await loadAllEvents();
      await loadCalendarSummary();
    } catch (e) {
      emit(
        CalendarError(
          message: 'Failed to initialize calendar: ${e.toString()}',
        ),
      );
    }
  }

  /// Load tất cả events
  Future<void> loadAllEvents() async {
    emit(CalendarLoading());

    final result = await _getCalendarEvents(NoParams());
    result.fold((failure) => emit(CalendarError(message: failure.message)), (
      events,
    ) {
      _allEvents = events;
      emit(
        CalendarEventsLoaded(
          events: events,
          selectedDate: _selectedDate,
          currentMonth: _currentMonth,
          summary: _currentSummary,
        ),
      );
    });
  }

  /// Load events theo ngày
  Future<void> loadEventsByDate(DateTime date) async {
    emit(CalendarLoading());

    final result = await _getCalendarEvents(NoParams());
    result.fold((failure) => emit(CalendarError(message: failure.message)), (
      events,
    ) {
      emit(
        CalendarEventsLoaded(
          events: events,
          selectedDate: date,
          currentMonth: _currentMonth,
          summary: _currentSummary,
        ),
      );
    });
  }

  /// Load events theo tháng
  Future<void> loadEventsByMonth(int year, int month) async {
    emit(CalendarLoading());

    final result = await _getCalendarEvents(NoParams());
    result.fold((failure) => emit(CalendarError(message: failure.message)), (
      events,
    ) {
      _currentMonth = DateTime(year, month);
      emit(
        CalendarEventsLoaded(
          events: events,
          selectedDate: _selectedDate,
          currentMonth: _currentMonth,
          summary: _currentSummary,
        ),
      );
    });
  }

  /// Load events trong khoảng thời gian
  Future<void> loadEventsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    emit(CalendarLoading());

    final result = await _getCalendarEvents(NoParams());
    result.fold((failure) => emit(CalendarError(message: failure.message)), (
      events,
    ) {
      emit(
        CalendarEventsLoaded(
          events: events,
          selectedDate: _selectedDate,
          currentMonth: _currentMonth,
          summary: _currentSummary,
        ),
      );
    });
  }

  /// Load upcoming events
  Future<void> loadUpcomingEvents({int limit = 10}) async {
    emit(CalendarLoading());

    final result = await _getCalendarEvents(NoParams());
    result.fold((failure) => emit(CalendarError(message: failure.message)), (
      events,
    ) {
      emit(
        CalendarEventsLoaded(
          events: events,
          selectedDate: _selectedDate,
          currentMonth: _currentMonth,
          summary: _currentSummary,
        ),
      );
    });
  }

  /// Load events theo loại
  Future<void> loadEventsByType(EventType type) async {
    emit(CalendarLoading());

    final result = await _getCalendarEvents(NoParams());
    result.fold((failure) => emit(CalendarError(message: failure.message)), (
      events,
    ) {
      emit(
        CalendarEventsLoaded(
          events: events,
          selectedDate: _selectedDate,
          currentMonth: _currentMonth,
          summary: _currentSummary,
        ),
      );
    });
  }

  /// Load calendar summary
  Future<void> loadCalendarSummary() async {
    final result = await _getCalendarSummary(NoParams());
    result.fold((failure) => emit(CalendarError(message: failure.message)), (
      summary,
    ) {
      _currentSummary = summary;
      emit(CalendarSummaryLoaded(summary: summary));
    });
  }

  /// Load monthly summary
  Future<void> loadMonthlySummary(int year, int month) async {
    final result = await _getCalendarSummary(NoParams());
    result.fold((failure) => emit(CalendarError(message: failure.message)), (
      summary,
    ) {
      _currentSummary = summary;
      emit(CalendarSummaryLoaded(summary: summary));
    });
  }

  /// Chọn ngày
  void selectDate(DateTime date) {
    _selectedDate = date;
    if (state is CalendarEventsLoaded) {
      final currentState = state as CalendarEventsLoaded;
      emit(currentState.copyWith(selectedDate: date));
    }
  }

  /// Chuyển tháng
  void changeMonth(DateTime month) {
    _currentMonth = month;
    loadEventsByMonth(month.year, month.month);
  }

  /// Chuyển tháng trước
  void previousMonth() {
    final previousMonth = DateTime(_currentMonth.year, _currentMonth.month - 1);
    changeMonth(previousMonth);
  }

  /// Chuyển tháng sau
  void nextMonth() {
    final nextMonth = DateTime(_currentMonth.year, _currentMonth.month + 1);
    changeMonth(nextMonth);
  }

  /// Lấy events cho ngày được chỉ định
  List<CalendarEvent> getEventsForDate(DateTime date) {
    return _allEvents.where((event) => event.isOnDate(date)).toList();
  }

  /// Kiểm tra xem ngày có events không
  bool hasEventsOnDate(DateTime date) {
    return _allEvents.any((event) => event.isOnDate(date));
  }

  /// Lấy events trong tuần
  List<CalendarEvent> getEventsForWeek(DateTime startOfWeek) {
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    return _allEvents
        .where((event) => event.isInDateRange(startOfWeek, endOfWeek))
        .toList();
  }

  /// Thêm event mới
  Future<void> addEvent(CalendarEvent event) async {
    emit(CalendarAddingEvent());

    final result = await _addCalendarEvent(
      AddCalendarEventParams(event: event),
    );
    result.fold((failure) => emit(CalendarError(message: failure.message)), (
      addedEvent,
    ) {
      _allEvents.add(addedEvent);
      emit(CalendarEventAdded(event: addedEvent, allEvents: _allEvents));
      // Reload current view
      _reloadCurrentView();
    });
  }

  /// Cập nhật event
  Future<void> updateEvent(CalendarEvent event) async {
    emit(CalendarUpdatingEvent());

    final result = await _updateCalendarEvent(
      UpdateCalendarEventParams(event: event),
    );
    result.fold((failure) => emit(CalendarError(message: failure.message)), (
      updatedEvent,
    ) {
      final index = _allEvents.indexWhere((e) => e.id == updatedEvent.id);
      if (index != -1) {
        _allEvents[index] = updatedEvent;
      }
      emit(CalendarEventUpdated(event: updatedEvent, allEvents: _allEvents));
      // Reload current view
      _reloadCurrentView();
    });
  }

  /// Xóa event
  Future<void> deleteEvent(String eventId) async {
    emit(CalendarDeletingEvent());

    final result = await _deleteCalendarEvent(
      DeleteCalendarEventParams(id: eventId),
    );
    result.fold((failure) => emit(CalendarError(message: failure.message)), (
      _,
    ) {
      _allEvents.removeWhere((event) => event.id == eventId);
      emit(CalendarEventDeleted(eventId: eventId, allEvents: _allEvents));
      // Reload current view
      _reloadCurrentView();
    });
  }

  /// Tìm kiếm events
  Future<void> searchEvents(String query) async {
    if (query.isEmpty) {
      _reloadCurrentView();
      return;
    }

    emit(CalendarSearching());

    // Tìm kiếm local trước
    final searchResults = _allEvents.where((event) {
      return event.title.toLowerCase().contains(query.toLowerCase()) ||
          event.description.toLowerCase().contains(query.toLowerCase()) ||
          event.location?.toLowerCase().contains(query.toLowerCase()) == true;
    }).toList();

    emit(CalendarEventsSearched(searchResults: searchResults, query: query));
  }

  /// Refresh dữ liệu
  Future<void> refresh() async {
    await initialize();
  }

  /// Reload view hiện tại
  void _reloadCurrentView() {
    emit(
      CalendarEventsLoaded(
        events: _allEvents,
        selectedDate: _selectedDate,
        currentMonth: _currentMonth,
        summary: _currentSummary,
      ),
    );
  }

  /// Reset về trạng thái ban đầu
  void reset() {
    _allEvents.clear();
    _selectedDate = DateTime.now();
    _currentMonth = DateTime.now();
    _currentSummary = null;
    emit(CalendarInitial());
  }

  /// Lấy màu cho event type
  Color getColorForEventType(EventType type) {
    switch (type) {
      case EventType.meeting:
        return Colors.blue;
      case EventType.leave:
        return Colors.green;
      case EventType.holiday:
        return Colors.orange;
      case EventType.training:
        return Colors.purple;
      case EventType.event:
        return Colors.teal;
    }
  }

  /// Lấy events theo filter
  List<CalendarEvent> getFilteredEvents({
    List<EventType>? types,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    var filteredEvents = _allEvents;

    if (types != null && types.isNotEmpty) {
      filteredEvents = filteredEvents
          .where((event) => types.contains(event.type))
          .toList();
    }

    if (startDate != null && endDate != null) {
      filteredEvents = filteredEvents
          .where((event) => event.isInDateRange(startDate, endDate))
          .toList();
    }

    return filteredEvents;
  }
}
