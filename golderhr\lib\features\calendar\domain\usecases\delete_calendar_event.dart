import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/calendar_repository.dart';

/// Use case để xóa calendar event
class DeleteCalendarEvent implements UseCase<void, DeleteCalendarEventParams> {
  final CalendarRepository repository;

  DeleteCalendarEvent(this.repository);

  @override
  Future<Either<Failure, void>> call(DeleteCalendarEventParams params) async {
    return await repository.deleteEvent(params.id);
  }
}

/// Parameters cho DeleteCalendarEvent
class DeleteCalendarEventParams extends Equatable {
  final String id;

  const DeleteCalendarEventParams({required this.id});

  @override
  List<Object> get props => [id];
}

/// Use case để xóa nhiều calendar events
class DeleteCalendarEvents
    implements UseCase<void, DeleteCalendarEventsParams> {
  final CalendarRepository repository;

  DeleteCalendarEvents(this.repository);

  @override
  Future<Either<Failure, void>> call(DeleteCalendarEventsParams params) async {
    return await repository.deleteEvents(params.ids);
  }
}

/// Parameters cho DeleteCalendarEvents
class DeleteCalendarEventsParams extends Equatable {
  final List<String> ids;

  const DeleteCalendarEventsParams({required this.ids});

  @override
  List<Object> get props => [ids];
}
