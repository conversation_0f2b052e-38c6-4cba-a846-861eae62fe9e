import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../../l10n/app_localizations.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/overtime_request_entity.dart';

class OvertimeDetailPage extends StatelessWidget {
  final OvertimeRequestEntity request;

  const OvertimeDetailPage({super.key, required this.request});

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final theme = Theme.of(context);
    final l10n = context.l10n;

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          l10n.overtimeDetails,
          style: theme.textTheme.titleLarge?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          if (request.status == OvertimeStatus.pending)
            IconButton(
              icon: const Icon(
                Icons.edit_rounded,
                color: AppColors.primaryBlue,
              ),
              onPressed: () {
                // TODO: Navigate to edit page
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Edit functionality coming soon'),
                  ),
                );
              },
              tooltip: 'Edit Request',
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: responsive.padding(all: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Header Card
            _buildStatusHeaderCard(responsive, theme, l10n),
            SizedBox(height: responsive.heightPercentage(2)),

            // Basic Information Card
            _buildBasicInfoCard(responsive, theme, l10n),
            SizedBox(height: responsive.heightPercentage(2)),

            // Time Details Card
            _buildTimeDetailsCard(responsive, theme, l10n),
            SizedBox(height: responsive.heightPercentage(2)),

            // Reason Card
            if (request.reason.isNotEmpty)
              _buildReasonCard(responsive, theme, l10n),
            if (request.reason.isNotEmpty)
              SizedBox(height: responsive.heightPercentage(2)),

            // Approval Information Card
            _buildApprovalInfoCard(responsive, theme, l10n),
            SizedBox(height: responsive.heightPercentage(2)),

            // Rejection Reason Card (if rejected)
            if (request.rejectionReason != null &&
                request.rejectionReason!.isNotEmpty)
              _buildRejectionReasonCard(responsive, theme, l10n),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusHeaderCard(
    Responsive responsive,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    final statusColor = _getStatusColor(request.status);
    final statusText = _getStatusText(request.status, l10n);
    final statusIcon = _getStatusIcon(request.status);

    return Container(
      padding: responsive.padding(all: 24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            statusColor.withValues(alpha: 0.1),
            statusColor.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: statusColor.withValues(alpha: 0.3), width: 2),
        boxShadow: [
          BoxShadow(
            color: statusColor.withValues(alpha: 0.2),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: statusColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(statusIcon, color: statusColor, size: 32),
          ),
          SizedBox(width: responsive.widthPercentage(4)),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Request #${request.id}',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: responsive.heightPercentage(0.5)),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: statusColor.withValues(alpha: 0.4),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, size: 16, color: statusColor),
                      const SizedBox(width: 6),
                      Text(
                        statusText,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: statusColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoCard(
    Responsive responsive,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return _buildInfoCard(
      responsive,
      theme,
      'Basic Information',
      Icons.info_outline_rounded,
      [
        _buildInfoRow(
          Icons.calendar_today_rounded,
          'Date',
          '${request.date.day}/${request.date.month}/${request.date.year}',
          theme,
          responsive,
        ),
        _buildInfoRow(
          Icons.category_rounded,
          'Type',
          _getTypeText(request.type, l10n),
          theme,
          responsive,
        ),
        _buildInfoRow(
          Icons.schedule_rounded,
          'Duration',
          '${request.hours.toStringAsFixed(1)} hours',
          theme,
          responsive,
        ),
      ],
    );
  }

  Widget _buildTimeDetailsCard(
    Responsive responsive,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return _buildInfoCard(
      responsive,
      theme,
      'Time Details',
      Icons.access_time_rounded,
      [
        _buildInfoRow(
          Icons.play_arrow_rounded,
          'Start Time',
          _formatTime(request.startTime),
          theme,
          responsive,
        ),
        _buildInfoRow(
          Icons.stop_rounded,
          'End Time',
          _formatTime(request.endTime),
          theme,
          responsive,
        ),
        _buildInfoRow(
          Icons.timer_rounded,
          'Total Hours',
          '${request.hours.toStringAsFixed(1)}h',
          theme,
          responsive,
        ),
      ],
    );
  }

  Widget _buildReasonCard(
    Responsive responsive,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return _buildInfoCard(
      responsive,
      theme,
      'Reason',
      Icons.description_rounded,
      [
        Container(
          width: double.infinity,
          padding: responsive.padding(all: 16),
          decoration: BoxDecoration(
            color: AppColors.primaryBlue.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.primaryBlue.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          child: Text(
            request.reason,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppColors.textPrimary,
              height: 1.5,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildApprovalInfoCard(
    Responsive responsive,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return _buildInfoCard(
      responsive,
      theme,
      'Approval Information',
      Icons.person_rounded,
      [
        if (request.assignedApproverId != null)
          _buildInfoRow(
            Icons.person_pin_circle_rounded,
            'Assigned Approver',
            request.assignedApproverId!,
            theme,
            responsive,
          ),
        if (request.approvedBy != null)
          _buildInfoRow(
            Icons.verified_user_rounded,
            'Approved By',
            request.approvedBy!,
            theme,
            responsive,
            valueColor: AppColors.success,
          ),
        if (request.status == OvertimeStatus.pending)
          Container(
            padding: responsive.padding(all: 12),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.pending_actions_rounded,
                  color: AppColors.warning,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Waiting for approval',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: AppColors.warning,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildRejectionReasonCard(
    Responsive responsive,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return _buildInfoCard(
      responsive,
      theme,
      'Rejection Reason',
      Icons.error_outline_rounded,
      [
        Container(
          width: double.infinity,
          padding: responsive.padding(all: 16),
          decoration: BoxDecoration(
            color: AppColors.error.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.error.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Text(
            request.rejectionReason!,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppColors.error,
              height: 1.5,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoCard(
    Responsive responsive,
    ThemeData theme,
    String title,
    IconData icon,
    List<Widget> children,
  ) {
    return Container(
      padding: responsive.padding(all: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryBlue.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: AppColors.primaryBlue.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: AppColors.primaryBlue, size: 20),
              ),
              SizedBox(width: responsive.widthPercentage(3)),
              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: responsive.heightPercentage(2)),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    IconData icon,
    String label,
    String value,
    ThemeData theme,
    Responsive responsive, {
    Color? valueColor,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: responsive.heightPercentage(1.5)),
      child: Row(
        children: [
          Icon(icon, size: 18, color: AppColors.textSecondary),
          SizedBox(width: responsive.widthPercentage(3)),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: valueColor ?? AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(OvertimeStatus status) {
    switch (status) {
      case OvertimeStatus.approved:
        return AppColors.success;
      case OvertimeStatus.rejected:
        return AppColors.error;
      case OvertimeStatus.pending:
        return AppColors.warning;
    }
  }

  String _getStatusText(OvertimeStatus status, AppLocalizations l10n) {
    switch (status) {
      case OvertimeStatus.approved:
        return l10n.approved;
      case OvertimeStatus.rejected:
        return l10n.rejected;
      case OvertimeStatus.pending:
        return l10n.pending;
    }
  }

  IconData _getStatusIcon(OvertimeStatus status) {
    switch (status) {
      case OvertimeStatus.approved:
        return Icons.check_circle_rounded;
      case OvertimeStatus.rejected:
        return Icons.cancel_rounded;
      case OvertimeStatus.pending:
        return Icons.schedule_rounded;
    }
  }

  String _getTypeText(OvertimeType type, AppLocalizations l10n) {
    switch (type) {
      case OvertimeType.regular:
        return l10n.regularOvertime;
      case OvertimeType.weekend:
        return l10n.weekendOvertime;
      case OvertimeType.holiday:
        return l10n.holidayOvertime;
    }
  }

  String _formatTime(DateTime time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }
}
