import 'package:flutter/material.dart';
import '../../domain/entities/overtime_request_entity.dart';
import 'overtime_admin_request_card.dart';

class OvertimeAdminListWidget extends StatelessWidget {
  final List<OvertimeRequestEntity> requests;
  final ScrollController scrollController;
  final bool isLoadingMore;
  final bool isProcessing;

  const OvertimeAdminListWidget({
    super.key,
    required this.requests,
    required this.scrollController,
    required this.isLoadingMore,
    required this.isProcessing,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: requests.length + (isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == requests.length) {
          // Loading more indicator
          return const Padding(
            padding: EdgeInsets.all(16),
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final request = requests[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: OvertimeAdminRequestCard(
            request: request,
            isProcessing: isProcessing,
          ),
        );
      },
    );
  }
}
