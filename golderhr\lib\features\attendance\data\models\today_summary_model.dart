// lib/features/attendance/data/models/today_summary_model.dart
import 'package:golderhr/features/attendance/domain/entities/today_summary.dart';

class TodaySummaryModel extends TodaySummary {
  const TodaySummaryModel({
    required super.isCheckedIn,
    required super.isCheckedOut,
    required super.checkInTime,
    required super.checkOutTime,
    required super.totalHours,
    required super.overtime,
  });

  factory TodaySummaryModel.fromJson(Map<String, dynamic> json) {
    return TodaySummaryModel(
      isCheckedIn: json['isCheckedIn'] ?? false,
      isCheckedOut: json['isCheckedOut'] ?? false,
      checkInTime: json['checkInTime'] ?? '--:-- --',
      checkOutTime: json['checkOutTime'] ?? '--:-- --',
      totalHours: json['totalHours'] ?? '--',
      overtime: json['overtime'] ?? '--',
    );
  }
}
