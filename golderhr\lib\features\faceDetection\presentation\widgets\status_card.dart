import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../../l10n/app_localizations.dart';
import '../cubit/face_checkin_cubit.dart';
import '../cubit/face_checkin_state.dart';

class StatusCard extends StatelessWidget {
  const StatusCard({super.key});

  Color _getStatusColor(String status) {
    status = status.toLowerCase();
    if (status.contains('successful')) return Colors.green.shade600;
    if (status.contains('ready')) return Colors.blue.shade600;
    if (status.contains('no') || status.contains('denied')) {
      return Colors.orange.shade700;
    }
    if (status.contains('error') || status.contains('failed')) {
      return Colors.red.shade700;
    }
    if (status.contains('processing') || status.contains('getting')) {
      return Colors.blue.shade600;
    }
    return Colors.grey.shade600;
  }

  IconData _getStatusIcon(String status) {
    status = status.toLowerCase();
    if (status.contains('successful')) return Icons.check_circle;
    if (status.contains('ready')) return Icons.info_outline;
    if (status.contains('no') || status.contains('denied')) {
      return Icons.warning_amber_rounded;
    }
    if (status.contains('error') || status.contains('failed')) {
      return Icons.error_outline;
    }
    if (status.contains('processing') || status.contains('analyzing')) {
      return Icons.hourglass_empty_rounded;
    }
    if (status.contains('getting')) return Icons.location_searching;
    return Icons.info_outline;
  }

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final l10n = AppLocalizations.of(context)!;
    return BlocBuilder<FaceDetectionCubit, FaceDetectionState>(
      builder: (context, state) {
        final statusColor = _getStatusColor(state.status);
        final statusIcon = _getStatusIcon(state.status);
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          padding: responsive.padding(all: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(responsive.fontSize(16)),
            border: Border.all(color: statusColor.withAlpha(77), width: 1),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(12),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: responsive.padding(all: 8),
                decoration: BoxDecoration(
                  color: statusColor.withAlpha(26),
                  borderRadius: BorderRadius.circular(responsive.fontSize(8)),
                ),
                child: Icon(
                  statusIcon,
                  color: statusColor,
                  size: responsive.fontSize(20),
                ),
              ),
              SizedBox(width: responsive.scaleWidth(12)),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      l10n.detectFaceSystemStatus,
                      style: TextStyle(
                        fontSize: responsive.fontSize(12),
                        color: Colors.grey,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: responsive.scaleHeight(2)),
                    Text(
                      state.status.isEmpty && !state.isLoading
                          ? l10n.detectFaceReadyForCheckIn
                          : state.status,
                      style: TextStyle(
                        fontSize: responsive.fontSize(13),
                        fontWeight: FontWeight.w600,
                        color: statusColor,
                      ),
                    ),
                    if (state.faces.isNotEmpty &&
                        state.image != null &&
                        !state.isLoading) ...[
                      SizedBox(height: responsive.scaleHeight(4)),
                      Text(
                        l10n.detectFaceFacesDetected(state.faces.length),
                        style: TextStyle(
                          fontSize: responsive.fontSize(11),
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
