import 'package:flutter/material.dart';
import '../../../../core/responsive/responsive.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/leave_policy.dart';

class LeavePolicyCard extends StatelessWidget {
  final List<LeavePolicy> policies;

  const LeavePolicyCard({super.key, required this.policies});

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: responsive.padding(all: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.primaryBlue.withValues(alpha: 0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.textSecondary.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.info_outline_rounded,
                  color: AppColors.primaryBlue,
                  size: 20,
                ),
              ),
              SizedBox(width: responsive.widthPercentage(3)),
              Text(
                'Leave Policy',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontSize: responsive.fontSize(16),
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: responsive.heightPercentage(2.5)),
          ...policies.asMap().entries.map((entry) {
            final index = entry.key;
            final policy = entry.value;
            return Padding(
              padding: EdgeInsets.only(
                bottom: index < policies.length - 1
                    ? responsive.heightPercentage(1.5)
                    : 0,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 6,
                    height: 6,
                    margin: const EdgeInsets.only(top: 6),
                    decoration: BoxDecoration(
                      color: AppColors.primaryBlue,
                      shape: BoxShape.circle,
                    ),
                  ),
                  SizedBox(width: responsive.widthPercentage(3)),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          policy.title,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontSize: responsive.fontSize(14),
                            color: AppColors.textPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: responsive.heightPercentage(0.5)),
                        Text(
                          policy.description,
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontSize: responsive.fontSize(13),
                            color: AppColors.textSecondary,
                            height: 1.4,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }
}
