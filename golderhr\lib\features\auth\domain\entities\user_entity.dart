import 'package:equatable/equatable.dart';

class UserEntity extends Equatable {
  final String id;
  final String email;
  final String fullname;
  final String role;
  final String? avatar;
  final String? referenceImageUrl;

  const UserEntity({
    required this.id,
    required this.email,
    required this.fullname,
    required this.role,
    this.avatar,
    this.referenceImageUrl,
  });

  @override
  List<Object?> get props => [
    id,
    email,
    fullname,
    role,
    avatar,
    referenceImageUrl,
  ];
}
