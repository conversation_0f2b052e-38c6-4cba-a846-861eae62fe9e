import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../logger/app_logger.dart';

class DioInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (kDebugMode) {
      AppLogger.debug('REQUEST[${options.method}] => PATH: ${options.uri}');
      AppLogger.debug('Headers: ${options.headers}');
      if (options.data != null) {
        AppLogger.debug('Data: ${options.data}');
      }
    }
    return handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (kDebugMode) {
      AppLogger.info(
        'RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.uri}',
      );
      AppLogger.info('Data: ${response.data}');
    }
    return handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (kDebugMode) {
      AppLogger.error(
        'ERROR[${err.response?.statusCode}] => PATH: ${err.requestOptions.uri}',
        err,
      );
      AppLogger.error('Message: ${err.message}');
      if (err.response != null) {
        AppLogger.error('Error Data: ${err.response?.data}');
      }
    }
    return handler.next(err);
  }
}
