import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';

import '../../../../core/animation/auth_page_animation_mixin.dar.dart';
import '../../../../core/animation/fade_slide_animation.dart';
import '../../../../core/responsive/responsive.dart';
import '../../../../shared/widgets/gradient_background.dart';
import '../widgets/arrow_appbar.dart';
import '../widgets/auth_card.dart';
import '../widgets/auth_welcome_section.dart';
import '../widgets/forgot_password_form.dart';

class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordState();
}

class _ForgotPasswordState extends State<ForgotPasswordPage>
    with TickerProviderStateMixin, AuthPageAnimationMixin {
  @override
  void initState() {
    super.initState();
    initializeAnimations();
  }

  @override
  void dispose() {
    disposeAnimations();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);

    return Scaffold(
      resizeToAvoidBottomInset: false,
      extendBodyBehindAppBar: true,
      body: GradientBackground(
        child: SafeArea(
          child: SingleChildScrollView(
            keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
            physics: const ClampingScrollPhysics(),
            child: SizedBox(
              height:
                  MediaQuery.of(context).size.height -
                  MediaQuery.of(context).padding.top,
              child: Padding(
                padding: responsive.padding(horizontal: 24, vertical: 16),
                child: Column(
                  children: [
                    ArrowAppBar(),
                    SizedBox(height: responsive.heightPercentage(8)),

                    // Welcome section with animation
                    FadeSlideAnimation(
                      fadeAnimation: fadeAnimation,
                      slideAnimation: slideAnimation,
                      child: AuthWelcomeSection(
                        title: context.l10n.authForgotPassword,
                        subtitle: context.l10n.authSubTitleForgotPassword,
                        showLogo: false,
                      ),
                    ),
                    SizedBox(height: responsive.heightPercentage(4)),

                    FadeSlideAnimation(
                      fadeAnimation: fadeAnimation,
                      slideAnimation: slideAnimation,
                      child: AuthCard(child: ForgotPasswordForm()),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
