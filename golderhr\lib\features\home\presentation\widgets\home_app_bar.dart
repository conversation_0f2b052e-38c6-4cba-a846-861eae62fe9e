import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/core/routes/app_routes.dart';
import 'package:golderhr/shared/widgets/user_avatar.dart';
import 'package:iconsax/iconsax.dart';

class HomeAppBar extends StatelessWidget implements PreferredSizeWidget {
  const HomeAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: Padding(
        padding: context.responsive.padding(all: 6.0),
        child: UserAvatarVariants.medium(
          onTap: () {
            context.push(AppRoutes.profile);
          },
        ),
      ),
      title: Text(
        context.l10n.home,
        style: context.lightTheme.textTheme.displaySmall!.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          onPressed: () {
            // <PERSON><PERSON><PERSON> sử route message chưa có, tạm comment lại hoặc trỏ đến route đã có
            // context.push(AppRoutes.messages);
          },
          icon: const Icon(Iconsax.message, color: Colors.black),
        ),
        IconButton(
          onPressed: () {
            context.push(AppRoutes.setting);
          },
          icon: const Icon(Iconsax.setting_4, color: Colors.black),
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
