import '../../domain/entities/department_entity.dart';

class DepartmentModel extends DepartmentEntity {
  const DepartmentModel({
    required super.id,
    required super.name,
    super.description,
    super.code,
    super.isActive = true,
    super.isDeleted = false,
    super.isDisabled = false,
    super.parentId,
    super.idMapper,
    super.codeMapper,
    super.createdAt,
    super.updatedAt,
  });

  factory DepartmentModel.fromJson(Map<String, dynamic> json) {
    return DepartmentModel(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'],
      code: json['code'],
      isActive: json['isActive'] ?? true,
      isDeleted: json['isdeleted'] ?? false,
      isDisabled: json['isdisable'] ?? false,
      parentId: json['parentId'],
      idMapper: json['IdMapper'],
      codeMapper: json['CodeMapper'],
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      if (description != null) 'description': description,
      if (code != null) 'code': code,
      'isActive': isActive,
      'isdeleted': isDeleted,
      'isdisable': isDisabled,
      if (parentId != null) 'parentId': parentId,
      if (idMapper != null) 'IdMapper': idMapper,
      if (codeMapper != null) 'CodeMapper': codeMapper,
      if (createdAt != null) 'createdAt': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
    };
  }

  @override
  DepartmentModel copyWith({
    String? id,
    String? name,
    String? description,
    String? code,
    bool? isActive,
    bool? isDeleted,
    bool? isDisabled,
    String? parentId,
    int? idMapper,
    String? codeMapper,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DepartmentModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      code: code ?? this.code,
      isActive: isActive ?? this.isActive,
      isDeleted: isDeleted ?? this.isDeleted,
      isDisabled: isDisabled ?? this.isDisabled,
      parentId: parentId ?? this.parentId,
      idMapper: idMapper ?? this.idMapper,
      codeMapper: codeMapper ?? this.codeMapper,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'DepartmentModel(id: $id, name: $name, description: $description, code: $code, isActive: $isActive, isDeleted: $isDeleted, isDisabled: $isDisabled)';
  }
}

// Create department parameters
class CreateDepartmentParams {
  final String name;
  final String? description;
  final String? code;
  final String? parentId;

  const CreateDepartmentParams({
    required this.name,
    this.description,
    this.code,
    this.parentId,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      if (description != null) 'description': description,
      if (code != null) 'code': code,
      if (parentId != null) 'parentId': parentId,
    };
  }
}

// Update department parameters
class UpdateDepartmentParams {
  final String departmentId;
  final String? name;
  final String? description;
  final String? code;
  final bool? isActive;
  final bool? isDisabled;
  final String? parentId;

  const UpdateDepartmentParams({
    required this.departmentId,
    this.name,
    this.description,
    this.code,
    this.isActive,
    this.isDisabled,
    this.parentId,
  });

  Map<String, dynamic> toJson() {
    return {
      if (name != null) 'name': name,
      if (description != null) 'description': description,
      if (code != null) 'code': code,
      if (isActive != null) 'isActive': isActive,
      if (isDisabled != null) 'isdisable': isDisabled,
      if (parentId != null) 'parentId': parentId,
    };
  }
}
