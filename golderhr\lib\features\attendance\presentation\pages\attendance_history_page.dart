import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import '../../../../injection_container.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../../../shared/widgets/responsive_layout.dart';

import '../cubit/attendance_history_page/attendance_history_cubit.dart';
import '../cubit/attendance_history_page/attendance_history_state.dart';
import '../widgets/attendance_summary_card.dart';
import '../widgets/calendar_tab_view.dart';
import '../widgets/history_tab_view.dart';
import '../widgets/loading_widget.dart';

/// Widget chính, cung cấp [AttendanceHistoryCubit] cho cây widget.
class AttendanceHistoryPage extends StatelessWidget {
  const AttendanceHistoryPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          sl<AttendanceHistoryCubit>()..fetchMonthlyDetails(DateTime.now()),
      child: const _AttendanceHistoryView(),
    );
  }
}

/// Widget hiển thị giao diện với thiết kế hiện đại
class _AttendanceHistoryView extends StatelessWidget {
  const _AttendanceHistoryView();

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final theme = Theme.of(context);
    final l10n = context.l10n;

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            'Attendance History',
            style: theme.textTheme.titleLarge?.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          actions: [
            IconButton(
              icon: const Icon(
                Icons.refresh_rounded,
                color: AppColors.primaryBlue,
              ),
              onPressed: () {
                context.read<AttendanceHistoryCubit>().fetchMonthlyDetails(
                  DateTime.now(),
                );
              },
              tooltip: 'Refresh',
            ),
          ],
        ),
        body: BlocBuilder<AttendanceHistoryCubit, AttendanceHistoryState>(
          builder: (context, state) {
            if (state is AttendanceHistoryLoading ||
                state is AttendanceHistoryInitial) {
              return const LoadingWidget(
                message: 'Loading attendance history...',
              );
            }

            if (state is AttendanceHistoryLoaded) {
              return RefreshIndicator(
                onRefresh: () async {
                  context.read<AttendanceHistoryCubit>().fetchMonthlyDetails(
                    DateTime.now(),
                  );
                },
                child: Column(
                  children: [
                    // Summary Card
                    AttendanceSummaryCard(
                      summary: state.monthlyDetails.summary,
                    ),

                    // Tab Bar
                    Container(
                      margin: responsive.padding(
                        horizontal: 16.0,
                        vertical: 8.0,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primaryBlue.withValues(
                              alpha: 0.08,
                            ),
                            blurRadius: 20,
                            offset: const Offset(0, 4),
                            spreadRadius: 0,
                          ),
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: TabBar(
                        labelColor: AppColors.primaryBlue,
                        unselectedLabelColor: AppColors.textSecondary,
                        indicator: BoxDecoration(
                          color: AppColors.primaryBlue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        indicatorSize: TabBarIndicatorSize.tab,
                        dividerColor: Colors.transparent,
                        tabs: [
                          Tab(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.calendar_month_rounded,
                                  size: 18,
                                ),
                                const SizedBox(width: 8),
                                Text('Calendar'),
                              ],
                            ),
                          ),
                          Tab(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.history_rounded, size: 18),
                                const SizedBox(width: 8),
                                Text(l10n.viewFullHistory),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Tab Views
                    Expanded(
                      child: TabBarView(
                        children: [
                          CalendarTabView(
                            monthlyDetails: state.monthlyDetails,
                            focusedDay: state.focusedDay,
                            selectedDayInfo: state.selectedDayInfo,
                          ),
                          HistoryTabView(monthlyDetails: state.monthlyDetails),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }

            if (state is AttendanceHistoryError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, size: 64, color: AppColors.error),
                    const SizedBox(height: 16),
                    Text(
                      state.message,
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: () => context
                          .read<AttendanceHistoryCubit>()
                          .fetchMonthlyDetails(DateTime.now()),
                      icon: const Icon(Icons.refresh),
                      label: const Text('Retry'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryBlue,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ],
                ),
              );
            }

            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }
}
