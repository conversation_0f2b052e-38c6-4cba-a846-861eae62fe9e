import 'package:dartz/dartz.dart';

import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/leave_balance.dart';
import '../../domain/entities/leave_request.dart';
import '../../domain/repositories/leave_repository.dart';
import '../datasources/leave_remote_data_source.dart';
import '../models/leave_request_model.dart';

class LeaveRepositoryImpl implements LeaveRepository {
  final LeaveRemoteDataSource remoteDataSource;

  LeaveRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, LeaveBalance>> getLeaveSummary() async {
    try {
      final summaryModel = await remoteDataSource.getLeaveSummary();

      // Convert balance by type from model to entity
      Map<LeaveType, LeaveTypeBalance>? balanceByType;
      if (summaryModel.leaveBalanceByType != null) {
        balanceByType = {};
        summaryModel.leaveBalanceByType!.forEach((key, value) {
          final leaveType = _parseLeaveType(key);
          if (leaveType != null) {
            balanceByType![leaveType] = LeaveTypeBalance(
              used: value.used,
              remaining: value.remaining,
              total: value.total,
            );
          }
        });
      }

      // Convert summary model to LeaveBalance entity
      final leaveBalance = LeaveBalance(
        remainingDays: summaryModel.remainingDays,
        totalDays: summaryModel.totalAllowedDays,
        usedDays: summaryModel.thisYearDays,
        pendingDays: summaryModel.pendingRequests,
        approvedDays: summaryModel.approvedRequests,
        rejectedDays: summaryModel.rejectedRequests,
        balanceByType: balanceByType,
      );

      return Right(leaveBalance);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(
        ServerFailure('An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  LeaveType? _parseLeaveType(String typeString) {
    switch (typeString.toLowerCase()) {
      case 'annual':
        return LeaveType.annual;
      case 'sick':
        return LeaveType.sick;
      case 'personal':
        return LeaveType.personal;
      case 'maternity':
        return LeaveType.maternity;
      case 'unpaid':
        return LeaveType.unpaid;
      default:
        return null;
    }
  }

  @override
  Future<Either<Failure, List<LeaveRequest>>> getLeaveHistory({
    int page = 1,
    int limit = 10,
    String? status,
  }) async {
    try {
      final leaveRequestModels = await remoteDataSource.getLeaveHistory(
        page: page,
        limit: limit,
        status: status,
      );

      // Models extend entities, so we can return them directly
      return Right(leaveRequestModels);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(
        ServerFailure('An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, LeaveRequest>> submitLeaveRequest({
    required LeaveType type,
    required DateTime startDate,
    required DateTime endDate,
    required String reason,
    String? approverId,
  }) async {
    try {
      final requestModel = LeaveRequestModel.createRequest(
        type: type,
        startDate: startDate,
        endDate: endDate,
        reason: reason,
        approverId: approverId,
      );

      final submittedRequest = await remoteDataSource.submitLeaveRequest(
        requestModel,
      );
      return Right(submittedRequest);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(
        ServerFailure('An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, LeaveRequest>> updateLeaveRequest({
    required String id,
    required LeaveType type,
    required DateTime startDate,
    required DateTime endDate,
    required String reason,
  }) async {
    try {
      final requestModel = LeaveRequestModel.createRequest(
        type: type,
        startDate: startDate,
        endDate: endDate,
        reason: reason,
      );

      final updatedRequest = await remoteDataSource.updateLeaveRequest(
        id,
        requestModel,
      );
      return Right(updatedRequest);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(
        ServerFailure('An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, void>> cancelLeaveRequest(String id) async {
    try {
      await remoteDataSource.cancelLeaveRequest(id);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(
        ServerFailure('An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, LeaveRequest>> getLeaveRequestById(String id) async {
    try {
      final leaveRequest = await remoteDataSource.getLeaveRequestById(id);
      return Right(leaveRequest);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(
        ServerFailure('An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> getApprovers() async {
    try {
      final approvers = await remoteDataSource.getApprovers();

      // Convert ApproverModel to Map for easier use in UI
      final approverMaps = approvers
          .map(
            (approver) => {
              'id': approver.id,
              'name': approver.fullname,
              'department': approver.department,
              'position': approver.position,
              'email': approver.email,
              'role': approver.roleName,
              'displayName': approver.displayName,
              'displayInfo': approver.displayInfo,
            },
          )
          .toList();

      return Right(approverMaps);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(
        ServerFailure('An unexpected error occurred: ${e.toString()}'),
      );
    }
  }
}
