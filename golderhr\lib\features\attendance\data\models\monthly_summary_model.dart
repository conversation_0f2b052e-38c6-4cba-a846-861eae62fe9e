import 'package:golderhr/features/attendance/domain/entities/monthly_summary.dart';

class MonthlySummaryModel extends MonthlySummary {
  const MonthlySummaryModel({
    required super.workDays,
    required super.totalHours,
    required super.overtime,
    required super.daysOff,
  });
  factory MonthlySummaryModel.fromJson(Map<String, dynamic> json) {
    return MonthlySummaryModel(
      workDays: json['workDays'] ?? '0 / 0',
      totalHours: json['totalHours'] ?? '--',
      overtime: json['overtime'] ?? '--',
      daysOff: json['daysOff'] ?? 0,
    );
  }
}
