import 'package:connectivity_plus/connectivity_plus.dart';

/// Abstract class để kiểm tra kết nối mạng
abstract class NetworkInfo {
  Future<bool> get isConnected;
}

/// Implementation của NetworkInfo sử dụng connectivity_plus
class NetworkInfoImpl implements NetworkInfo {
  final Connectivity connectivity;

  NetworkInfoImpl(this.connectivity);

  @override
  Future<bool> get isConnected async {
    final result = await connectivity.checkConnectivity();
    return result != ConnectivityResult.none;
  }
}
