import 'package:equatable/equatable.dart';

class RoleEntity extends Equatable {
  final String id;
  final String name;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const RoleEntity({
    required this.id,
    required this.name,
    this.createdAt,
    this.updatedAt,
  });

  RoleEntity copyWith({
    String? id,
    String? name,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RoleEntity(
      id: id ?? this.id,
      name: name ?? this.name,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [id, name, createdAt, updatedAt];

  @override
  String toString() {
    return 'RoleEntity(id: $id, name: $name, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

class RoleListResult {
  final List<RoleEntity> roles;
  final int totalCount;
  final int currentPage;
  final int totalPages;
  final bool hasNextPage;
  final bool hasPrevPage;

  const RoleListResult({
    required this.roles,
    required this.totalCount,
    required this.currentPage,
    required this.totalPages,
    required this.hasNextPage,
    required this.hasPrevPage,
  });

  factory RoleListResult.fromJson(Map<String, dynamic> json) {
    return RoleListResult(
      roles: (json['roles'] as List<dynamic>?)
              ?.map((roleJson) => RoleEntity(
                    id: roleJson['_id'] ?? roleJson['id'] ?? '',
                    name: roleJson['name'] ?? '',
                    createdAt: roleJson['createdAt'] != null
                        ? DateTime.parse(roleJson['createdAt'])
                        : null,
                    updatedAt: roleJson['updatedAt'] != null
                        ? DateTime.parse(roleJson['updatedAt'])
                        : null,
                  ))
              .toList() ??
          [],
      totalCount: json['totalCount'] ?? 0,
      currentPage: json['currentPage'] ?? 1,
      totalPages: json['totalPages'] ?? 1,
      hasNextPage: json['hasNextPage'] ?? false,
      hasPrevPage: json['hasPrevPage'] ?? false,
    );
  }
}
