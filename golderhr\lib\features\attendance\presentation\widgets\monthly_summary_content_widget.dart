import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';

import '../../domain/entities/monthly_summary.dart';
import 'stat_row_widget.dart';

class MonthlySummaryContentWidget extends StatelessWidget {
  final MonthlySummary monthlyData;

  const MonthlySummaryContentWidget({super.key, required this.monthlyData});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    const accentColor = Color(0xFF6772FF);

    return Column(
      children: [
        StatRowWidget(
          title: l10n.workDays,
          value: monthlyData.workDays,
          icon: Icons.work_history_outlined,
          color: accentColor,
        ),
        StatRowWidget(
          title: l10n.totalHours,
          value: monthlyData.totalHours,
          icon: Icons.timer_outlined,
          color: Colors.green,
        ),
        StatRowWidget(
          title: l10n.overtime,
          value: monthlyData.overtime,
          icon: Icons.add_alarm_outlined,
          color: Colors.orange,
        ),
        StatRowWidget(
          title: l10n.daysOff,
          value: monthlyData.daysOff.toString(),
          icon: Icons.beach_access_outlined,
          color: Colors.teal,
        ),
      ],
    );
  }
}
