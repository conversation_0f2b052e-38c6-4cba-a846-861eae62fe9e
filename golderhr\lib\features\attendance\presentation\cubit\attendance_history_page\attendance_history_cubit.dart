import 'package:bloc/bloc.dart';
import 'package:table_calendar/table_calendar.dart'; // For isSameDay

import '../../../domain/entities/monthly_details.dart';
import '../../../domain/usecases/get_monthly_details.dart';
import 'attendance_history_state.dart';

class AttendanceHistoryCubit extends Cubit<AttendanceHistoryState> {
  final GetMonthlyDetails getMonthlyDetails;

  /// Helper function to check if two DateTime objects represent the same month and year.
  bool _isSameMonth(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month;
  }

  AttendanceHistoryCubit({required this.getMonthlyDetails})
    : super(AttendanceHistoryInitial());

  Future<void> fetchMonthlyDetails(DateTime month) async {
    if (state is AttendanceHistoryLoaded &&
        _isSameMonth((state as AttendanceHistoryLoaded).focusedDay, month)) {
      return;
    }

    emit(AttendanceHistoryLoading());

    final params = MonthlyDetailsParams(year: month.year, month: month.month);
    final result = await getMonthlyDetails(params);

    // attendance_history_cubit.dart

    result.fold(
      (failure) {
        emit(
          const AttendanceHistoryError(
            "Failed to load attendance history. Please try again.",
          ),
        );
      },
      (details) {
        // B1: Thêm kiểm tra an toàn, nếu API trả về danh sách rỗng thì không xử lý gì thêm.
        if (details.dailyDetails.isEmpty) {
          emit(
            AttendanceHistoryLoaded(
              monthlyDetails: details,
              focusedDay: month,
              selectedDayInfo: null, // Không có ngày nào để chọn
            ),
          );
          return;
        }

        // B2: Tìm ngày có thể chọn đầu tiên.
        // Dùng try-catch với firstWhere (không có orElse) là một cách an toàn và rõ ràng.
        AttendanceDayDetail firstSelectableDay;
        try {
          firstSelectableDay = details.dailyDetails.firstWhere(
            (d) => d.status != 'Weekend' && d.status != 'Holiday',
          );
        } catch (e) {
          // Nếu không tìm thấy ngày nào phù hợp (VD: cả tháng là cuối tuần/ngày lễ),
          // thì mặc định chọn ngày đầu tiên trong danh sách.
          firstSelectableDay = details.dailyDetails.first;
        }

        // B3: Emit state với dữ liệu đã được xử lý.
        emit(
          AttendanceHistoryLoaded(
            monthlyDetails: details,
            focusedDay: month,
            selectedDayInfo: firstSelectableDay,
          ),
        );
      },
    );
  }

  void selectDay(DateTime selectedDay) {
    if (state is AttendanceHistoryLoaded) {
      final currentState = state as AttendanceHistoryLoaded;

      // Tìm thông tin ngày đã chọn, nếu không có thì dùng ngày hiện tại đang chọn
      var detailsForDay = currentState.selectedDayInfo;

      try {
        final match = currentState.monthlyDetails.dailyDetails.firstWhere(
          (dayDetail) => isSameDay(dayDetail.date, selectedDay),
        );
        detailsForDay = match;
      } catch (_) {}

      emit(
        currentState.copyWith(
          selectedDayInfo: () => detailsForDay,
          focusedDay: selectedDay,
        ),
      );
    }
  }
}
