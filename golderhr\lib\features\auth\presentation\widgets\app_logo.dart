import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

class AppLogo extends StatelessWidget {
  final String image;
  final double? size;

  const AppLogo({
    super.key,
    this.image = "assets/images/splash.jpg",
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    final logoSize = size ?? context.responsive.scaleWidth(80);

    return Container(
      width: logoSize,
      height: logoSize,
      decoration: BoxDecoration(
        color: Colors.white.withAlpha((0.1 * 255).round()),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withAlpha((0.2 * 255).round()),
          width: 1,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(
          logoSize * 0.1,
        ), // Add padding to make image look better
        child: Image.asset(
          image,
          width: logoSize * 0.8,
          height: logoSize * 0.8,
          fit: BoxFit.contain,
        ),
      ),
    );
  }
}
