import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../domain/entities/leave_request.dart';
import '../../domain/usecases/get_all_leave_requests.dart';
import '../../domain/usecases/approve_leave_request.dart';
import '../../domain/usecases/reject_leave_request.dart';

part 'leave_admin_state.dart';

class LeaveAdminCubit extends Cubit<LeaveAdminState> {
  final GetAllLeaveRequests getAllLeaveRequests;
  final ApproveLeaveRequest approveLeaveRequest;
  final RejectLeaveRequest rejectLeaveRequest;

  LeaveAdminCubit({
    required this.getAllLeaveRequests,
    required this.approveLeaveRequest,
    required this.rejectLeaveRequest,
  }) : super(const LeaveAdminState());

  Future<void> loadAllRequests({
    int page = 1,
    int limit = 10,
    String? status,
    bool isRefresh = false,
  }) async {
    print(
      '🔍 [LEAVE_ADMIN_CUBIT] loadAllRequests called - page: $page, status: $status, isRefresh: $isRefresh',
    );

    if (isRefresh) {
      emit(state.copyWith(isLoading: true, requests: []));
    } else if (page == 1) {
      emit(state.copyWith(isLoading: true));
    } else {
      emit(state.copyWith(isLoadingMore: true));
    }

    print('🔍 [LEAVE_ADMIN_CUBIT] Calling getAllLeaveRequests use case...');
    final result = await getAllLeaveRequests(
      GetAllLeaveRequestsParams(page: page, limit: limit, status: status),
    );

    result.fold(
      (failure) {
        print('❌ [LEAVE_ADMIN_CUBIT] Failed to load requests: ${failure.message}');
        emit(
          state.copyWith(
            isLoading: false,
            isLoadingMore: false,
            errorMessage: 'Failed to load requests: ${failure.message}',
          ),
        );
      },
      (requests) {
        print(
          '✅ [LEAVE_ADMIN_CUBIT] Successfully loaded ${requests.length} requests',
        );

        List<LeaveRequest> updatedRequests;
        if (page == 1 || isRefresh) {
          updatedRequests = requests;
        } else {
          updatedRequests = [...state.requests, ...requests];
        }

        emit(
          state.copyWith(
            isLoading: false,
            isLoadingMore: false,
            requests: updatedRequests,
            currentPage: page,
            hasMoreData: requests.length >= limit,
            selectedStatus: status,
            errorMessage: null,
          ),
        );
      },
    );
  }

  Future<void> approveRequest(String requestId) async {
    print('🔍 [LEAVE_ADMIN_CUBIT] approveRequest called for ID: $requestId');

    emit(state.copyWith(isProcessing: true));

    final result = await approveLeaveRequest(
      ApproveLeaveRequestParams(requestId: requestId),
    );

    result.fold(
      (failure) {
        print('❌ [LEAVE_ADMIN_CUBIT] Failed to approve request: ${failure.message}');
        emit(
          state.copyWith(
            isProcessing: false,
            errorMessage: 'Failed to approve request: ${failure.message}',
          ),
        );
      },
      (approvedRequest) {
        print('✅ [LEAVE_ADMIN_CUBIT] Successfully approved request: $requestId');

        // Update the request in the list
        final updatedRequests = state.requests.map((request) {
          if (request.id == requestId) {
            return approvedRequest;
          }
          return request;
        }).toList();

        emit(
          state.copyWith(
            isProcessing: false,
            requests: updatedRequests,
            successMessage: 'Request approved successfully',
            errorMessage: null,
          ),
        );
      },
    );
  }

  Future<void> rejectRequest(String requestId, String rejectionReason) async {
    print(
      '🔍 [LEAVE_ADMIN_CUBIT] rejectRequest called for ID: $requestId with reason: $rejectionReason',
    );

    emit(state.copyWith(isProcessing: true));

    final result = await rejectLeaveRequest(
      RejectLeaveRequestParams(
        requestId: requestId,
        rejectionReason: rejectionReason,
      ),
    );

    result.fold(
      (failure) {
        print('❌ [LEAVE_ADMIN_CUBIT] Failed to reject request: ${failure.message}');
        emit(
          state.copyWith(
            isProcessing: false,
            errorMessage: 'Failed to reject request: ${failure.message}',
          ),
        );
      },
      (rejectedRequest) {
        print('✅ [LEAVE_ADMIN_CUBIT] Successfully rejected request: $requestId');

        // Update the request in the list
        final updatedRequests = state.requests.map((request) {
          if (request.id == requestId) {
            return rejectedRequest;
          }
          return request;
        }).toList();

        emit(
          state.copyWith(
            isProcessing: false,
            requests: updatedRequests,
            successMessage: 'Request rejected successfully',
            errorMessage: null,
          ),
        );
      },
    );
  }

  void filterByStatus(String? status) {
    print('🔍 [LEAVE_ADMIN_CUBIT] filterByStatus called with status: $status');
    loadAllRequests(page: 1, status: status, isRefresh: true);
  }

  void clearMessages() {
    emit(state.copyWith(errorMessage: null, successMessage: null));
  }

  void refresh() {
    print('🔍 [LEAVE_ADMIN_CUBIT] refresh called');
    loadAllRequests(page: 1, status: state.selectedStatus, isRefresh: true);
  }

  void loadMore() {
    if (!state.isLoadingMore && state.hasMoreData) {
      print(
        '🔍 [LEAVE_ADMIN_CUBIT] loadMore called - next page: ${state.currentPage + 1}',
      );
      loadAllRequests(
        page: state.currentPage + 1,
        status: state.selectedStatus,
      );
    }
  }
}
