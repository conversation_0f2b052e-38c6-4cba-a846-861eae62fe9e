import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/user_entity.dart';

// either left right
abstract class AuthRepository {
  Future<Either<Failure, UserEntity>> login(Map<String, dynamic> credentials);
  Future<Either<Failure, UserEntity>> register(Map<String, dynamic> userData);
  Future<Either<Failure, void>> logout();
  Future<Either<Failure, UserEntity?>> getAuthStatus();
  Future<Either<Failure, void>> forgotPassword(String email);
  Future<Either<Failure, String>> verifyOtp(String otp);
  Future<Either<Failure, void>> resendOtp();
  Future<Either<Failure, void>> resetPassword({
    required String resetToken,
    required String newPassword,
  });
  Future<Either<Failure, UserEntity?>> getCachedUser();
}
