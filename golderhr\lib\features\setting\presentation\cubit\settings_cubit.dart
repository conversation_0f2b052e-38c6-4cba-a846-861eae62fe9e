// features/setting/presentation/cubit/settings_cubit.dart

import 'package:flutter_bloc/flutter_bloc.dart';
import 'settings_state.dart';

class SettingsCubit extends Cubit<SettingsState> {
  SettingsCubit() : super(const SettingsState());

  // Trong thực tế, bạn có thể load cài đặt đã lưu từ SharedPreferences/GetStorage ở đây
  void loadInitialSettings() {
    // Giả sử load từ storage...
    // final savedState = ...
    // emit(savedState);
  }

  // Mỗi hành động thay đổi sẽ là một hàm riêng biệt
  void toggleFaceRecognition(bool value) {
    emit(state.copyWith(isFaceRecognitionEnabled: value));
    // Ở đây bạn có thể gọi logic để lưu giá trị mới vào storage
    // saveSettingToStorage('faceId', value);
  }

  void toggleBiometricLogin(bool value) {
    emit(state.copyWith(isBiometricLoginEnabled: value));
  }

  void toggleAutoCheckOut(bool value) {
    emit(state.copyWith(isAutoCheckOutEnabled: value));
  }
}
