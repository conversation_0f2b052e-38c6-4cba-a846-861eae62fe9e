import 'package:flutter/material.dart';
import '../../../../shared/widgets/text_field_custom.dart';

class EditableInfoRow extends StatelessWidget {
  final String label;
  final String value;
  final IconData? icon;
  final bool isEditing;
  final TextEditingController? controller;
  final String? hintText;
  final String? Function(String?)? validator;

  const EditableInfoRow({
    super.key,
    required this.label,
    required this.value,
    this.icon,
    required this.isEditing,
    this.controller,
    this.hintText,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    assert(
      !isEditing || controller != null,
      'Controller must be provided when isEditing is true.',
    );

    if (isEditing) {
      controller!.text = value;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: isEditing ? _buildEditMode(context) : _buildViewMode(context),
    );
  }

  Widget _buildViewMode(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(16.0),
      margin: const EdgeInsets.only(bottom: 12.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (icon != null)
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(icon, color: theme.colorScheme.primary, size: 20),
            ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  value.isNotEmpty ? value : '-',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditMode(BuildContext context) {
    return TextFieldCustom(
      controller: controller,
      prefixIcon: icon,
      hintText: hintText ?? 'Enter $label',
      validator:
          validator ??
          (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter $label';
            }
            if (label.contains('Email')) {
              final emailRegExp = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
              if (!emailRegExp.hasMatch(value)) {
                return 'Please enter a valid email';
              }
            }
            if (label.contains('Phone')) {
              final phoneRegExp = RegExp(r'^\+?\d{10,}$');
              if (!phoneRegExp.hasMatch(value)) {
                return 'Please enter a valid phone number';
              }
            }
            return null;
          },
    );
  }
}
