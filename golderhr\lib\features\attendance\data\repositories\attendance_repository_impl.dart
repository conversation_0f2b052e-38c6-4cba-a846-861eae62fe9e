import 'package:dartz/dartz.dart';

// Import các file core và data source

import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';

import '../../domain/entities/attendance_history.dart';
import '../datasources/attendance_remote_data_source.dart';

// Import "bản hợp đồng" từ tầng Domain
import '../../domain/repositories/attendance_repository.dart';

// Import các entities từ Domain
import '../../domain/entities/monthly_details.dart';

import '../../domain/entities/monthly_summary.dart';
import '../../domain/entities/today_summary.dart';
import '../../domain/entities/weekly_summary.dart';

class AttendanceRepositoryImplV1 implements AttendanceRepositoryV1 {
  final AttendanceRemoteDataSourceV1 remoteDataSource;
  // Chúng ta không cần DioClient ở đây vì DataSource đã có nó.
  // Chúng ta cũng không cần LocalDataSource cho feature này.

  AttendanceRepositoryImplV1({required this.remoteDataSource});

  @override
  Future<Either<Failure, TodaySummary>> getTodaySummary() async {
    try {
      final todaySummaryModel = await remoteDataSource.getTodaySummary();
      // Model kế thừa Entity, nên có thể trả về trực tiếp
      return Right(todaySummaryModel);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, WeeklySummary>> getWeeklySummary() async {
    try {
      final weeklySummaryModel = await remoteDataSource.getWeeklySummary();
      return Right(weeklySummaryModel);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, MonthlySummary>> getMonthlySummary() async {
    try {
      final monthlySummaryModel = await remoteDataSource.getMonthlySummary();
      return Right(monthlySummaryModel);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, MonthlyDetails>> getMonthlyDetails({
    required int year,
    required int month,
  }) async {
    try {
      final monthlyDetailsModel = await remoteDataSource.getMonthlyDetails(
        year: year,
        month: month,
      );
      return Right(monthlyDetailsModel);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, PaginatedAttendanceHistory>> getAttendanceHistory({
    required int page,
    required int limit,
  }) async {
    try {
      final historyModel = await remoteDataSource.getAttendanceHistory(
        page: page,
        limit: limit,
      );
      return Right(historyModel);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    }
  }
}
