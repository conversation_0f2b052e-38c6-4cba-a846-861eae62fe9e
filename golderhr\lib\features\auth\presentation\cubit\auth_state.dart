// file: features/auth/presentation/cubit/auth_state.dart

import 'package:equatable/equatable.dart';
import 'package:golderhr/features/auth/domain/entities/user_entity.dart'; // Đảm bảo đường dẫn này đúng

abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

/// Trạng thái ban đầu, khi ứng dụng vừa khởi động.
class AuthInitial extends AuthState {}

/// Trạng thái đang tải, khi một hành động xác thực (login, logout, register) đang diễn ra.
class AuthLoading extends AuthState {}

/// Trạng thái đã xác thực thành công.
/// Nó chứa thông tin của người dùng hiện tại.
class Authenticated extends AuthState {
  final UserEntity user;

  const Authenticated({required this.user});

  @override
  List<Object?> get props => [user];
}

/// Trạng thái chưa xác thực hoặc đã đăng xuất.
class Unauthenticated extends AuthState {}

/// Trạng thái xảy ra lỗi trong quá trình xác thực.
/// Chứa thông báo lỗi để hiển thị cho người dùng.
class AuthError extends AuthState {
  final String message;
  const AuthError(this.message);

  @override
  List<Object?> get props => [message];
}

/// Trạng thái thành công chung cho các hành động không cần trả về dữ liệu (ví dụ: resend OTP).
class AuthSuccess extends AuthState {}

/// Các trạng thái cho luồng quên mật khẩu.
class ForgotPasswordOtpSent extends AuthState {}

class ForgotPasswordOtpVerified extends AuthState {
  const ForgotPasswordOtpVerified();
}

class PasswordResetSuccess extends AuthState {}
