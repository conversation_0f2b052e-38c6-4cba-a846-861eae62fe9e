import 'package:golderhr/features/auth/domain/entities/user_entity.dart';

class UserModel extends UserEntity {
  const UserModel({
    required super.id,
    required super.email,
    required super.fullname,
    required super.role,
    super.referenceImageUrl,
    super.avatar,
  });
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['_id'] as String? ?? '',
      email: json['email'] as String? ?? '',
      fullname: json['fullname'] as String? ?? '',
      role: json['role'] is Map<String, dynamic>
          ? json['role']['name'] as String? ?? 'user'
          : json['role'] as String? ?? 'user',
      referenceImageUrl: json['referenceImageUrl'] as String?,
      avatar: json['avatar'] as String?, // Sửa từ 'avatarUrl' thành 'avatar'
    );
  }
  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'email': email,
      'fullname': fullname,
      'role': role,
      'referenceImageUrl': referenceImageUrl,
      'avatarUrl': avatar,
    };
  }

  factory UserModel.empty() {
    return const UserModel(
      id: '',
      email: '',
      fullname: '',
      role: '',
      referenceImageUrl: null,
      avatar: null,
    );
  }
}
