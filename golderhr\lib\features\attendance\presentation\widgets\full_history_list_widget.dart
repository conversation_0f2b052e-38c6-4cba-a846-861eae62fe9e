import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../../core/routes/app_routes.dart';
import '../../domain/entities/attendance_history.dart';
import '../../domain/entities/today_summary.dart';
import 'history_table_header_widget.dart';
import 'attendance_record_row_widget.dart';

class FullHistoryListWidget extends StatelessWidget {
  final TodaySummary todaySummary;
  final List<AttendanceHistoryItem> recentHistory;

  const FullHistoryListWidget({
    super.key,
    required this.todaySummary,
    required this.recentHistory,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final l10n = context.l10n;
    const primaryColor = Color(0xFF4A56E2);
    // T<PERSON><PERSON> danh sách hiển thị: hôm nay + 4 dòng lịch sử gần nhất (tổng 5 dòng)
    final displayList = <Widget>[
      // Dòng đầu tiên: dữ liệu hôm nay
      AttendanceRecordRowWidget(
        date: l10n.today,
        checkIn: todaySummary.checkInTime,
        checkOut: todaySummary.checkOutTime,
        total: todaySummary.totalHours,
        isToday: true,
      ),
    ];

    // Thêm tối đa 4 dòng lịch sử gần nhất
    final historyToShow = recentHistory.take(4).toList();
    for (final historyItem in historyToShow) {
      displayList.add(
        AttendanceRecordRowWidget(
          date: historyItem.date,
          checkIn: historyItem.checkIn,
          checkOut: historyItem.checkOut,
          total: historyItem.totalHours,
        ),
      );
    }

    return Column(
      children: [
        // Card container cho toàn bộ bảng attendance
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              const HistoryTableHeaderWidget(),
              Divider(height: 0.5, thickness: 1),
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: displayList.length,
                itemBuilder: (context, index) => displayList[index],
                separatorBuilder: (context, index) =>
                    const Divider(height: 0.5, thickness: 1),
              ),
            ],
          ),
        ),
        SizedBox(height: responsive.heightPercentage(2)),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            icon: const Icon(Icons.arrow_forward),
            label: Text(l10n.viewFullHistory),
            onPressed: () => context.push(AppRoutes.attendanceHistory),
            style: OutlinedButton.styleFrom(
              foregroundColor: primaryColor,
              side: BorderSide(color: primaryColor.withValues(alpha: 0.3)),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
