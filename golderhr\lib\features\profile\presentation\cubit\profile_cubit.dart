import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import '../../domain/usecases/get_user_profile.dart';
import '../../domain/usecases/update_avatar.dar.dart';
import '../../domain/usecases/update_user_profile.dart';
import '../../domain/usecases/change_password.dart';

import 'profile_state.dart';

class ProfileCubit extends Cubit<ProfileState> {
  final GetUserProfile getUserProfile;
  final UpdateUserProfile updateUserProfile;
  final UploadProfileImage uploadProfileImage;
  final ChangePassword changePasswordUseCase;
  final ImagePicker _imagePicker;

  ProfileCubit({
    required this.getUserProfile,
    required this.updateUserProfile,
    required this.uploadProfileImage,
    required this.changePasswordUseCase,
    ImagePicker? imagePicker,
  }) : _imagePicker = imagePicker ?? ImagePicker(),
       super(ProfileState.initial());

  Future<void> fetchUserProfile() async {
    emit(state.copyWith(status: ProfileStatus.loading));

    try {
      final result = await getUserProfile();
      result.fold(
        (failure) => emit(
          state.copyWith(
            status: ProfileStatus.error,
            errorMessage: failure.toString(),
          ),
        ),
        (userProfile) => emit(
          state.copyWith(
            status: ProfileStatus.loaded,
            userProfile: userProfile,
          ),
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: ProfileStatus.error,
          errorMessage: 'Exception: $e',
        ),
      );
    }
  }

  // Method for profile page - auto upload image
  Future<void> pickProfileImage() async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.gallery,
      );
      if (pickedFile != null) {
        emit(
          state.copyWith(
            newImageFile: pickedFile.path,
            status: ProfileStatus.loading,
          ),
        );
        final result = await uploadProfileImage(pickedFile.path);
        result.fold(
          (failure) => emit(
            state.copyWith(
              status: ProfileStatus.error,
              errorMessage: failure.toString(),
            ),
          ),
          (imageUrl) async {
            final updateResult = await updateUserProfile(
              name: state.userProfile.name,
              email: state.userProfile.email,
              phone: state.userProfile.phone,
              avatarUrl: imageUrl,
            );
            updateResult.fold(
              (failure) => emit(
                state.copyWith(
                  status: ProfileStatus.error,
                  errorMessage: failure.toString(),
                ),
              ),
              (userProfile) => emit(
                state.copyWith(
                  status: ProfileStatus.loaded,
                  userProfile: userProfile,
                  newImageFile: null,
                ),
              ),
            );
          },
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          status: ProfileStatus.error,
          errorMessage: 'errorImagePicker',
        ),
      );
    }
  }

  // Method for edit profile page - only pick image, don't upload yet
  Future<void> pickImageForEdit() async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.gallery,
      );
      if (pickedFile != null) {
        emit(
          state.copyWith(
            newImageFile: pickedFile.path,
            status: ProfileStatus.initial, // Don't set to loading
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(status: ProfileStatus.error, errorMessage: e.toString()),
      );
    }
  }

  Future<void> updateProfile({
    required String name,
    required String email,
    required String phone,
  }) async {
    emit(state.copyWith(status: ProfileStatus.loading));

    String? finalAvatarUrl = state.userProfile.avatarUrl;

    // If there's a new image, upload it first
    if (state.newImageFile != null) {
      final uploadResult = await uploadProfileImage(state.newImageFile!);
      final uploadSuccess = uploadResult.fold(
        (failure) {
          emit(
            state.copyWith(
              status: ProfileStatus.error,
              errorMessage: 'Failed to upload image: ${failure.toString()}',
            ),
          );
          return false;
        },
        (imageUrl) {
          finalAvatarUrl = imageUrl;
          return true;
        },
      );

      if (!uploadSuccess) return; // Stop if image upload failed
    }

    // Update profile with all data
    final result = await updateUserProfile(
      name: name,
      email: email,
      phone: phone,
      avatarUrl: finalAvatarUrl,
    );

    result.fold(
      (failure) => emit(
        state.copyWith(
          status: ProfileStatus.error,
          errorMessage: failure.toString(),
        ),
      ),
      (userProfile) => emit(
        state.copyWith(
          status: ProfileStatus.loaded,
          userProfile: userProfile,
          newImageFile: null, // Clear the selected image
        ),
      ),
    );
  }

  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    emit(state.copyWith(status: ProfileStatus.loading));
    final result = await changePasswordUseCase(
      currentPassword: currentPassword,
      newPassword: newPassword,
    );
    result.fold(
      (failure) => emit(
        state.copyWith(
          status: ProfileStatus.error,
          errorMessage: 'Failed to change password',
        ),
      ),
      (_) => emit(state.copyWith(status: ProfileStatus.loaded)),
    );
  }
}
