import 'package:equatable/equatable.dart';

import '../../../domain/entities/attendance_history.dart';
import '../../../domain/entities/monthly_summary.dart';
import '../../../domain/entities/today_summary.dart';
import '../../../domain/entities/weekly_summary.dart';

/// Lớ<PERSON> cơ sở trừu tượng cho tất cả các trạng thái của [AttendanceCubit].
abstract class AttendanceState extends Equatable {
  const AttendanceState();
  @override
  List<Object> get props => [];
}

class AttendanceInitial extends AttendanceState {}

class AttendanceLoading extends AttendanceState {}

class AttendanceLoaded extends AttendanceState {
  final TodaySummary todaySummary;
  final WeeklySummary weeklySummary;
  final MonthlySummary monthlySummary;
  final List<AttendanceHistoryItem> recentHistory;

  const AttendanceLoaded({
    required this.todaySummary,
    required this.weeklySummary,
    required this.monthlySummary,
    required this.recentHistory,
  });

  @override
  List<Object> get props => [
    todaySummary,
    weeklySummary,
    monthlySummary,
    recentHistory,
  ];
}

/// Trạng thái khi có lỗi xảy ra.
class AttendanceError extends AttendanceState {
  final String message;
  const AttendanceError(this.message);

  @override
  List<Object> get props => [message];
}
