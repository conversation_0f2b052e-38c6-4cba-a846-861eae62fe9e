import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';

import '../../../../core/responsive/responsive.dart';
import '../../domain/entities/weekly_summary.dart';
import 'info_card_widget.dart';
import 'weekly_summary_content_widget.dart';
import 'gradient_circular_progress.dart';

class WeekTabView extends StatelessWidget {
  final WeeklySummary weeklyData;

  const WeekTabView({super.key, required this.weeklyData});

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final l10n = context.l10n;

    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(
        horizontal: responsive.widthPercentage(4.0),
        vertical: responsive.heightPercentage(3.0),
      ),
      child: Column(
        children: [
          InfoCardWidget(
            title: l10n.weeklySummary,
            icon: Icons.calendar_view_week_outlined,
            child: WeeklySummaryContentWidget(weeklyData: weeklyData),
          ),
          SizedBox(height: responsive.heightPercentage(3.0)),
          InfoCardWidget(
            title: l10n.weeklyPerformance,
            icon: Icons.donut_large_outlined,
            child: GradientCircularProgress(
              progress: weeklyData.performance,
              label: "${(weeklyData.performance * 100).toStringAsFixed(0)}%",
              size: responsive.widthPercentage(40.0),
            ),
          ),
        ],
      ),
    );
  }
}
