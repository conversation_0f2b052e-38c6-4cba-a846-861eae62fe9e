// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBMlIBbHCGNBwiSf4eovYBsDPgfzDDDw6M',
    appId: '1:895106658180:web:50b5df8d0a712513f5f7d1',
    messagingSenderId: '895106658180',
    projectId: 'goldenhr-54cda',
    authDomain: 'goldenhr-54cda.firebaseapp.com',
    storageBucket: 'goldenhr-54cda.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBye5nMBw14Pmxt9oa2hYwHdCD3ts75fdw',
    appId: '1:895106658180:android:914f5c61af0e85b6f5f7d1',
    messagingSenderId: '895106658180',
    projectId: 'goldenhr-54cda',
    storageBucket: 'goldenhr-54cda.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyB4-cOAUefQA9rJVNrI5WpP3qxx9nmqoJo',
    appId: '1:895106658180:ios:dbc7c546251674d2f5f7d1',
    messagingSenderId: '895106658180',
    projectId: 'goldenhr-54cda',
    storageBucket: 'goldenhr-54cda.firebasestorage.app',
    iosBundleId: 'com.example.golderhr',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyB4-cOAUefQA9rJVNrI5WpP3qxx9nmqoJo',
    appId: '1:895106658180:ios:dbc7c546251674d2f5f7d1',
    messagingSenderId: '895106658180',
    projectId: 'goldenhr-54cda',
    storageBucket: 'goldenhr-54cda.firebasestorage.app',
    iosBundleId: 'com.example.golderhr',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBMlIBbHCGNBwiSf4eovYBsDPgfzDDDw6M',
    appId: '1:895106658180:web:47cdfc36f72646fdf5f7d1',
    messagingSenderId: '895106658180',
    projectId: 'goldenhr-54cda',
    authDomain: 'goldenhr-54cda.firebaseapp.com',
    storageBucket: 'goldenhr-54cda.firebasestorage.app',
  );
}
