import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get/get.dart';
import '../../../../injection_container.dart' as di;
import '../../../attendance/presentation/widgets/loading_widget.dart';
import '../cubit/overtime_admin_cubit.dart';
import '../widgets/overtime_admin_filter_widget.dart';
import '../widgets/overtime_admin_list_widget.dart';

class OvertimeAdminPage extends StatelessWidget {
  const OvertimeAdminPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<OvertimeAdminCubit>()..loadAllRequests(),
      child: const OvertimeAdminView(),
    );
  }
}

class OvertimeAdminView extends StatefulWidget {
  const OvertimeAdminView({super.key});

  @override
  State<OvertimeAdminView> createState() => _OvertimeAdminViewState();
}

class _OvertimeAdminViewState extends State<OvertimeAdminView> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      context.read<OvertimeAdminCubit>().loadMore();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Overtime Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<OvertimeAdminCubit>().refresh();
            },
          ),
        ],
      ),
      body: BlocConsumer<OvertimeAdminCubit, OvertimeAdminState>(
        listener: (context, state) {
          if (state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: Colors.red,
              ),
            );
            context.read<OvertimeAdminCubit>().clearMessages();
          }

          if (state.successMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.successMessage!),
                backgroundColor: Colors.green,
              ),
            );
            context.read<OvertimeAdminCubit>().clearMessages();
          }
        },
        builder: (context, state) {
          if (state.isLoading && state.requests.isEmpty) {
            return const LoadingWidget();
          }

          return RefreshIndicator(
            onRefresh: () async {
              context.read<OvertimeAdminCubit>().refresh();
            },
            child: Column(
              children: [
                // Filter Section
                const OvertimeAdminFilterWidget(),

                // Requests List
                Expanded(
                  child: state.requests.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.assignment_outlined,
                                size: 64,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'no_requests_found'.tr,
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(color: Colors.grey[600]),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'try_changing_filter'.tr,
                                style: Theme.of(context).textTheme.bodyMedium
                                    ?.copyWith(color: Colors.grey[500]),
                              ),
                            ],
                          ),
                        )
                      : OvertimeAdminListWidget(
                          requests: state.requests,
                          scrollController: _scrollController,
                          isLoadingMore: state.isLoadingMore,
                          isProcessing: state.isProcessing,
                        ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
