import 'package:equatable/equatable.dart';

class TodaySummary extends Equatable {
  final bool isCheckedIn;
  final bool isCheckedOut;
  final String checkInTime;
  final String checkOutTime;
  final String totalHours;
  final String overtime;

  const TodaySummary({
    required this.isCheckedIn,
    required this.isCheckedOut,
    required this.checkInTime,
    required this.checkOutTime,
    required this.totalHours,
    required this.overtime,
  });
  @override
  List<Object?> get props => [
    isCheckedIn,
    isCheckedOut,
    checkInTime,
    checkOutTime,
    totalHours,
    overtime,
  ];
}
