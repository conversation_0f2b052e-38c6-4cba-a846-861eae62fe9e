import 'package:flutter/material.dart';
import '../../../../core/responsive/responsive.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/leave_request.dart';
import '../pages/leave_request_details_page.dart';

class LeaveRecordItem extends StatelessWidget {
  final LeaveRequest request;
  final VoidCallback? onTap;

  const LeaveRecordItem({super.key, required this.request, this.onTap});

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final theme = Theme.of(context);

    return InkWell(
      onTap: onTap ?? () => _navigateToDetails(context),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: responsive.padding(all: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getTypeColor(request.type).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getTypeIcon(request.type),
                    color: _getTypeColor(request.type),
                    size: 16,
                  ),
                ),
                SizedBox(width: responsive.widthPercentage(3)),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getTypeDisplayName(request.type),
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontSize: responsive.fontSize(14),
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: responsive.heightPercentage(0.5)),
                      Text(
                        request.dateRange,
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontSize: responsive.fontSize(12),
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusBadge(request.status, theme, responsive),
              ],
            ),
            SizedBox(height: responsive.heightPercentage(1.5)),
            Row(
              children: [
                Icon(
                  Icons.schedule_outlined,
                  size: 14,
                  color: AppColors.textSecondary,
                ),
                SizedBox(width: responsive.widthPercentage(1)),
                Text(
                  '${request.duration} ${request.duration > 1 ? 'days' : 'day'}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontSize: responsive.fontSize(12),
                    color: AppColors.textSecondary,
                  ),
                ),
                const Spacer(),
                if (request.approverName != null) ...[
                  Icon(
                    Icons.person_outline,
                    size: 14,
                    color: AppColors.textSecondary,
                  ),
                  SizedBox(width: responsive.widthPercentage(1)),
                  Text(
                    request.approverName!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontSize: responsive.fontSize(12),
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ],
            ),
            if (request.reason.isNotEmpty) ...[
              SizedBox(height: responsive.heightPercentage(1)),
              Container(
                width: double.infinity,
                padding: responsive.padding(all: 12),
                decoration: BoxDecoration(
                  color: AppColors.background,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  request.reason,
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontSize: responsive.fontSize(12),
                    color: AppColors.textSecondary,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(
    LeaveStatus status,
    ThemeData theme,
    Responsive responsive,
  ) {
    Color color;
    String text;

    switch (status) {
      case LeaveStatus.approved:
        color = AppColors.success;
        text = 'Approved';
        break;
      case LeaveStatus.pending:
        color = AppColors.warning;
        text = 'Pending';
        break;
      case LeaveStatus.rejected:
        color = AppColors.error;
        text = 'Rejected';
        break;
      case LeaveStatus.cancelled:
        color = AppColors.textSecondary;
        text = 'Cancelled';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: theme.textTheme.bodySmall?.copyWith(
          fontSize: responsive.fontSize(10),
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Color _getTypeColor(LeaveType type) {
    switch (type) {
      case LeaveType.annual:
        return AppColors.primaryBlue;
      case LeaveType.sick:
        return AppColors.error;
      case LeaveType.personal:
        return AppColors.warning;
      case LeaveType.maternity:
        return AppColors.primaryGreen;
      case LeaveType.unpaid:
        return AppColors.textSecondary;
    }
  }

  IconData _getTypeIcon(LeaveType type) {
    switch (type) {
      case LeaveType.annual:
        return Icons.beach_access_outlined;
      case LeaveType.sick:
        return Icons.local_hospital_outlined;
      case LeaveType.personal:
        return Icons.person_outline;
      case LeaveType.maternity:
        return Icons.child_care_outlined;
      case LeaveType.unpaid:
        return Icons.money_off_outlined;
    }
  }

  String _getTypeDisplayName(LeaveType type) {
    switch (type) {
      case LeaveType.annual:
        return 'Annual Leave';
      case LeaveType.sick:
        return 'Sick Leave';
      case LeaveType.personal:
        return 'Personal Leave';
      case LeaveType.maternity:
        return 'Maternity Leave';
      case LeaveType.unpaid:
        return 'Unpaid Leave';
    }
  }

  void _navigateToDetails(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => LeaveRequestDetailsPage(request: request),
      ),
    );
  }
}
