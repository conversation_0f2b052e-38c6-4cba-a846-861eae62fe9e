import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/responsive/responsive.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/leave_request.dart';
import '../cubit/leave_cubit.dart';
import '../cubit/leave_state.dart';
import '../widgets/leave_record_item.dart';
import '../widgets/leave_filter_chip.dart';
import 'leave_request_details_page.dart';

class LeaveHistoryPage extends StatefulWidget {
  const LeaveHistoryPage({super.key});

  @override
  State<LeaveHistoryPage> createState() => _LeaveHistoryPageState();
}

class _LeaveHistoryPageState extends State<LeaveHistoryPage> {
  LeaveStatus? _selectedFilter;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      // Load more data when reaching bottom
      // context.read<LeaveCubit>().loadMoreHistory();
    }
  }

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          context.l10n.leaveHistory,
          style: theme.textTheme.titleLarge?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list, color: AppColors.textPrimary),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter Chips
          Container(
            height: 60,
            padding: responsive.padding(horizontal: 16, vertical: 8),
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                LeaveFilterChip(
                  label: context.l10n.all,
                  isSelected: _selectedFilter == null,
                  onTap: () => _updateFilter(null),
                ),
                SizedBox(width: responsive.widthPercentage(2)),
                LeaveFilterChip(
                  label: context.l10n.pending,
                  isSelected: _selectedFilter == LeaveStatus.pending,
                  onTap: () => _updateFilter(LeaveStatus.pending),
                  color: AppColors.warning,
                ),
                SizedBox(width: responsive.widthPercentage(2)),
                LeaveFilterChip(
                  label: context.l10n.approved,
                  isSelected: _selectedFilter == LeaveStatus.approved,
                  onTap: () => _updateFilter(LeaveStatus.approved),
                  color: AppColors.success,
                ),
                SizedBox(width: responsive.widthPercentage(2)),
                LeaveFilterChip(
                  label: context.l10n.rejected,
                  isSelected: _selectedFilter == LeaveStatus.rejected,
                  onTap: () => _updateFilter(LeaveStatus.rejected),
                  color: AppColors.error,
                ),
              ],
            ),
          ),

          // History List
          Expanded(
            child: BlocBuilder<LeaveCubit, LeaveState>(
              builder: (context, state) {
                if (state is LeaveLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (state is LeaveLoaded) {
                  final filteredRequests = _filterRequests(
                    state.recentRequests,
                  );

                  if (filteredRequests.isEmpty) {
                    return _buildEmptyState();
                  }

                  return RefreshIndicator(
                    onRefresh: () async {
                      context.read<LeaveCubit>().refreshData();
                    },
                    child: ListView.builder(
                      controller: _scrollController,
                      padding: responsive.padding(all: 16),
                      itemCount: filteredRequests.length,
                      itemBuilder: (context, index) {
                        final request = filteredRequests[index];
                        return Container(
                          margin: EdgeInsets.only(
                            bottom: responsive.heightPercentage(1.5),
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.textSecondary.withValues(
                                  alpha: 0.05,
                                ),
                                blurRadius: 10,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: LeaveRecordItem(
                            request: request,
                            onTap: () => _showRequestDetails(request),
                          ),
                        );
                      },
                    ),
                  );
                }

                return const Center(child: Text('Something went wrong'));
              },
            ),
          ),
        ],
      ),
    );
  }

  List<LeaveRequest> _filterRequests(List<LeaveRequest> requests) {
    if (_selectedFilter == null) return requests;
    return requests
        .where((request) => request.status == _selectedFilter)
        .toList();
  }

  void _updateFilter(LeaveStatus? filter) {
    setState(() {
      _selectedFilter = filter;
    });
  }

  Widget _buildEmptyState() {
    final responsive = Responsive.of(context);
    final theme = Theme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_note_outlined,
            size: 64,
            color: AppColors.textSecondary.withValues(alpha: 0.5),
          ),
          SizedBox(height: responsive.heightPercentage(2)),
          Text(
            _selectedFilter == null
                ? context.l10n.noLeaveRequestsYet
                : context.l10n.noFilteredRequests(_selectedFilter!.value),
            style: theme.textTheme.titleMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: responsive.heightPercentage(1)),
          Text(
            context.l10n.yourLeaveRequestsWillAppearHere,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Filter by Status',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 20),
            ...LeaveStatus.values.map(
              (status) => ListTile(
                leading: Icon(
                  _getStatusIcon(status),
                  color: _getStatusColor(status),
                ),
                title: Text(_getStatusDisplayName(status)),
                trailing: _selectedFilter == status
                    ? const Icon(Icons.check, color: AppColors.primaryBlue)
                    : null,
                onTap: () {
                  _updateFilter(status);
                  Navigator.pop(context);
                },
              ),
            ),
            ListTile(
              leading: const Icon(Icons.clear_all),
              title: Text(context.l10n.showAll),
              trailing: _selectedFilter == null
                  ? const Icon(Icons.check, color: AppColors.primaryBlue)
                  : null,
              onTap: () {
                _updateFilter(null);
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showRequestDetails(LeaveRequest request) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => LeaveRequestDetailsPage(request: request),
      ),
    );
  }

  IconData _getStatusIcon(LeaveStatus status) {
    switch (status) {
      case LeaveStatus.pending:
        return Icons.pending_actions_outlined;
      case LeaveStatus.approved:
        return Icons.check_circle_outline;
      case LeaveStatus.rejected:
        return Icons.cancel_outlined;
      case LeaveStatus.cancelled:
        return Icons.block_outlined;
    }
  }

  Color _getStatusColor(LeaveStatus status) {
    switch (status) {
      case LeaveStatus.pending:
        return AppColors.warning;
      case LeaveStatus.approved:
        return AppColors.success;
      case LeaveStatus.rejected:
        return AppColors.error;
      case LeaveStatus.cancelled:
        return AppColors.textSecondary;
    }
  }

  String _getStatusDisplayName(LeaveStatus status) {
    switch (status) {
      case LeaveStatus.pending:
        return 'Pending Approval';
      case LeaveStatus.approved:
        return 'Approved';
      case LeaveStatus.rejected:
        return 'Rejected';
      case LeaveStatus.cancelled:
        return 'Cancelled';
    }
  }
}
