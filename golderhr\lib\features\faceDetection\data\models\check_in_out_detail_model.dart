import 'package:golderhr/features/faceDetection/domain/entities/check_in_out_detail_entity.dart';

import 'location_model.dart';

class CheckInOutDetailModel extends CheckInOutDetailEntity {
  const CheckInOutDetailModel({
    required super.time,
    required super.imageUrl,
    required LocationModel super.location,
  });

  factory CheckInOutDetailModel.fromJson(Map<String, dynamic> json) {
    return CheckInOutDetailModel(
      time: DateTime.parse(json['time']),
      imageUrl: json['imageUrl'],
      location: LocationModel.fromJson(json['location']),
    );
  }
}
