enum LeaveType {
  annual('annual'),
  sick('sick'),
  personal('personal'),
  maternity('maternity'),
  unpaid('unpaid');

  const LeaveType(this.value);
  final String value;
}

enum LeaveStatus {
  pending('pending'),
  approved('approved'),
  rejected('rejected'),
  cancelled('cancelled');

  const LeaveStatus(this.value);
  final String value;
}

class LeaveRequest {
  final String id;
  final String employeeId;
  final String employeeName;
  final LeaveType type;
  final DateTime startDate;
  final DateTime endDate;
  final int duration;
  final String reason;
  final LeaveStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? approvedAt;
  final String? approverName;
  final String? approverId;
  final String? assignedApproverId; // Added to match backend
  final String? approvedBy; // Added to match backend
  final String? rejectionReason;

  const LeaveRequest({
    required this.id,
    required this.employeeId,
    required this.employeeName,
    required this.type,
    required this.startDate,
    required this.endDate,
    required this.duration,
    required this.reason,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.approvedAt,
    this.approverName,
    this.approverId,
    this.assignedApproverId,
    this.approvedBy,
    this.rejectionReason,
  });

  String get dateRange {
    if (startDate.day == endDate.day &&
        startDate.month == endDate.month &&
        startDate.year == endDate.year) {
      return '${startDate.day.toString().padLeft(2, '0')}/${startDate.month.toString().padLeft(2, '0')}';
    }
    return '${startDate.day.toString().padLeft(2, '0')}/${startDate.month.toString().padLeft(2, '0')} - ${endDate.day.toString().padLeft(2, '0')}/${endDate.month.toString().padLeft(2, '0')}';
  }

  bool get isPending => status == LeaveStatus.pending;
  bool get isApproved => status == LeaveStatus.approved;
  bool get isRejected => status == LeaveStatus.rejected;
  bool get isCancelled => status == LeaveStatus.cancelled;

  LeaveRequest copyWith({
    String? id,
    String? employeeId,
    String? employeeName,
    LeaveType? type,
    DateTime? startDate,
    DateTime? endDate,
    int? duration,
    String? reason,
    LeaveStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? approvedAt,
    String? approverName,
    String? approverId,
    String? assignedApproverId,
    String? approvedBy,
    String? rejectionReason,
  }) {
    return LeaveRequest(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      employeeName: employeeName ?? this.employeeName,
      type: type ?? this.type,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      duration: duration ?? this.duration,
      reason: reason ?? this.reason,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      approvedAt: approvedAt ?? this.approvedAt,
      approverName: approverName ?? this.approverName,
      approverId: approverId ?? this.approverId,
      assignedApproverId: assignedApproverId ?? this.assignedApproverId,
      approvedBy: approvedBy ?? this.approvedBy,
      rejectionReason: rejectionReason ?? this.rejectionReason,
    );
  }
}
