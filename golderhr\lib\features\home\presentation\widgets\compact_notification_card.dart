import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/core/routes/app_routes.dart';
import 'package:golderhr/shared/widgets/responsive_spacer.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../notification/data/model/notification_model.dart'; // Giữ nguyên model

class CompactNotificationCard extends StatelessWidget {
  final NotificationModel notification;

  const CompactNotificationCard({super.key, required this.notification});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          context.pushNamed(
            AppRoutes.notificationDetailRelative,
            pathParameters: {'id': notification.id},
            extra: notification,
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: context.responsive.padding(vertical: 16.0),
          child: Row(
            children: [
              // Hero Icon vẫn giữ nguyên để có animation đẹp
              Hero(
                tag: 'notification_icon_${notification.id}',
                child: Container(
                  padding: context.responsive.padding(all: 12),
                  decoration: BoxDecoration(
                    color: notification.color.withValues(alpha: 0.1),
                    shape: BoxShape.circle, // Bo tròn icon cho mềm mại hơn
                  ),
                  child: Icon(
                    notification.icon,
                    color: notification.color,
                    size: context.responsive.fontSize(24),
                  ),
                ),
              ),
              ResponsiveSpacer(
                mobileSize: 16,
                tabletSize: 18,
                axis: Axis.horizontal,
              ),

              // Cột chứa Title và Thời gian
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Hero Title
                    Hero(
                      tag: 'notification_title_${notification.id}',
                      child: Material(
                        color: Colors.transparent,
                        child: Text(
                          notification.title,
                          maxLines: 1, // Chỉ hiện 1 dòng trên trang chủ
                          overflow: TextOverflow.ellipsis,
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                        ),
                      ),
                    ),
                    ResponsiveSpacer(mobileSize: 4, tabletSize: 6),
                    // Hero Time
                    Hero(
                      tag: 'notification_time_${notification.id}',
                      child: Material(
                        color: Colors.transparent,
                        child: Text(
                          timeago.format(notification.timestamp, locale: 'vi'),
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: Colors.grey.shade600),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              ResponsiveSpacer(mobileSize: 8, tabletSize: 10),

              // Chỉ dẫn có thể nhấn vào
              const Icon(Icons.chevron_right_rounded, color: Colors.grey),
            ],
          ),
        ),
      ),
    );
  }
}
