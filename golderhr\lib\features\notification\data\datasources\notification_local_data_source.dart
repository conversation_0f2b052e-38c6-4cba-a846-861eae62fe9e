import 'package:flutter/material.dart';

import '../model/notification_model.dart';

abstract class NotificationLocalDataSource {
  Future<List<NotificationModel>> getNotifications();
}

class NotificationLocalDataSourceImpl implements NotificationLocalDataSource {
  @override
  Future<List<NotificationModel>> getNotifications() async {
    // Mô phỏng độ trễ mạng
    await Future.delayed(const Duration(seconds: 1));

    // Dữ liệu giả chất lượng cao
    return _mockNotifications;
  }

  // Dữ liệu giả
  final List<NotificationModel> _mockNotifications = [
    NotificationModel(
      id: '1',
      title: "<PERSON><PERSON><PERSON> cầu nghỉ phép được duyệt",
      message: "<PERSON><PERSON><PERSON> cầu nghỉ ngày 25/12 của bạn đã được chấp thuận.",
      timestamp: DateTime.now().subtract(const Duration(hours: 4)),
      icon: Icons.check_circle_outline_rounded,
      color: Colors.green,
      isImportant: true,
      isRead: false,
      category: NotificationCategory.system,
    ),
    NotificationModel(
      id: '2',
      title: "Chấm công nhận diện khuôn mặt",
      message: "<PERSON>ạn đã chấm công vào thành công lúc 08:02 AM.",
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      icon: Icons.face_retouching_natural_rounded,
      color: Colors.blueAccent,
      isRead: false,
      category: NotificationCategory.customer,
    ),
    NotificationModel(
      id: '3',
      title: "Phiếu lương tháng 11 có sẵn",
      message: "Kiểm tra ngay phiếu lương chi tiết tháng 11 của bạn.",
      timestamp: DateTime.now().subtract(const Duration(days: 1, hours: 8)),
      icon: Icons.account_balance_wallet_rounded,
      color: Colors.orange,
      isImportant: true,
      category: NotificationCategory.system,
    ),
    NotificationModel(
      id: '4',
      title: "Nhắc nhở: Khóa học an toàn lao động",
      message: "Khóa học sẽ bắt đầu lúc 2:00 PM ngày mai.",
      timestamp: DateTime.now().subtract(const Duration(days: 1, hours: 3)),
      icon: Icons.school_rounded,
      color: Colors.purple,
      category: NotificationCategory.customer,
    ),
    NotificationModel(
      id: '6',
      title: "Đánh giá từ khách hàng",
      message: "Chị Lan đã để lại một đánh giá 5 sao cho dịch vụ của bạn.",
      timestamp: DateTime.now().subtract(const Duration(days: 2)),
      icon: Icons.reviews_rounded,
      color: Colors.teal,
      isRead: true,
      category: NotificationCategory.customer,
    ),
  ];
}
