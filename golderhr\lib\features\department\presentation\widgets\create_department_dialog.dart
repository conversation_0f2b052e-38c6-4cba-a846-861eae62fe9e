import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/theme/app_text_styles.dart';
import 'package:iconsax/iconsax.dart';

import '../cubit/department_cubit.dart';
import '../../domain/entities/department_entity.dart';

class CreateDepartmentDialog extends StatefulWidget {
  const CreateDepartmentDialog({super.key});

  @override
  State<CreateDepartmentDialog> createState() => _CreateDepartmentDialogState();
}

class _CreateDepartmentDialogState extends State<CreateDepartmentDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _codeController = TextEditingController();

  String? _selectedParentId;
  List<DepartmentEntity> _parentDepartments = [];

  @override
  void initState() {
    super.initState();
    _loadParentDepartments();
  }

  void _loadParentDepartments() {
    context.read<DepartmentCubit>().loadDepartmentsForDropdown();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.lightTheme;
    final responsive = context.responsive;
    final l10n = context.l10n;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(responsive.defaultRadius),
      ),
      child: Container(
        width: responsive.adaptiveValue<double>(
          mobile: responsive.widthPercentage(90),
          tablet: responsive.widthPercentage(70),
          mobileLandscape: responsive.widthPercentage(80),
          tabletLandscape: responsive.widthPercentage(60),
        ),
        constraints: BoxConstraints(
          maxWidth: responsive.adaptiveValue<double>(
            mobile: 500,
            tablet: 600,
            mobileLandscape: 550,
            tabletLandscape: 650,
          ),
          maxHeight: responsive.heightPercentage(85),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: responsive.padding(all: 20),
              decoration: BoxDecoration(
                color: theme.primaryColor,
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(responsive.defaultRadius),
                ),
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: responsive.adaptiveValue<double>(
                      mobile: 18,
                      tablet: 20,
                      mobileLandscape: 19,
                      tabletLandscape: 22,
                    ),
                    backgroundColor: Colors.white.withValues(alpha: 0.2),
                    child: Icon(
                      Iconsax.building,
                      color: Colors.white,
                      size: responsive.adaptiveValue<double>(
                        mobile: 18,
                        tablet: 20,
                        mobileLandscape: 19,
                        tabletLandscape: 22,
                      ),
                    ),
                  ),
                  SizedBox(width: responsive.scaleWidth(16)),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Create New Department',
                          style: AppTextStyle.bold(
                            context,
                            size: 18,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          'Add a new department to the organization',
                          style: AppTextStyle.regular(
                            context,
                            size: 14,
                            color: Colors.white.withValues(alpha: 0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // Form Content
            Expanded(
              child: BlocConsumer<DepartmentCubit, DepartmentState>(
                listener: (context, state) {
                  if (!state.isProcessing &&
                      !state.hasError &&
                      state.hasSuccess) {
                    Navigator.pop(context);
                  }

                  // Update parent departments list
                  if (state.dropdownDepartments.isNotEmpty) {
                    setState(() {
                      _parentDepartments = state.dropdownDepartments;
                    });
                  }
                },
                builder: (context, state) {
                  if (state.isProcessing) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  return SingleChildScrollView(
                    padding: responsive.responsivePadding,
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Basic Information Section
                          _buildSectionHeader(
                            'Basic Information',
                            Iconsax.building,
                          ),
                          SizedBox(height: responsive.defaultSpacing),

                          _buildTextField(
                            controller: _nameController,
                            label: 'Department Name *',
                            icon: Iconsax.building,
                            validator: (value) =>
                                _validateDepartmentName(value, context),
                            hint:
                                'Enter department name (e.g., Human Resources, IT)',
                          ),

                          SizedBox(height: responsive.defaultSpacing),

                          _buildTextField(
                            controller: _descriptionController,
                            label: 'Description',
                            icon: Iconsax.document_text,
                            hint: 'Enter department description (optional)',
                            maxLines: 3,
                          ),

                          SizedBox(height: responsive.defaultSpacing),

                          _buildTextField(
                            controller: _codeController,
                            label: 'Department Code',
                            icon: Iconsax.code,
                            validator: (value) =>
                                _validateDepartmentCode(value, context),
                            hint: 'Enter department code (e.g., HR, IT, FIN)',
                          ),

                          SizedBox(height: responsive.defaultSpacing * 1.5),

                          // Hierarchy Section
                          _buildSectionHeader(
                            'Hierarchy',
                            Iconsax.hierarchy_square_2,
                          ),
                          SizedBox(height: responsive.defaultSpacing),

                          // Parent Department Dropdown
                          DropdownButtonFormField<String>(
                            value: _selectedParentId,
                            decoration: InputDecoration(
                              labelText: 'Parent Department',
                              labelStyle: AppTextStyle.regular(
                                context,
                                size: 12,
                              ),
                              prefixIcon: Icon(
                                Iconsax.hierarchy_square_2,
                                size: responsive.adaptiveValue<double>(
                                  mobile: 20,
                                  tablet: 22,
                                  mobileLandscape: 21,
                                  tabletLandscape: 24,
                                ),
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(
                                  responsive.defaultRadius,
                                ),
                              ),
                              filled: true,
                              fillColor: Colors.grey.shade50,
                              contentPadding: responsive.padding(
                                horizontal: 16,
                                vertical: 12,
                              ),
                            ),
                            hint: const Text(
                              'Select parent department (optional)',
                            ),
                            items: [
                              const DropdownMenuItem<String>(
                                value: null,
                                child: Text('No Parent (Top Level)'),
                              ),
                              ..._parentDepartments.map((dept) {
                                return DropdownMenuItem<String>(
                                  value: dept.id,
                                  child: Text(dept.name),
                                );
                              }),
                            ],
                            onChanged: (value) {
                              setState(() {
                                _selectedParentId = value;
                              });
                            },
                          ),

                          SizedBox(height: responsive.defaultSpacing),

                          // Info card
                          Container(
                            padding: responsive.padding(all: 12),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade50,
                              borderRadius: BorderRadius.circular(
                                responsive.defaultRadius,
                              ),
                              border: Border.all(color: Colors.blue.shade200),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Iconsax.info_circle,
                                  color: Colors.blue.shade600,
                                  size: responsive.scaleRadius(16),
                                ),
                                SizedBox(width: responsive.scaleWidth(8)),
                                Expanded(
                                  child: Text(
                                    'Department names and codes should be unique. You can create a hierarchy by selecting a parent department.',
                                    style: AppTextStyle.regular(
                                      context,
                                      size: 12,
                                      color: Colors.blue.shade700,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            // Action Buttons
            Container(
              padding: responsive.padding(all: 20),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.vertical(
                  bottom: Radius.circular(responsive.defaultRadius),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        padding: responsive.padding(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            responsive.defaultRadius,
                          ),
                        ),
                      ),
                      child: Text(
                        l10n.cancel,
                        style: AppTextStyle.medium(
                          context,
                          size: responsive.adaptiveValue<double>(
                            mobile: 14,
                            tablet: 16,
                            mobileLandscape: 15,
                            tabletLandscape: 17,
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: responsive.scaleWidth(16)),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _createDepartment,
                      style: ElevatedButton.styleFrom(
                        padding: responsive.padding(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            responsive.defaultRadius,
                          ),
                        ),
                      ),
                      child: Text(
                        'Create Department',
                        style: AppTextStyle.medium(
                          context,
                          size: responsive.adaptiveValue<double>(
                            mobile: 14,
                            tablet: 16,
                            mobileLandscape: 15,
                            tabletLandscape: 17,
                          ),
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    final responsive = context.responsive;

    return Row(
      children: [
        Icon(
          icon,
          size: responsive.adaptiveValue<double>(
            mobile: 18,
            tablet: 20,
            mobileLandscape: 19,
            tabletLandscape: 22,
          ),
          color: Colors.grey[700],
        ),
        SizedBox(width: responsive.scaleWidth(8)),
        Text(
          title,
          style: AppTextStyle.bold(context, size: 16, color: Colors.black87),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? Function(String?)? validator,
    String? hint,
    int maxLines = 1,
  }) {
    final responsive = context.responsive;

    return TextFormField(
      controller: controller,
      validator: validator,
      maxLines: maxLines,
      style: AppTextStyle.regular(context, size: 14),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        labelStyle: AppTextStyle.regular(context, size: 12),
        hintStyle: AppTextStyle.regular(
          context,
          size: 12,
          color: Colors.grey[500],
        ),
        prefixIcon: Icon(
          icon,
          size: responsive.adaptiveValue<double>(
            mobile: 20,
            tablet: 22,
            mobileLandscape: 21,
            tabletLandscape: 24,
          ),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(responsive.defaultRadius),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
        contentPadding: responsive.padding(horizontal: 16, vertical: 12),
      ),
    );
  }

  String? _validateDepartmentName(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return 'Department name is required';
    }
    if (value.trim().length < 2) {
      return 'Department name must be at least 2 characters';
    }
    if (value.trim().length > 100) {
      return 'Department name must be less than 100 characters';
    }
    return null;
  }

  String? _validateDepartmentCode(String? value, BuildContext context) {
    if (value != null && value.trim().isNotEmpty) {
      if (value.trim().length < 2) {
        return 'Department code must be at least 2 characters';
      }
      if (value.trim().length > 10) {
        return 'Department code must be less than 10 characters';
      }
      // Check for valid characters (letters, numbers, hyphens, underscores)
      if (!RegExp(r'^[a-zA-Z0-9\-_]+$').hasMatch(value.trim())) {
        return 'Department code contains invalid characters';
      }
    }
    return null;
  }

  void _createDepartment() {
    if (_formKey.currentState!.validate()) {
      context.read<DepartmentCubit>().createNewDepartment(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        code: _codeController.text.trim().isEmpty
            ? null
            : _codeController.text.trim(),
        parentId: _selectedParentId,
      );
    }
  }
}
