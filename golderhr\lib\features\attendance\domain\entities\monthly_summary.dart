// lib/features/attendance/domain/entities/monthly_summary.dart
import 'package:equatable/equatable.dart';

class MonthlySummary extends Equatable {
  final String workDays; // vd: "18 / 22"
  final String totalHours; // vd: "144h 45m"
  final String overtime; // vd: "8h 15m"
  final int daysOff; // vd: 2

  const MonthlySummary({
    required this.workDays,
    required this.totalHours,
    required this.overtime,
    required this.daysOff,
  });

  @override
  List<Object?> get props => [workDays, totalHours, overtime, daysOff];
}
