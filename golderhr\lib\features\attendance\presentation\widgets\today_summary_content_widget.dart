import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';

import '../../domain/entities/today_summary.dart';
import 'stat_row_widget.dart';

class TodaySummaryContentWidget extends StatelessWidget {
  final TodaySummary todaySummary;

  const TodaySummaryContentWidget({super.key, required this.todaySummary});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    const primaryColor = Color(0xFF4A56E2);

    return Column(
      children: [
        StatRowWidget(
          title: l10n.checkIn,
          value: todaySummary.checkInTime,
          icon: Icons.login,
          color: Colors.green,
        ),
        StatRowWidget(
          title: l10n.checkOut,
          value: todaySummary.checkOutTime,
          icon: Icons.logout,
          color: Colors.redAccent,
        ),
        StatRowWidget(
          title: l10n.totalHours,
          value: todaySummary.totalHours,
          icon: Icons.timer,
          color: primaryColor,
        ),
        StatRowWidget(
          title: l10n.overtime,
          value: todaySummary.overtime,
          icon: Icons.add_circle_outline,
          color: Colors.orange,
        ),
      ],
    );
  }
}
