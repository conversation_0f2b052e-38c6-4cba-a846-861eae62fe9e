import '../../domain/entities/overtime_summary_entity.dart';

class OvertimeSummaryModel extends OvertimeSummaryEntity {
  const OvertimeSummaryModel({
    required super.thisMonthHours,
    required super.thisWeekHours,
    required super.pendingRequests,
    required super.approvedRequests,
    required super.rejectedRequests,
    required super.totalHoursThisYear,
  });

  factory OvertimeSummaryModel.fromJson(Map<String, dynamic> json) {
    return OvertimeSummaryModel(
      thisMonthHours: (json['thisMonthHours'] ?? 0.0).toDouble(),
      thisWeekHours: (json['thisWeekHours'] ?? 0.0).toDouble(),
      pendingRequests: json['pendingRequests'] ?? 0,
      approvedRequests: json['approvedRequests'] ?? 0,
      rejectedRequests: json['rejectedRequests'] ?? 0,
      totalHoursThisYear: (json['totalHoursThisYear'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'thisMonthHours': thisMonthHours,
      'thisWeekHours': thisWeekHours,
      'pendingRequests': pendingRequests,
      'approvedRequests': approvedRequests,
      'rejectedRequests': rejectedRequests,
      'totalHoursThisYear': totalHoursThisYear,
    };
  }

  @override
  OvertimeSummaryModel copyWith({
    double? thisMonthHours,
    double? thisWeekHours,
    int? pendingRequests,
    int? approvedRequests,
    int? rejectedRequests,
    double? totalHoursThisYear,
  }) {
    return OvertimeSummaryModel(
      thisMonthHours: thisMonthHours ?? this.thisMonthHours,
      thisWeekHours: thisWeekHours ?? this.thisWeekHours,
      pendingRequests: pendingRequests ?? this.pendingRequests,
      approvedRequests: approvedRequests ?? this.approvedRequests,
      rejectedRequests: rejectedRequests ?? this.rejectedRequests,
      totalHoursThisYear: totalHoursThisYear ?? this.totalHoursThisYear,
    );
  }
}
