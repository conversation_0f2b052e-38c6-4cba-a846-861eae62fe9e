import 'package:flutter_cache_manager/flutter_cache_manager.dart';

/// Custom cache manager for profile images with optimized settings
class ProfileImageCacheManager {
  static const key = 'profile_images';

  static CacheManager instance = CacheManager(
    Config(
      key,
      stalePeriod: const Duration(days: 7), // Cache for 7 days
      maxNrOfCacheObjects: 100, // Maximum 100 cached images
      repo: JsonCacheInfoRepository(databaseName: key),
      fileService: HttpFileService(),
    ),
  );
}

/// Custom cache manager for general images with different settings
class GeneralImageCacheManager {
  static const key = 'general_images';

  static CacheManager instance = CacheManager(
    Config(
      key,
      stalePeriod: const Duration(days: 3), // Cache for 3 days
      maxNrOfCacheObjects: 200, // Maximum 200 cached images
      repo: JsonCacheInfoRepository(databaseName: key),
      fileService: HttpFileService(),
    ),
  );
}

/// Utility class for cache management
class ImageCacheUtils {
  /// Clear all cached images
  static Future<void> clearAllCache() async {
    await ProfileImageCacheManager.instance.emptyCache();
    await GeneralImageCacheManager.instance.emptyCache();
  }

  /// Clear only profile images cache
  static Future<void> clearProfileCache() async {
    await ProfileImageCacheManager.instance.emptyCache();
  }

  /// Clear only general images cache
  static Future<void> clearGeneralCache() async {
    await GeneralImageCacheManager.instance.emptyCache();
  }

  /// Get cache size information
  static Future<Map<String, int>> getCacheInfo() async {
    final profileCacheObjects = await ProfileImageCacheManager.instance
        .getFileFromCache('');
    final generalCacheObjects = await GeneralImageCacheManager.instance
        .getFileFromCache('');

    return {
      'profile_cache_count': profileCacheObjects != null ? 1 : 0,
      'general_cache_count': generalCacheObjects != null ? 1 : 0,
    };
  }
}
