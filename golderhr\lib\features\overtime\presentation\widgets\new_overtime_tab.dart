import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../../l10n/app_localizations.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/widgets/wheel_time_picker.dart';
import '../../../../shared/widgets/show_custom_snackBar.dart';
import '../../../../shared/utils/validators.dart';
import '../../domain/entities/overtime_request_entity.dart';
import '../cubit/overtime_cubit.dart';
import '../cubit/overtime_state.dart';
import 'approver_selection_widget.dart';

class NewOvertimeTab extends StatefulWidget {
  const NewOvertimeTab({super.key});

  @override
  State<NewOvertimeTab> createState() => _NewOvertimeTabState();
}

class _NewOvertimeTabState extends State<NewOvertimeTab> {
  final _formKey = GlobalKey<FormState>();
  final _dateController = TextEditingController();
  final _startTimeController = TextEditingController();
  final _endTimeController = TextEditingController();
  final _reasonController = TextEditingController();

  DateTime? _selectedDate;
  TimeOfDay? _selectedStartTime;
  TimeOfDay? _selectedEndTime;
  OvertimeType _selectedType = OvertimeType.regular;
  ApproverEntity? _selectedApprover;

  @override
  void dispose() {
    _dateController.dispose();
    _startTimeController.dispose();
    _endTimeController.dispose();
    _reasonController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    // Load approvers when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<OvertimeCubit>().loadApprovers();
    });
  }

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final theme = Theme.of(context);
    final l10n = context.l10n;

    return BlocListener<OvertimeCubit, OvertimeState>(
      listener: (context, state) {
        if (state.errorMessage != null) {
          showTopSnackBar(
            context,
            title: l10n.error,
            message: state.errorMessage!,
            isError: true,
          );
          context.read<OvertimeCubit>().clearMessages();
        }

        if (state.successMessage != null) {
          showTopSnackBar(
            context,
            title: l10n.success,
            message: state.successMessage!,
            isError: false,
          );
          context.read<OvertimeCubit>().clearMessages();
          // Clear the form after successful submission
          _clearForm();
        }
      },
      child: GestureDetector(
        onTap: () {
          // Dismiss keyboard when tapping outside
          FocusScope.of(context).unfocus();
        },
        child: SingleChildScrollView(
          keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
          padding: EdgeInsets.only(
            left: responsive.widthPercentage(4.0),
            right: responsive.widthPercentage(4.0),
            top: responsive.heightPercentage(2.0),
            bottom:
                MediaQuery.of(context).viewInsets.bottom +
                responsive.heightPercentage(10.0),
          ),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: responsive.heightPercentage(1.0)),

                // Header with icon and title
                _buildHeaderSection(responsive, theme, l10n),
                SizedBox(height: responsive.heightPercentage(2.0)),

                _buildSectionCard(responsive, theme, l10n.overtimeDetails, [
                  Row(
                    children: [
                      Expanded(
                        child: _buildDateTimeField(
                          responsive,
                          theme,
                          l10n.date,
                          _dateController.text,
                          Icons.calendar_today_outlined,
                          onTap: () => _selectDate(context),
                        ),
                      ),
                      SizedBox(width: responsive.widthPercentage(4.0)),
                      Expanded(
                        child: _buildDropdownField(
                          responsive,
                          theme,
                          l10n.overtimeType,
                          _selectedType,
                          _getOvertimeTypeOptions(l10n),
                          (value) => setState(() => _selectedType = value!),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: responsive.heightPercentage(2.0)),
                  Row(
                    children: [
                      Expanded(
                        child: _buildDateTimeField(
                          responsive,
                          theme,
                          l10n.startTime,
                          _startTimeController.text,
                          Icons.access_time,
                          onTap: () => _selectStartTime(context),
                        ),
                      ),
                      SizedBox(width: responsive.widthPercentage(4)),
                      Expanded(
                        child: _buildDateTimeField(
                          responsive,
                          theme,
                          l10n.endTime,
                          _endTimeController.text,
                          Icons.access_time_filled_outlined,
                          onTap: () => _selectEndTime(context),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: responsive.heightPercentage(2)),
                  CustomTextField(
                    controller: _reasonController,
                    label: l10n.reason,
                    maxLines: 2,
                    validator: (value) =>
                        Validators.validateOvertimeReason(value, context),
                  ),
                ]),
                SizedBox(height: responsive.heightPercentage(2)),
                ApproverSelectionWidget(
                  selectedApprover: _selectedApprover,
                  onApproverSelected: (approver) {
                    setState(() {
                      _selectedApprover = approver;
                    });
                  },
                ),
                SizedBox(height: responsive.heightPercentage(3)),
                BlocBuilder<OvertimeCubit, OvertimeState>(
                  builder: (context, state) {
                    return SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: state.isSubmitting ? null : _submitRequest,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryBlue,
                          padding: responsive.padding(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                        ),
                        child: state.isSubmitting
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: const CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2,
                                    ),
                                  ),
                                  SizedBox(
                                    width: responsive.widthPercentage(2),
                                  ),
                                  Text(
                                    l10n.submitting,
                                    style: theme.textTheme.titleMedium
                                        ?.copyWith(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                  ),
                                ],
                              )
                            : Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.send_outlined,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                  SizedBox(
                                    width: responsive.widthPercentage(2),
                                  ),
                                  Text(
                                    l10n.submitRequest,
                                    style: theme.textTheme.titleMedium
                                        ?.copyWith(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                  ),
                                ],
                              ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderSection(
    Responsive responsive,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return Container(
      padding: responsive.padding(all: 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryBlue.withValues(alpha: 0.1),
            AppColors.primaryBlue.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.primaryBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.schedule_rounded,
              color: AppColors.primaryBlue,
              size: 24,
            ),
          ),
          SizedBox(width: responsive.widthPercentage(4)),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  l10n.newRequest,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(height: responsive.heightPercentage(0.5)),
                Text(
                  l10n.fillDetailsToSubmit,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard(
    Responsive responsive,
    ThemeData theme,
    String title,
    List<Widget> children,
  ) {
    return Container(
      padding: responsive.padding(all: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryBlue.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: AppColors.primaryBlue.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.edit_note_rounded,
                  color: AppColors.primaryBlue,
                  size: 20,
                ),
              ),
              SizedBox(width: responsive.widthPercentage(2)),
              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(height: responsive.heightPercentage(2.5)),
          ...children,
        ],
      ),
    );
  }

  Widget _buildDateTimeField(
    Responsive responsive,
    ThemeData theme,
    String label,
    String value,
    IconData icon, {
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: responsive.padding(all: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(
            color: value.isEmpty
                ? AppColors.textSecondary.withValues(alpha: 0.3)
                : AppColors.primaryBlue.withValues(alpha: 0.5),
            width: value.isEmpty ? 1 : 2,
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: value.isNotEmpty
              ? [
                  BoxShadow(
                    color: AppColors.primaryBlue.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          children: [
            Icon(icon, color: AppColors.textSecondary),
            SizedBox(width: responsive.widthPercentage(3)),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: responsive.heightPercentage(0.5)),
                  Text(
                    value.isEmpty ? '${context.l10n.select} $label' : value,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: value.isEmpty
                          ? AppColors.textSecondary.withValues(alpha: 0.7)
                          : AppColors.textPrimary,
                      fontWeight: value.isEmpty
                          ? FontWeight.normal
                          : FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDropdownField<T>(
    Responsive responsive,
    ThemeData theme,
    String label,
    T value,
    List<DropdownMenuItem<T>> items,
    ValueChanged<T?> onChanged,
  ) {
    return Container(
      padding: responsive.padding(horizontal: 16, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(
          color: AppColors.primaryBlue.withValues(alpha: 0.3),
          width: 1.5,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryBlue.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: responsive.heightPercentage(0.5)),
          DropdownButton<T>(
            value: value,
            items: items.map((item) {
              return DropdownMenuItem<T>(
                value: item.value,
                child: Row(
                  children: [
                    Icon(
                      Icons.category_rounded,
                      size: 16,
                      color: AppColors.primaryBlue,
                    ),
                    const SizedBox(width: 8),
                    Expanded(child: item.child),
                  ],
                ),
              );
            }).toList(),
            onChanged: onChanged,
            isExpanded: true,
            underline: const SizedBox(),
            icon: Icon(
              Icons.keyboard_arrow_down_rounded,
              color: AppColors.primaryBlue,
            ),
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
            dropdownColor: Colors.white,
          ),
        ],
      ),
    );
  }

  List<DropdownMenuItem<OvertimeType>> _getOvertimeTypeOptions(
    AppLocalizations l10n,
  ) {
    return [
      DropdownMenuItem(
        value: OvertimeType.regular,
        child: Text(l10n.regularOvertime),
      ),
      DropdownMenuItem(
        value: OvertimeType.weekend,
        child: Text(l10n.weekendOvertime),
      ),
      DropdownMenuItem(
        value: OvertimeType.holiday,
        child: Text(l10n.holidayOvertime),
      ),
    ];
  }

  Future<void> _selectDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 30)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
        _dateController.text = '${date.day}/${date.month}/${date.year}';
      });
    }
  }

  Future<void> _selectStartTime(BuildContext context) async {
    showDialog(
      context: context,
      builder: (context) => WheelTimePicker(
        initialTime: _selectedStartTime ?? TimeOfDay.now(),
        title: context.l10n.startTime,
        onTimeSelected: (time) {
          setState(() {
            _selectedStartTime = time;
            _startTimeController.text = time.format(context);
          });
        },
      ),
    );
  }

  Future<void> _selectEndTime(BuildContext context) async {
    showDialog(
      context: context,
      builder: (context) => WheelTimePicker(
        initialTime: _selectedEndTime ?? _selectedStartTime ?? TimeOfDay.now(),
        title: context.l10n.endTime,
        onTimeSelected: (time) {
          setState(() {
            _selectedEndTime = time;
            _endTimeController.text = time.format(context);
          });
        },
      ),
    );
  }

  void _submitRequest() {
    if (!_formKey.currentState!.validate()) return;

    // Validate required fields
    if (_selectedDate == null ||
        _selectedStartTime == null ||
        _selectedEndTime == null) {
      showTopSnackBar(
        context,
        title: context.l10n.error,
        message: context.l10n.pleaseSelectAllFields,
        isError: true,
      );
      return;
    }

    // Validate approver selection
    if (_selectedApprover == null) {
      showTopSnackBar(
        context,
        title: context.l10n.error,
        message: context.l10n.pleaseSelectApprover,
        isError: true,
      );
      return;
    }

    // Validate date is not in the past (allow today)
    final today = DateTime.now();
    final todayOnly = DateTime(today.year, today.month, today.day);
    final selectedDateOnly = DateTime(
      _selectedDate!.year,
      _selectedDate!.month,
      _selectedDate!.day,
    );

    if (selectedDateOnly.isBefore(todayOnly)) {
      showTopSnackBar(
        context,
        title: context.l10n.error,
        message: context.l10n.cannotSelectPastDates,
        isError: true,
      );
      return;
    }

    final startDateTime = DateTime(
      _selectedDate!.year,
      _selectedDate!.month,
      _selectedDate!.day,
      _selectedStartTime!.hour,
      _selectedStartTime!.minute,
    );

    final endDateTime = DateTime(
      _selectedDate!.year,
      _selectedDate!.month,
      _selectedDate!.day,
      _selectedEndTime!.hour,
      _selectedEndTime!.minute,
    );

    // Validate end time is after start time
    if (endDateTime.isBefore(startDateTime) ||
        endDateTime.isAtSameMomentAs(startDateTime)) {
      showTopSnackBar(
        context,
        title: context.l10n.error,
        message: context.l10n.endTimeMustBeAfterStartTime,
        isError: true,
      );
      return;
    }

    // Validate minimum overtime duration (at least 30 minutes)
    final duration = endDateTime.difference(startDateTime);
    if (duration.inMinutes < 30) {
      showTopSnackBar(
        context,
        title: context.l10n.error,
        message: context.l10n.minimumOvertimeDuration,
        isError: true,
      );
      return;
    }

    // Validate maximum overtime duration (not more than 12 hours)
    if (duration.inHours > 12) {
      showTopSnackBar(
        context,
        title: context.l10n.error,
        message: context.l10n.maximumOvertimeDuration,
        isError: true,
      );
      return;
    }

    // Validate time is not in the past for today's date
    if (selectedDateOnly.isAtSameMomentAs(todayOnly)) {
      final now = DateTime.now();
      if (startDateTime.isBefore(now)) {
        showTopSnackBar(
          context,
          title: context.l10n.error,
          message: context.l10n.cannotSelectPastTime,
          isError: true,
        );
        return;
      }
    }

    // Validate reasonable working hours (6 AM to 11 PM)
    if (_selectedStartTime!.hour < 6 || _selectedEndTime!.hour > 23) {
      showTopSnackBar(
        context,
        title: context.l10n.error,
        message: 'Overtime hours should be between 6:00 AM and 11:00 PM',
        isError: true,
      );
      return;
    }

    context.read<OvertimeCubit>().submitRequestWithApprover(
      date: _selectedDate!,
      startTime: startDateTime,
      endTime: endDateTime,
      reason: _reasonController.text.trim(),
      type: _selectedType,
      approver: _selectedApprover,
    );

    // Clear form after successful submission
    _clearForm();
  }

  void _clearForm() {
    _dateController.clear();
    _startTimeController.clear();
    _endTimeController.clear();
    _reasonController.clear();
    setState(() {
      _selectedDate = null;
      _selectedStartTime = null;
      _selectedEndTime = null;
      _selectedType = OvertimeType.regular;
      _selectedApprover = null;
    });
  }
}
