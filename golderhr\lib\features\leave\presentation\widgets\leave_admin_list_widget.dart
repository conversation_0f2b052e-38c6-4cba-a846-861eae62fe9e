import 'package:flutter/material.dart';
import '../../domain/entities/leave_request.dart';
import 'leave_admin_request_card.dart';

class LeaveAdminListWidget extends StatelessWidget {
  final List<LeaveRequest> requests;
  final ScrollController scrollController;
  final bool isLoadingMore;
  final bool isProcessing;

  const LeaveAdminListWidget({
    super.key,
    required this.requests,
    required this.scrollController,
    required this.isLoadingMore,
    required this.isProcessing,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: requests.length + (isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == requests.length) {
          // Loading more indicator
          return Container(
            padding: const EdgeInsets.all(24),
            child: Center(
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      shape: BoxShape.circle,
                    ),
                    child: const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Loading more requests...',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        final request = requests[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: LeaveAdminRequestCard(
            request: request,
            isProcessing: isProcessing,
            animationDelay: Duration(milliseconds: index * 100),
          ),
        );
      },
    );
  }
}
