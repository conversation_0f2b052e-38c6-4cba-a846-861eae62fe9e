import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/calendar_event.dart';
import '../repositories/calendar_repository.dart';

/// Use case để cập nhật calendar event
class UpdateCalendarEvent
    implements UseCase<CalendarEvent, UpdateCalendarEventParams> {
  final CalendarRepository repository;

  UpdateCalendarEvent(this.repository);

  @override
  Future<Either<Failure, CalendarEvent>> call(
    UpdateCalendarEventParams params,
  ) async {
    return await repository.updateEvent(params.event);
  }
}

/// Parameters cho UpdateCalendarEvent
class UpdateCalendarEventParams extends Equatable {
  final CalendarEvent event;

  const UpdateCalendarEventParams({required this.event});

  @override
  List<Object> get props => [event];
}
