import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/extensions/responsive_extension.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../../../injection_container.dart';
import '../../domain/usecases/get_calendar_events.dart';
import '../../domain/usecases/add_calendar_event.dart';
import '../../domain/usecases/update_calendar_event.dart';
import '../../domain/usecases/delete_calendar_event.dart';
import '../../domain/usecases/get_calendar_summary.dart';
import '../cubit/calendar_cubit.dart';
import '../utils/calendar_dialogs.dart';
import 'calendar_view.dart' as calendar_view;

/// Calendar Page với Clean Architecture và UI hiện đại
class CalendarPage extends StatelessWidget {
  const CalendarPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => CalendarCubit(
        getCalendarEvents: sl<GetCalendarEvents>(),
        addCalendarEvent: sl<AddCalendarEvent>(),
        updateCalendarEvent: sl<UpdateCalendarEvent>(),
        deleteCalendarEvent: sl<DeleteCalendarEvent>(),
        getCalendarSummary: sl<GetCalendarSummary>(),
      )..initialize(),
      child: const CalendarPageView(),
    );
  }
}

/// Main Calendar Page View với responsive design
class CalendarPageView extends StatefulWidget {
  const CalendarPageView({super.key});

  @override
  State<CalendarPageView> createState() => _CalendarPageViewState();
}

class _CalendarPageViewState extends State<CalendarPageView> {
  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildResponsiveAppBar(context, l10n),
      body: const calendar_view.CalendarView(),
      floatingActionButton: _buildResponsiveFloatingActionButton(context, l10n),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  PreferredSizeWidget _buildResponsiveAppBar(
    BuildContext context,
    dynamic l10n,
  ) {
    return AppBar(
      backgroundColor: AppColors.background,
      elevation: 0,
      scrolledUnderElevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: AppColors.textPrimary,
          size: context.rf(24),
        ),
        onPressed: () => context.pop(),
        padding: EdgeInsets.all(context.rw(8)),
      ),
      title: Text(
        l10n.calendarPageTitle,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          color: AppColors.textPrimary,
          fontWeight: FontWeight.bold,
          fontSize: context.rf(20),
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          icon: Icon(
            Icons.search,
            color: AppColors.textPrimary,
            size: context.rf(24),
          ),
          onPressed: () => CalendarDialogs.showSearchDialog(context),
          padding: EdgeInsets.all(context.rw(8)),
          tooltip: l10n.searchEvents,
        ),
        IconButton(
          icon: Icon(
            Icons.refresh,
            color: AppColors.textPrimary,
            size: context.rf(24),
          ),
          onPressed: () => context.read<CalendarCubit>().refresh(),
          padding: EdgeInsets.all(context.rw(8)),
          tooltip: l10n.refresh,
        ),
        SizedBox(width: context.rw(8)),
      ],
    );
  }

  Widget _buildResponsiveFloatingActionButton(
    BuildContext context,
    dynamic l10n,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: context.rh(16), right: context.rw(8)),
      child: FloatingActionButton.extended(
        onPressed: () => CalendarDialogs.showAddEventDialog(context),
        backgroundColor: AppColors.primaryGreen,
        foregroundColor: Colors.white,
        elevation: 8,
        highlightElevation: 12,
        icon: Icon(Icons.add, size: context.rf(20)),
        label: Text(
          l10n.addEvent,
          style: TextStyle(
            fontSize: context.rf(14),
            fontWeight: FontWeight.w600,
          ),
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
    );
  }
}
