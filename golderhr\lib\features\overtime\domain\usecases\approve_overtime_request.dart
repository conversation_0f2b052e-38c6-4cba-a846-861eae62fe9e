import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/overtime_request_entity.dart';
import '../repositories/overtime_admin_repository.dart';

class ApproveOvertimeRequest
    implements UseCase<OvertimeRequestEntity, ApproveOvertimeRequestParams> {
  final OvertimeAdminRepository repository;

  ApproveOvertimeRequest(this.repository);

  @override
  Future<Either<Failure, OvertimeRequestEntity>> call(
    ApproveOvertimeRequestParams params,
  ) async {
    return await repository.approveOvertimeRequest(params.requestId);
  }
}

class ApproveOvertimeRequestParams extends Equatable {
  final String requestId;

  const ApproveOvertimeRequestParams({required this.requestId});

  @override
  List<Object> get props => [requestId];
}
