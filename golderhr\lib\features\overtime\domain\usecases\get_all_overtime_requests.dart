import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/overtime_request_entity.dart';
import '../repositories/overtime_admin_repository.dart';

class GetAllOvertimeRequests
    implements
        UseCase<List<OvertimeRequestEntity>, GetAllOvertimeRequestsParams> {
  final OvertimeAdminRepository repository;

  GetAllOvertimeRequests(this.repository);

  @override
  Future<Either<Failure, List<OvertimeRequestEntity>>> call(
    GetAllOvertimeRequestsParams params,
  ) async {
    return await repository.getAllOvertimeRequests(
      page: params.page,
      limit: params.limit,
      status: params.status,
    );
  }
}

class GetAllOvertimeRequestsParams extends Equatable {
  final int page;
  final int limit;
  final String? status;

  const GetAllOvertimeRequestsParams({
    this.page = 1,
    this.limit = 10,
    this.status,
  });

  @override
  List<Object?> get props => [page, limit, status];
}
