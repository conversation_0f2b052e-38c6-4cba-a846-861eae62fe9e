<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.google.mlkit:face-detection:16.1.7" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.12\transforms\95c1437391274030283f1aa4b1e0543a\transformed\jetified-face-detection-16.1.7\assets"><file name="models_bundled/BCLjoy_200.emd" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\95c1437391274030283f1aa4b1e0543a\transformed\jetified-face-detection-16.1.7\assets\models_bundled\BCLjoy_200.emd"/><file name="models_bundled/BCLlefteyeclosed_200.emd" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\95c1437391274030283f1aa4b1e0543a\transformed\jetified-face-detection-16.1.7\assets\models_bundled\BCLlefteyeclosed_200.emd"/><file name="models_bundled/BCLrighteyeclosed_200.emd" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\95c1437391274030283f1aa4b1e0543a\transformed\jetified-face-detection-16.1.7\assets\models_bundled\BCLrighteyeclosed_200.emd"/><file name="models_bundled/blazeface.tfl" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\95c1437391274030283f1aa4b1e0543a\transformed\jetified-face-detection-16.1.7\assets\models_bundled\blazeface.tfl"/><file name="models_bundled/contours.tfl" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\95c1437391274030283f1aa4b1e0543a\transformed\jetified-face-detection-16.1.7\assets\models_bundled\contours.tfl"/><file name="models_bundled/fssd_25_8bit_gray_v2.tflite" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\95c1437391274030283f1aa4b1e0543a\transformed\jetified-face-detection-16.1.7\assets\models_bundled\fssd_25_8bit_gray_v2.tflite"/><file name="models_bundled/fssd_25_8bit_v2.tflite" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\95c1437391274030283f1aa4b1e0543a\transformed\jetified-face-detection-16.1.7\assets\models_bundled\fssd_25_8bit_v2.tflite"/><file name="models_bundled/fssd_anchors_v2.pb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\95c1437391274030283f1aa4b1e0543a\transformed\jetified-face-detection-16.1.7\assets\models_bundled\fssd_anchors_v2.pb"/><file name="models_bundled/fssd_anchors_v5.pb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\95c1437391274030283f1aa4b1e0543a\transformed\jetified-face-detection-16.1.7\assets\models_bundled\fssd_anchors_v5.pb"/><file name="models_bundled/fssd_medium_8bit_gray_v5.tflite" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\95c1437391274030283f1aa4b1e0543a\transformed\jetified-face-detection-16.1.7\assets\models_bundled\fssd_medium_8bit_gray_v5.tflite"/><file name="models_bundled/fssd_medium_8bit_v5.tflite" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\95c1437391274030283f1aa4b1e0543a\transformed\jetified-face-detection-16.1.7\assets\models_bundled\fssd_medium_8bit_v5.tflite"/><file name="models_bundled/LMprec_600.emd" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\95c1437391274030283f1aa4b1e0543a\transformed\jetified-face-detection-16.1.7\assets\models_bundled\LMprec_600.emd"/><file name="models_bundled/MFT_fssd_accgray.pb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\95c1437391274030283f1aa4b1e0543a\transformed\jetified-face-detection-16.1.7\assets\models_bundled\MFT_fssd_accgray.pb"/><file name="models_bundled/MFT_fssd_fastgray.pb" path="C:\Users\<USER>\.gradle\caches\8.12\transforms\95c1437391274030283f1aa4b1e0543a\transformed\jetified-face-detection-16.1.7\assets\models_bundled\MFT_fssd_fastgray.pb"/></source></dataSet><dataSet config=":sqflite_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\sqflite_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":permission_handler_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\permission_handler_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\path_provider_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":google_mlkit_commons" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\google_mlkit_commons\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":google_mlkit_face_detection" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\google_mlkit_face_detection\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":geocoding_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\geocoding_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_secure_storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\flutter_secure_storage\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_plugin_android_lifecycle" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\flutter_plugin_android_lifecycle\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_native_splash" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\flutter_native_splash\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":firebase_core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\firebase_core\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":firebase_messaging" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\firebase_messaging\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":connectivity_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\connectivity_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":url_launcher_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\url_launcher_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":local_auth_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\local_auth_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":image_picker_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\image_picker_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":geolocator_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\geolocator_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_local_notifications" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\flutter_local_notifications\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\shared_preferences_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":device_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\device_info_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Gordel_HR\golderhr\build\app\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>