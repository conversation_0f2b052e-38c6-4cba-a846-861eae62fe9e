import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/admin_user_entity.dart';
import '../repositories/admin_user_repository.dart';

class GetAllUsersUseCase implements UseCase<AdminUserListResult, GetAllUsersParams> {
  final AdminUserRepository repository;

  GetAllUsersUseCase(this.repository);

  @override
  Future<Either<Failure, AdminUserListResult>> call(GetAllUsersParams params) async {
    return await repository.getAllUsers(
      page: params.page,
      limit: params.limit,
      search: params.search,
      role: params.role,
      department: params.department,
      includeDeleted: params.includeDeleted,
      sortBy: params.sortBy,
      sortOrder: params.sortOrder,
    );
  }
}

class GetAllUsersParams {
  final int page;
  final int limit;
  final String? search;
  final String? role;
  final String? department;
  final bool includeDeleted;
  final String sortBy;
  final String sortOrder;

  GetAllUsersParams({
    this.page = 1,
    this.limit = 10,
    this.search,
    this.role,
    this.department,
    this.includeDeleted = false,
    this.sortBy = 'createdAt',
    this.sortOrder = 'desc',
  });

  factory GetAllUsersParams.fromFilterOptions(
    UserFilterOptions options, {
    int page = 1,
    int limit = 10,
  }) {
    return GetAllUsersParams(
      page: page,
      limit: limit,
      search: options.search,
      role: options.role,
      department: options.department,
      includeDeleted: options.includeDeleted,
      sortBy: options.sortBy.name,
      sortOrder: options.sortOrder.name,
    );
  }
}
