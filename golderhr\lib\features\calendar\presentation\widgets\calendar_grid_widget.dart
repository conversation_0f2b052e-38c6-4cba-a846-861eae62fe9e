import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/extensions/responsive_extension.dart';
import '../../../../shared/theme/app_colors.dart';
import '../cubit/calendar_cubit.dart';
import '../cubit/calendar_state.dart';

/// Widget hiển thị lưới calendar với các ngày trong tháng
class CalendarGridWidget extends StatelessWidget {
  const CalendarGridWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CalendarCubit, CalendarState>(
      builder: (context, state) {
        final cubit = context.read<CalendarCubit>();
        final currentMonth = cubit.currentMonth;
        final selectedDate = cubit.selectedDate;

        return _buildCalendarGrid(context, cubit, currentMonth, selectedDate);
      },
    );
  }

  Widget _buildCalendarGrid(
    BuildContext context,
    CalendarCubit cubit,
    DateTime currentMonth,
    DateTime selectedDate,
  ) {
    final firstDayOfMonth = DateTime(currentMonth.year, currentMonth.month, 1);
    final lastDayOfMonth = DateTime(
      currentMonth.year,
      currentMonth.month + 1,
      0,
    );
    final firstDayWeekday = (firstDayOfMonth.weekday == 7)
        ? 0
        : firstDayOfMonth.weekday;
    final daysInMonth = lastDayOfMonth.day;

    List<Widget> dayWidgets = [];

    // Thêm các ô trống cho những ngày trước ngày đầu tiên của tháng
    for (int i = 0; i < firstDayWeekday; i++) {
      dayWidgets.add(const SizedBox.shrink());
    }

    // Thêm các ngày trong tháng
    for (int day = 1; day <= daysInMonth; day++) {
      final date = DateTime(currentMonth.year, currentMonth.month, day);
      dayWidgets.add(_buildDayCell(context, cubit, date, selectedDate));
    }

    // Thêm các ô trống để hoàn thành tuần cuối
    while (dayWidgets.length % 7 != 0) {
      dayWidgets.add(const SizedBox.shrink());
    }

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 7,
      children: dayWidgets,
    );
  }

  Widget _buildDayCell(
    BuildContext context,
    CalendarCubit cubit,
    DateTime date,
    DateTime selectedDate,
  ) {
    final hasEvents = cubit.hasEventsOnDate(date);
    final isSelected = _isSameDay(date, selectedDate);
    final isToday = _isSameDay(date, DateTime.now());
    final isPastDate = date.isBefore(
      DateTime.now().subtract(const Duration(days: 1)),
    );

    return GestureDetector(
      onTap: () => cubit.selectDate(date),
      child: Container(
        margin: EdgeInsets.all(context.rw(2)),
        decoration: BoxDecoration(
          color: _getCellBackgroundColor(isSelected, isToday, isPastDate),
          borderRadius: BorderRadius.circular(8),
          border: _getCellBorder(isToday, isSelected),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: AppColors.primaryBlue.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Số ngày
            Text(
              date.day.toString(),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: _getFontWeight(isToday, isSelected),
                color: _getTextColor(isSelected, isToday, isPastDate),
              ),
            ),
            // Indicator cho events
            if (hasEvents)
              Positioned(
                bottom: context.rh(4),
                child: _buildEventIndicator(context, isSelected),
              ),
          ],
        ),
      ),
    );
  }

  Color _getCellBackgroundColor(
    bool isSelected,
    bool isToday,
    bool isPastDate,
  ) {
    if (isSelected) {
      return AppColors.primaryBlue;
    } else if (isToday) {
      return AppColors.primaryGreen.withOpacity(0.15);
    } else if (isPastDate) {
      return Colors.grey.withOpacity(0.05);
    }
    return Colors.transparent;
  }

  Border? _getCellBorder(bool isToday, bool isSelected) {
    if (isToday && !isSelected) {
      return Border.all(color: AppColors.primaryGreen, width: 2);
    }
    return null;
  }

  FontWeight _getFontWeight(bool isToday, bool isSelected) {
    if (isToday || isSelected) {
      return FontWeight.bold;
    }
    return FontWeight.normal;
  }

  Color _getTextColor(bool isSelected, bool isToday, bool isPastDate) {
    if (isSelected) {
      return Colors.white;
    } else if (isToday) {
      return AppColors.primaryGreen;
    } else if (isPastDate) {
      return AppColors.textSecondary.withOpacity(0.5);
    }
    return AppColors.textPrimary;
  }

  Widget _buildEventIndicator(BuildContext context, bool isSelected) {
    return Container(
      width: context.rw(6),
      height: context.rw(6),
      decoration: BoxDecoration(
        color: isSelected ? Colors.white.withOpacity(0.8) : AppColors.secondary,
        shape: BoxShape.circle,
      ),
    );
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}
