// lib/features/faceDetection/presentation/pages/face_detection_page.dart

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../../injection_container.dart'; // Import sl
import '../../../../l10n/app_localizations.dart'; // Import l10n
import '../../domain/usecase/check_in_usecase.dart';
import '../../domain/usecase/check_out_usecase.dart';
import '../../domain/usecase/get_today_attendance_usecase.dart';
import '../cubit/face_checkin_cubit.dart';
import '../widgets/action_buttons.dart';
import '../widgets/app_bar.dart';
import '../widgets/image_preview_card.dart';
import '../widgets/location_card.dart';
import '../widgets/status_card.dart';
import '../widgets/welcome_header.dart';
// ... các import khác ...

enum AttendanceMode { checkIn, checkOut }

class FaceDetectionPage extends StatelessWidget {
  final AttendanceMode mode;

  const FaceDetectionPage({super.key, required this.mode});

  @override
  Widget build(BuildContext context) {
    // 1. Lấy l10n một cách an toàn từ context
    final l10n = AppLocalizations.of(context)!;
    final responsive = Responsive.of(context);

    return BlocProvider(
      create: (context) => FaceDetectionCubit(
        checkInUseCase: sl<CheckInUseCase>(), // Lấy UseCase từ sl
        checkOutUseCase: sl<CheckOutUseCase>(), // Lấy UseCase từ sl
        getTodayAttendanceUseCase: sl<GetTodayAttendanceUseCase>(),
        l10n: l10n, // Truyền l10n từ context
      ),
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: const FaceDetectionAppBar(),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: responsive.padding(all: 20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const WelcomeHeader(),
                SizedBox(height: responsive.scaleHeight(30)),
                const LocationCard(),
                SizedBox(height: responsive.scaleHeight(20)),
                const ImagePreviewCard(),
                SizedBox(height: responsive.scaleHeight(30)),
                ActionButtons(),
                SizedBox(height: responsive.scaleHeight(20)),
                const StatusCard(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
