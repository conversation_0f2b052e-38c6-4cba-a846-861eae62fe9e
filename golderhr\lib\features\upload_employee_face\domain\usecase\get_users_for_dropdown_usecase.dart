import 'package:dartz/dartz.dart';
import 'package:golderhr/features/upload_employee_face/domain/entities/user_for_dropdown_entity.dart';
import 'package:golderhr/features/upload_employee_face/domain/reponsitories/upload_face_repository.dart';

import '../../../../core/error/failures.dart';

class GetUsersForDropdown {
  final UploadFaceRepository repository;

  GetUsersForDropdown(this.repository);

  Future<Either<Failure, List<UserForDropdownEntity>>> call() async {
    return await repository.getUsersForDropdown();
  }
}
