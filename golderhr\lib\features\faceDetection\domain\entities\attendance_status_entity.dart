import 'package:equatable/equatable.dart';

enum NextActionType { CHECK_IN, CHECK_OUT, UNKNOWN }

class AttendanceStatusEntity extends Equatable {
  final bool canCheckIn;
  final bool canCheckOut;
  final int checkInsCount;
  final int checkOutsCount;
  final DateTime? lastCheckInTime;
  final DateTime? lastCheckOutTime;
  final NextActionType nextAction;
  final bool hasCheckedIn;
  final bool hasCheckedOut;
  final DateTime? checkInTime;
  final DateTime? checkOutTime;

  const AttendanceStatusEntity({
    required this.canCheckIn,
    required this.canCheckOut,
    required this.checkInsCount,
    required this.checkOutsCount,
    this.lastCheckInTime,
    this.lastCheckOutTime,
    required this.nextAction,
    required this.hasCheckedIn,
    required this.hasCheckedOut,
    this.checkInTime,
    this.checkOutTime,
  });

  @override
  List<Object?> get props => [
    canCheckIn,
    canCheckOut,
    checkInsCount,
    checkOutsCount,
    lastCheckInTime,
    lastCheckOutTime,
    nextAction,
    hasCheckedIn,
    hasCheckedOut,
    checkInTime,
    checkOutTime,
  ];
}
