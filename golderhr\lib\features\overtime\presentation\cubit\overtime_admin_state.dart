part of 'overtime_admin_cubit.dart';

class OvertimeAdminState extends Equatable {
  final bool isLoading;
  final bool isLoadingMore;
  final bool isProcessing;
  final List<OvertimeRequestEntity> requests;
  final int currentPage;
  final bool hasMoreData;
  final String? selectedStatus;
  final String? errorMessage;
  final String? successMessage;

  const OvertimeAdminState({
    this.isLoading = false,
    this.isLoadingMore = false,
    this.isProcessing = false,
    this.requests = const [],
    this.currentPage = 1,
    this.hasMoreData = true,
    this.selectedStatus,
    this.errorMessage,
    this.successMessage,
  });

  OvertimeAdminState copyWith({
    bool? isLoading,
    bool? isLoadingMore,
    bool? isProcessing,
    List<OvertimeRequestEntity>? requests,
    int? currentPage,
    bool? hasMoreData,
    String? selectedStatus,
    String? errorMessage,
    String? successMessage,
  }) {
    return OvertimeAdminState(
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      isProcessing: isProcessing ?? this.isProcessing,
      requests: requests ?? this.requests,
      currentPage: currentPage ?? this.currentPage,
      hasMoreData: hasMoreData ?? this.hasMoreData,
      selectedStatus: selectedStatus ?? this.selectedStatus,
      errorMessage: errorMessage,
      successMessage: successMessage,
    );
  }

  @override
  List<Object?> get props => [
    isLoading,
    isLoadingMore,
    isProcessing,
    requests,
    currentPage,
    hasMoreData,
    selectedStatus,
    errorMessage,
    successMessage,
  ];
}
