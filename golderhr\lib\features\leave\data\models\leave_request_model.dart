import '../../domain/entities/leave_request.dart';

class LeaveRequestModel extends LeaveRequest {
  const LeaveRequestModel({
    required super.id,
    required super.employeeId,
    required super.employeeName,
    required super.type,
    required super.startDate,
    required super.endDate,
    required super.duration,
    required super.reason,
    required super.status,
    required super.createdAt,
    required super.updatedAt,
    super.approvedAt,
    super.approverName,
    super.approverId,
    super.assignedApproverId,
    super.approvedBy,
    super.rejectionReason,
  });

  factory LeaveRequestModel.fromJson(Map<String, dynamic> json) {
    return LeaveRequestModel(
      id: json['_id'] ?? json['id'] ?? '',
      employeeId: json['employeeId'] is Map
          ? json['employeeId']['_id'] ?? ''
          : json['employeeId'] ?? '',
      employeeName:
          json['employeeName'] ??
          (json['employeeId'] is Map
              ? json['employeeId']['fullname'] ?? ''
              : ''),
      type: _parseLeaveType(json['type']),
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      duration: json['duration'] ?? 0,
      reason: json['reason'] ?? '',
      status: _parseLeaveStatus(json['status']),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt'] ?? json['createdAt']),
      approvedAt: json['approvedAt'] != null
          ? DateTime.parse(json['approvedAt'])
          : null,
      approverName: _getApproverName(json),
      approverId: json['approverId'] is Map
          ? json['approverId']['_id'] ?? ''
          : json['approverId'],
      assignedApproverId: json['assignedApproverId'] is Map
          ? json['assignedApproverId']['_id'] ?? ''
          : json['assignedApproverId'],
      approvedBy: json['approvedBy'] is Map
          ? json['approvedBy']['_id'] ?? ''
          : json['approvedBy'],
      rejectionReason: json['rejectionReason'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'employeeId': employeeId,
      'employeeName': employeeName,
      'type': type.value,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'duration': duration,
      'reason': reason,
      'status': status.value,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'approvedAt': approvedAt?.toIso8601String(),
      'approverId': approverId,
      'assignedApproverId': assignedApproverId,
      'approvedBy': approvedBy,
      'rejectionReason': rejectionReason,
    };
  }

  static LeaveType _parseLeaveType(String? type) {
    switch (type) {
      case 'annual':
        return LeaveType.annual;
      case 'sick':
        return LeaveType.sick;
      case 'personal':
        return LeaveType.personal;
      case 'maternity':
        return LeaveType.maternity;
      case 'unpaid':
        return LeaveType.unpaid;
      default:
        return LeaveType.annual;
    }
  }

  static LeaveStatus _parseLeaveStatus(String? status) {
    switch (status) {
      case 'pending':
        return LeaveStatus.pending;
      case 'approved':
        return LeaveStatus.approved;
      case 'rejected':
        return LeaveStatus.rejected;
      case 'cancelled':
        return LeaveStatus.cancelled;
      default:
        return LeaveStatus.pending;
    }
  }

  static String? _getApproverName(Map<String, dynamic> json) {
    // Try to get approver name from populated field
    if (json['approvedBy'] != null && json['approvedBy'] is Map) {
      return json['approvedBy']['fullname'];
    }
    return null;
  }

  // Factory for creating request payload
  factory LeaveRequestModel.createRequest({
    required LeaveType type,
    required DateTime startDate,
    required DateTime endDate,
    required String reason,
    String? approverId,
  }) {
    final now = DateTime.now();
    return LeaveRequestModel(
      id: '',
      employeeId: '', // Will be set by backend
      employeeName: '', // Will be set by backend
      type: type,
      startDate: startDate,
      endDate: endDate,
      duration: 0, // Will be calculated by backend
      reason: reason,
      status: LeaveStatus.pending,
      createdAt: now,
      updatedAt: now,
      approverId: approverId,
    );
  }

  Map<String, dynamic> toCreateRequestJson() {
    return {
      'type': type.value,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'reason': reason,
    };
  }

  Map<String, dynamic> toCreateRequestJsonWithApprover(String? approverId) {
    final json = toCreateRequestJson();
    if (approverId != null && approverId.isNotEmpty) {
      json['approverId'] = approverId;
    }
    return json;
  }
}
