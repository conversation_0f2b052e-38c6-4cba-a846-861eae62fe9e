import 'package:flutter/material.dart';
import '../../../../core/responsive/responsive.dart';
import '../../../../shared/widgets/responsive_spacer.dart';
import 'app_logo.dart';

class AuthWelcomeSection extends StatelessWidget {
  final String title;
  final String subtitle;
  final String? logoImage;
  final bool showLogo;

  const AuthWelcomeSection({
    super.key,
    required this.title,
    required this.subtitle,
    this.logoImage,
    this.showLogo = true,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);

    return SafeArea(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: responsive.isLandscape ? double.infinity : double.infinity,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // App Logo/Image
            if (showLogo) ...[
              SizedBox(
                width: responsive.adaptiveValue<double>(
                  mobile: responsive.scaleWidth(120),
                  tablet: responsive.scaleWidth(150),
                  mobileLandscape: responsive.scaleWidth(100),
                  tabletLandscape: responsive.scaleWidth(100),
                ),
                height: responsive.adaptiveValue<double>(
                  mobile: responsive.scaleHeight(100),
                  tablet: responsive.scaleHeight(150),
                  mobileLandscape: responsive.scaleHeight(200),
                  tabletLandscape: responsive.scaleHeight(200),
                ),
                child: AppLogo(image: logoImage ?? "assets/images/splash.jpg"),
              ),
              ResponsiveSpacer(
                mobileSize: 15,
                tabletSize: 20,
                mobileLandscapeSize: 24,
                tabletLandscapeSize: 16,
              ),
            ],

            // Welcome text
            Text(
              title,
              style: TextStyle(
                fontSize: responsive.adaptiveValue<double>(
                  mobile: responsive.fontSize(28),
                  tablet: responsive.fontSize(32),
                  mobileLandscape: responsive.fontSize(20),
                  tabletLandscape: responsive.fontSize(
                    28,
                  ), // Thay null bằng giá trị mặc định
                ),
                fontWeight: FontWeight.bold,
                color: Colors.white,
                letterSpacing: -0.5,
              ),
              textAlign: TextAlign.center,
            ),
            ResponsiveSpacer(
              mobileSize: 8,
              tabletSize: 4,
              mobileLandscapeSize: 8,
              tabletLandscapeSize: 4,
            ),

            Text(
              subtitle,
              style: TextStyle(
                fontSize: responsive.adaptiveValue<double>(
                  mobile: responsive.fontSize(15),
                  tablet: responsive.fontSize(16),
                  mobileLandscape: responsive.fontSize(15),
                  tabletLandscape: responsive.fontSize(16 * 1.2),
                ),
                color: Colors.white.withAlpha((0.8 * 255).round()),
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
