import 'package:golderhr/features/faceDetection/domain/entities/attendance_record_entity.dart';

import 'check_in_out_detail_model.dart';

class AttendanceRecordModel extends AttendanceRecordEntity {
  const AttendanceRecordModel({
    required super.id,
    required super.employeeId,
    required super.workDate,
    required super.status,
    CheckInOutDetailModel? super.checkIn,
    CheckInOutDetailModel? super.checkOut,
  });

  factory AttendanceRecordModel.fromJson(Map<String, dynamic> json) {
    return AttendanceRecordModel(
      id: json['_id'],
      employeeId: json['employeeId'],
      workDate: DateTime.parse(json['workDate']),
      status: json['status'],
      checkIn: json['checkIn'] != null
          ? CheckInOutDetailModel.fromJson(json['checkIn'])
          : null,
      checkOut: json['checkOut'] != null
          ? CheckInOutDetailModel.fromJson(json['checkOut'])
          : null,
    );
  }
}
