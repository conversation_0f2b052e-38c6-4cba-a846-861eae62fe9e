part of 'leave_admin_cubit.dart';

class LeaveAdminState extends Equatable {
  final bool isLoading;
  final bool isLoadingMore;
  final bool isProcessing;
  final List<LeaveRequest> requests;
  final String? errorMessage;
  final String? successMessage;
  final int currentPage;
  final bool hasMoreData;
  final String? selectedStatus;

  const LeaveAdminState({
    this.isLoading = false,
    this.isLoadingMore = false,
    this.isProcessing = false,
    this.requests = const [],
    this.errorMessage,
    this.successMessage,
    this.currentPage = 1,
    this.hasMoreData = true,
    this.selectedStatus,
  });

  LeaveAdminState copyWith({
    bool? isLoading,
    bool? isLoadingMore,
    bool? isProcessing,
    List<LeaveRequest>? requests,
    String? errorMessage,
    String? successMessage,
    int? currentPage,
    bool? hasMoreData,
    String? selectedStatus,
  }) {
    return LeaveAdminState(
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      isProcessing: isProcessing ?? this.isProcessing,
      requests: requests ?? this.requests,
      errorMessage: errorMessage,
      successMessage: successMessage,
      currentPage: currentPage ?? this.currentPage,
      hasMoreData: hasMoreData ?? this.hasMoreData,
      selectedStatus: selectedStatus ?? this.selectedStatus,
    );
  }

  @override
  List<Object?> get props => [
        isLoading,
        isLoadingMore,
        isProcessing,
        requests,
        errorMessage,
        successMessage,
        currentPage,
        hasMoreData,
        selectedStatus,
      ];
}
