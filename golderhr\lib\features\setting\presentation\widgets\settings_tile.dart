import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

class SettingsTile extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;
  final bool showArrow;

  const SettingsTile({
    super.key,

    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    this.onTap,
    this.showArrow = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final responsive = context.responsive;
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        splashColor: theme.colorScheme.primary.withAlpha((255 * 0.1).round()),
        child: Padding(
          padding: responsive.padding(all: 16),
          child: Row(
            children: [
              Container(
                padding: responsive.padding(all: 10),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Colors.grey.withAlpha((255 * 0.5).round()),
                  ),
                  color: color.withAlpha((255 * 0.3).round()),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(icon, color: color, size: responsive.fontSize(20)),
              ),
              SizedBox(width: responsive.widthPercentage(4)),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: responsive.heightPercentage(0.2)),
                    Text(
                      subtitle,
                      style: theme.textTheme.titleSmall!.copyWith(
                        color: theme.colorScheme.tertiary,
                      ),
                    ),
                  ],
                ),
              ),
              if (showArrow)
                Icon(
                  Icons.arrow_forward_ios,
                  color: theme.colorScheme.primary,
                  size: responsive.fontSize(16),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
