import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Core & Services
import 'core/network/dio_client.dart';
import 'core/services/biometric_service.dart';
import 'core/services/firebase_service.dart';
import 'core/services/flutter_secure_storage.dart';

import 'features/attendance/domain/usecases/get_attendance_history.dart';
import 'features/attendance/domain/usecases/get_monthly_details.dart';
import 'features/attendance/domain/usecases/get_monthly_summary.dart';
import 'features/attendance/domain/usecases/get_today_summary.dart';
import 'features/attendance/domain/usecases/get_weekly_summary.dart';
import 'features/auth/domain/usecases/forgot_password.dart';
import 'features/auth/domain/usecases/get_auth_status.dart';
import 'features/auth/domain/usecases/get_cached_user_usecase.dart';
import 'features/auth/domain/usecases/login.dart';
import 'features/auth/domain/usecases/logout.dart';
import 'features/auth/domain/usecases/register.dart';
import 'features/auth/domain/usecases/resend_otp.dart';
import 'features/auth/domain/usecases/reset_password.dart';
import 'features/auth/domain/usecases/verify_otp.dart';

/// CALENDAR Feature
import 'features/calendar/data/datasources/calendar_remote_data_source.dart';
import 'features/calendar/data/datasources/calendar_local_data_source.dart';
import 'features/calendar/data/repositories/calendar_repository_impl.dart';
import 'features/calendar/domain/repositories/calendar_repository.dart';
import 'features/calendar/domain/usecases/get_calendar_events.dart';
import 'features/calendar/domain/usecases/add_calendar_event.dart';
import 'features/calendar/domain/usecases/update_calendar_event.dart';
import 'features/calendar/domain/usecases/delete_calendar_event.dart';
import 'features/calendar/domain/usecases/get_calendar_summary.dart'
    as calendar_summary;
import 'features/calendar/presentation/cubit/calendar_cubit.dart';
import 'core/network/network_info.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// Shared Feature
import 'features/cubit/language_cubit.dart';
import 'features/cubit/user_cubit.dart';

/// AUTH Feature
import 'features/auth/data/datasources/locals/auth_local_data_source.dart';
import 'features/auth/data/datasources/remotes/auth_remote_data_source.dart';
import 'features/auth/data/repositories/auth_repository_impl.dart';
import 'features/auth/domain/repositories/auth_repository.dart';
import 'features/auth/presentation/cubit/auth_cubit.dart';

import 'features/faceDetection/domain/usecase/check_in_usecase.dart';
import 'features/faceDetection/domain/usecase/check_out_usecase.dart';
import 'features/faceDetection/domain/usecase/get_today_attendance_usecase.dart';

/// PROFILE Feature
import 'features/profile/data/datasources/profile_remote_data_source.dart';
import 'features/profile/data/repositories/profile_repository_impl.dart';
import 'features/profile/domain/repositories/profile_repository.dart';
import 'features/profile/domain/usecases/get_user_profile.dart';
import 'features/profile/domain/usecases/update_user_profile.dart';
import 'features/profile/domain/usecases/update_avatar.dar.dart';
import 'features/profile/domain/usecases/change_password.dart';
import 'features/profile/presentation/cubit/profile_cubit.dart';

/// NOTIFICATION Feature
import 'features/notification/data/datasources/notification_local_data_source.dart';
import 'features/notification/data/datasources/notification_remote_data_source.dart';
import 'features/notification/data/repositories/notification_repository_impl.dart';
import 'features/notification/domain/repositories/notification_repository.dart';
import 'features/notification/domain/usecases/get_notifications.dart';
import 'features/notification/domain/usecases/mark_notification_as_read.dart';
import 'features/notification/domain/usecases/get_unread_count.dart';
import 'features/notification/domain/usecases/mark_all_as_read.dart';
import 'features/notification/presentation/cubit/notification_cubit.dart';

/// ATTENDANCE Feature (V1 & V2)
import 'features/attendance/data/datasources/attendance_remote_data_source.dart'
    as attendance_ds;
import 'features/attendance/data/repositories/attendance_repository_impl.dart';
import 'features/attendance/domain/repositories/attendance_repository.dart';

import 'features/attendance/presentation/cubit/attendance_page/attendance_cubit.dart';
import 'features/attendance/presentation/cubit/attendance_history_page/attendance_history_cubit.dart';

/// FACE DETECTION
import 'features/faceDetection/data/datasources/attendance_remote_data_source.dart'
    as face_detection_ds;
import 'features/faceDetection/data/repositories/attendance_repository_impl.dart';
import 'features/faceDetection/domain/repositories/attendance_repository.dart';

/// UPLOAD FACE Feature
import 'features/upload_employee_face/data/datasources/upload_face_remote_data_source.dart';
import 'features/upload_employee_face/data/reponsitories/upload_face_repository_impl.dart';
import 'features/upload_employee_face/domain/reponsitories/upload_face_repository.dart';
import 'features/upload_employee_face/presentation/cubit/upload_face_cubit.dart';

/// OVERTIME Feature
import 'features/overtime/data/datasources/overtime_remote_data_source.dart';
import 'features/overtime/data/datasources/overtime_admin_remote_data_source.dart';
import 'features/overtime/data/repositories/overtime_repository_impl.dart';
import 'features/overtime/data/repositories/overtime_admin_repository_impl.dart';
import 'features/overtime/domain/repositories/overtime_repository.dart';
import 'features/overtime/domain/repositories/overtime_admin_repository.dart';
import 'features/overtime/domain/usecases/get_approvers.dart';
import 'features/overtime/domain/usecases/get_overtime_history.dart';
import 'features/overtime/domain/usecases/get_overtime_summary.dart';
import 'features/overtime/domain/usecases/submit_overtime_request.dart';
import 'features/overtime/domain/usecases/get_all_overtime_requests.dart';
import 'features/overtime/domain/usecases/approve_overtime_request.dart';
import 'features/overtime/domain/usecases/reject_overtime_request.dart';
import 'features/overtime/presentation/cubit/overtime_cubit.dart';
import 'features/overtime/presentation/cubit/overtime_admin_cubit.dart';

/// LEAVE Feature
import 'features/leave/presentation/cubit/leave_cubit.dart';
import 'features/leave/presentation/cubit/leave_admin_cubit.dart';

/// ADMIN USER MANAGEMENT Feature
import 'features/admin/data/datasources/admin_user_remote_data_source.dart';
import 'features/admin/data/repositories/admin_user_repository_impl.dart';
import 'features/admin/domain/repositories/admin_user_repository.dart';
import 'features/admin/domain/usecases/get_all_users_usecase.dart';
import 'features/admin/domain/usecases/create_user_usecase.dart';
import 'features/admin/domain/usecases/admin_user_usecases.dart';
import 'features/admin/presentation/cubit/admin_user_cubit.dart';

/// ROLE MANAGEMENT Feature
import 'features/role/data/datasources/role_remote_datasource.dart';
import 'features/role/data/repositories/role_repository_impl.dart';
import 'features/role/domain/repositories/role_repository.dart';
import 'features/role/domain/usecases/role_usecases.dart';
import 'features/role/presentation/cubit/role_cubit.dart';

/// DEPARTMENT MANAGEMENT Feature
import 'features/department/data/datasources/department_remote_datasource.dart';
import 'features/department/data/repositories/department_repository_impl.dart';
import 'features/department/domain/repositories/department_repository.dart';
import 'features/department/domain/usecases/department_usecases.dart';
import 'features/department/presentation/cubit/department_cubit.dart';
import 'features/leave/data/datasources/leave_remote_data_source.dart';
import 'features/leave/data/datasources/leave_admin_remote_data_source.dart';
import 'features/leave/data/repositories/leave_repository_impl.dart';
import 'features/leave/data/repositories/leave_admin_repository_impl.dart';
import 'features/leave/domain/repositories/leave_repository.dart';
import 'features/leave/domain/repositories/leave_admin_repository.dart';
import 'features/leave/domain/usecases/get_leave_summary.dart';
import 'features/leave/domain/usecases/get_leave_history.dart';
import 'features/leave/domain/usecases/submit_leave_request.dart';
import 'features/leave/domain/usecases/get_approvers.dart' as leave_approvers;
import 'features/leave/domain/usecases/get_all_leave_requests.dart';
import 'features/leave/domain/usecases/approve_leave_request.dart';
import 'features/leave/domain/usecases/reject_leave_request.dart';

final sl = GetIt.instance;

Future<void> setupLocator() async {
  // ========================
  // 🧩 CORE / EXTERNAL SERVICES
  // ========================
  sl.registerSingletonAsync<SharedPreferences>(
    () async => SharedPreferences.getInstance(),
  );
  await sl.isReady<SharedPreferences>();

  sl.registerSingleton<DioClient>(DioClient());
  sl.registerSingleton<BiometricService>(BiometricService());
  sl.registerSingleton<SecureStorageService>(SecureStorageService());
  sl.registerSingleton<FirebaseService>(FirebaseService());

  // ========================
  // 🌐 SHARED CUBITS
  // ========================
  sl.registerSingletonAsync<LanguageCubit>(() async {
    final cubit = LanguageCubit(sharedPreferences: sl());
    await cubit.loadSavedLanguage();
    return cubit;
  });

  sl.registerLazySingleton(() => UserCubit(getCachedUserUseCase: sl()));

  // ========================
  // 🔐 AUTH FEATURE
  // ========================
  sl
    ..registerFactory(
      () => AuthCubit(
        loginUseCase: sl(),
        registerUseCase: sl(),
        logoutUseCase: sl(),
        getAuthStatusUseCase: sl(),
        forgotPasswordUseCase: sl(),
        verifyOtpUseCase: sl(),
        resendOtpUseCase: sl(),
        resetPasswordUseCase: sl(),
        userCubit: sl(),
        localDataSource: sl(),
        dioClient: sl(),
        biometricService: sl(),
        secureStorage: sl(),
      ),
    )
    ..registerLazySingleton(() => LoginUseCase(sl()))
    ..registerLazySingleton(() => RegisterUseCase(sl()))
    ..registerLazySingleton(() => LogoutUseCase(sl()))
    ..registerLazySingleton(() => GetAuthStatusUseCase(sl()))
    ..registerLazySingleton(() => ForgotPasswordUseCase(sl()))
    ..registerLazySingleton(() => VerifyOtpUseCase(sl()))
    ..registerLazySingleton(() => ResendOtpUseCase(sl()))
    ..registerLazySingleton(() => ResetPasswordUseCase(sl()))
    ..registerLazySingleton(() => GetCachedUserUseCase(sl()))
    ..registerLazySingleton<AuthRepository>(
      () => AuthRepositoryImpl(
        remoteDataSource: sl(),
        localDataSource: sl(),
        dioClient: sl(),
      ),
    )
    ..registerLazySingleton<AuthRemoteDataSource>(
      () => AuthRemoteDataSourceImpl(dioClient: sl()),
    )
    ..registerLazySingleton<AuthLocalDataSource>(
      () => AuthLocalDataSourceImpl(sharedPreferences: sl()),
    );

  // ========================
  // 👤 PROFILE FEATURE
  // ========================
  sl
    ..registerFactory(
      () => ProfileCubit(
        getUserProfile: sl(),
        updateUserProfile: sl(),
        uploadProfileImage: sl(),
        changePasswordUseCase: sl(),
      ),
    )
    ..registerLazySingleton(() => GetUserProfile(sl()))
    ..registerLazySingleton(() => UpdateUserProfile(sl()))
    ..registerLazySingleton(() => UploadProfileImage(sl()))
    ..registerLazySingleton(() => ChangePassword(sl()))
    ..registerLazySingleton<ProfileRepository>(
      () => ProfileRepositoryImpl(sl()),
    )
    ..registerLazySingleton<ProfileRemoteDataSource>(
      () => ProfileRemoteDataSourceImpl(dioClient: sl()),
    );

  // ========================
  // 🔔 NOTIFICATION FEATURE
  // ========================
  sl
    ..registerFactory(
      () => NotificationCubit(
        getNotificationsUseCase: sl(),
        markNotificationAsReadUseCase: sl(),
        getUnreadCountUseCase: sl(),
        markAllAsReadUseCase: sl(),
      ),
    )
    ..registerLazySingleton(() => GetNotificationsUseCase(sl()))
    ..registerLazySingleton(() => MarkNotificationAsReadUseCase(sl()))
    ..registerLazySingleton(() => GetUnreadCountUseCase(sl()))
    ..registerLazySingleton(() => MarkAllAsReadUseCase(sl()))
    ..registerLazySingleton<NotificationRepository>(
      () => NotificationRepositoryImpl(
        localDataSource: sl(),
        remoteDataSource: sl(),
      ),
    )
    ..registerLazySingleton<NotificationLocalDataSource>(
      () => NotificationLocalDataSourceImpl(),
    )
    ..registerLazySingleton<NotificationRemoteDataSource>(
      () => NotificationRemoteDataSourceImpl(dioClient: sl()),
    );

  // ========================
  // 🧑‍💼 ATTENDANCE FEATURE V1 (FaceDetection)
  // ========================
  sl
    ..registerLazySingleton(() => CheckInUseCase(sl()))
    ..registerLazySingleton(() => CheckOutUseCase(sl()))
    ..registerLazySingleton(() => GetTodayAttendanceUseCase(sl()))
    ..registerLazySingleton<AttendanceRepository>(
      () => AttendanceRepositoryImpl(remoteDataSource: sl()),
    )
    ..registerLazySingleton<face_detection_ds.AttendanceRemoteDataSource>(
      () => face_detection_ds.AttendanceRemoteDataSourceImpl(dioClient: sl()),
    );

  // ========================
  // 📊 ATTENDANCE FEATURE V2 (Page Cubits)
  // ========================
  sl
    ..registerFactory(
      () => AttendanceCubitV1(
        getTodaySummary: sl(),
        getWeeklySummary: sl(),
        getMonthlySummary: sl(),
        getAttendanceHistory: sl(),
      ),
    )
    ..registerFactory(() => AttendanceHistoryCubit(getMonthlyDetails: sl()))
    ..registerLazySingleton(() => GetTodaySummary(sl()))
    ..registerLazySingleton(() => GetWeeklySummary(sl()))
    ..registerLazySingleton(() => GetMonthlySummary(sl()))
    ..registerLazySingleton(() => GetAttendanceHistory(sl()))
    ..registerLazySingleton(() => GetMonthlyDetails(sl()))
    ..registerLazySingleton<AttendanceRepositoryV1>(
      () => AttendanceRepositoryImplV1(remoteDataSource: sl()),
    )
    ..registerLazySingleton<attendance_ds.AttendanceRemoteDataSourceV1>(
      () => attendance_ds.AttendanceRemoteDataSourceImpl(dioClient: sl()),
    );

  // ========================
  // 📷 UPLOAD EMPLOYEE FACE FEATURE
  // ========================
  sl
    ..registerLazySingleton<UploadFaceRemoteDataSource>(
      () => UploadFaceRemoteDataSourceImpl(dioClient: sl()),
    )
    ..registerLazySingleton<UploadFaceRepository>(
      () => UploadFaceRepositoryImpl(remoteDataSource: sl()),
    )
    ..registerFactory(() => UploadFaceCubit(repository: sl()));

  // ========================
  // ⏰ OVERTIME FEATURE
  // ========================
  sl
    ..registerFactory(
      () => OvertimeCubit(
        getOvertimeSummary: sl(),
        getOvertimeHistory: sl(),
        getApprovers: sl(),
        submitOvertimeRequest: sl(),
      ),
    )
    ..registerFactory(
      () => OvertimeAdminCubit(
        getAllOvertimeRequests: sl(),
        approveOvertimeRequest: sl(),
        rejectOvertimeRequest: sl(),
      ),
    )
    ..registerLazySingleton(() => GetOvertimeSummary(sl()))
    ..registerLazySingleton(() => GetOvertimeHistory(sl()))
    ..registerLazySingleton(() => GetApprovers(sl()))
    ..registerLazySingleton(() => SubmitOvertimeRequest(sl()))
    ..registerLazySingleton(() => GetAllOvertimeRequests(sl()))
    ..registerLazySingleton(() => ApproveOvertimeRequest(sl()))
    ..registerLazySingleton(() => RejectOvertimeRequest(sl()))
    ..registerLazySingleton<OvertimeRepository>(
      () => OvertimeRepositoryImpl(remoteDataSource: sl()),
    )
    ..registerLazySingleton<OvertimeAdminRepository>(
      () => OvertimeAdminRepositoryImpl(remoteDataSource: sl()),
    )
    ..registerLazySingleton<OvertimeRemoteDataSource>(
      () => OvertimeRemoteDataSourceImpl(dioClient: sl()),
    )
    ..registerLazySingleton<OvertimeAdminRemoteDataSource>(
      () => OvertimeAdminRemoteDataSourceImpl(dioClient: sl()),
    );

  // ========================
  // 📅 CALENDAR FEATURE
  // ========================
  sl
    ..registerFactory(
      () => CalendarCubit(
        getCalendarEvents: sl(),
        addCalendarEvent: sl(),
        updateCalendarEvent: sl(),
        deleteCalendarEvent: sl(),
        getCalendarSummary: sl(),
      ),
    )
    ..registerLazySingleton(() => GetCalendarEvents(sl()))
    ..registerLazySingleton(() => AddCalendarEvent(sl()))
    ..registerLazySingleton(() => UpdateCalendarEvent(sl()))
    ..registerLazySingleton(() => DeleteCalendarEvent(sl()))
    ..registerLazySingleton(() => calendar_summary.GetCalendarSummary(sl()))
    ..registerLazySingleton<CalendarRepository>(
      () => CalendarRepositoryImpl(
        remoteDataSource: sl(),
        localDataSource: sl(),
        networkInfo: sl(),
      ),
    )
    ..registerLazySingleton<CalendarRemoteDataSource>(
      () => CalendarRemoteDataSourceImpl(dioClient: sl()),
    )
    ..registerLazySingleton<CalendarLocalDataSource>(
      () => CalendarLocalDataSourceImpl(),
    );

  // ========================
  // 🌐 NETWORK INFO
  // ========================
  sl
    ..registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl(sl()))
    ..registerLazySingleton(() => Connectivity());

  // ========================
  // 🏖️ LEAVE FEATURE
  // ========================

  // Data sources
  sl.registerLazySingleton<LeaveRemoteDataSource>(
    () => LeaveRemoteDataSourceImpl(dioClient: sl()),
  );

  // Repositories
  sl.registerLazySingleton<LeaveRepository>(
    () => LeaveRepositoryImpl(remoteDataSource: sl()),
  );

  // Use cases
  sl
    ..registerLazySingleton(() => GetLeaveSummary(sl()))
    ..registerLazySingleton(() => GetLeaveHistory(sl()))
    ..registerLazySingleton(() => SubmitLeaveRequest(sl()))
    ..registerLazySingleton(() => leave_approvers.GetApprovers(sl()));

  // Cubit
  sl.registerFactory(
    () => LeaveCubit(
      getLeaveSummary: sl(),
      getLeaveHistory: sl(),
      submitLeaveRequestUseCase: sl(),
      getApprovers: sl(),
    ),
  );

  // ========================
  // 🏖️ LEAVE ADMIN FEATURE
  // ========================

  // Admin Data sources
  sl.registerLazySingleton<LeaveAdminRemoteDataSource>(
    () => LeaveAdminRemoteDataSourceImpl(dioClient: sl()),
  );

  // Admin Repositories
  sl.registerLazySingleton<LeaveAdminRepository>(
    () => LeaveAdminRepositoryImpl(remoteDataSource: sl()),
  );

  // Admin Use cases
  sl
    ..registerLazySingleton(() => GetAllLeaveRequests(sl()))
    ..registerLazySingleton(() => ApproveLeaveRequest(sl()))
    ..registerLazySingleton(() => RejectLeaveRequest(sl()));

  // Admin Cubit
  sl.registerFactory(
    () => LeaveAdminCubit(
      getAllLeaveRequests: sl(),
      approveLeaveRequest: sl(),
      rejectLeaveRequest: sl(),
    ),
  );

  // ========================
  // 👥 ADMIN USER MANAGEMENT FEATURE
  // ========================

  // Data sources
  sl.registerLazySingleton<AdminUserRemoteDataSource>(
    () => AdminUserRemoteDataSourceImpl(dioClient: sl()),
  );

  // Repositories
  sl.registerLazySingleton<AdminUserRepository>(
    () => AdminUserRepositoryImpl(remoteDataSource: sl()),
  );

  // Use cases
  sl
    ..registerLazySingleton(() => GetAllUsersUseCase(sl()))
    ..registerLazySingleton(() => CreateUserUseCase(sl()))
    ..registerLazySingleton(() => GetUserByIdUseCase(sl()))
    ..registerLazySingleton(() => UpdateUserUseCase(sl()))
    ..registerLazySingleton(() => SoftDeleteUserUseCase(sl()))
    ..registerLazySingleton(() => RestoreUserUseCase(sl()))
    ..registerLazySingleton(() => ToggleUserStatusUseCase(sl()))
    ..registerLazySingleton(() => ResetUserPasswordUseCase(sl()))
    ..registerLazySingleton(() => GetUserStatisticsUseCase(sl()))
    ..registerLazySingleton(() => BulkDeleteUsersUseCase(sl()))
    ..registerLazySingleton(() => BulkRestoreUsersUseCase(sl()));

  // Cubit
  sl.registerFactory(
    () => AdminUserCubit(
      getAllUsersUseCase: sl(),
      createUserUseCase: sl(),
      getUserByIdUseCase: sl(),
      updateUserUseCase: sl(),
      softDeleteUserUseCase: sl(),
      restoreUserUseCase: sl(),
      toggleUserStatusUseCase: sl(),
      resetUserPasswordUseCase: sl(),
      getUserStatisticsUseCase: sl(),
      bulkDeleteUsersUseCase: sl(),
      bulkRestoreUsersUseCase: sl(),
    ),
  );

  // ========================
  // 🔐 ROLE MANAGEMENT FEATURE
  // ========================

  // Data sources
  sl.registerLazySingleton<RoleRemoteDataSource>(
    () => RoleRemoteDataSourceImpl(dioClient: sl()),
  );

  // Repositories
  sl.registerLazySingleton<RoleRepository>(
    () => RoleRepositoryImpl(remoteDataSource: sl()),
  );

  // Use cases
  sl
    ..registerLazySingleton(() => GetAllRoles(sl()))
    ..registerLazySingleton(() => GetRoleById(sl()))
    ..registerLazySingleton(() => CreateRole(sl()))
    ..registerLazySingleton(() => UpdateRole(sl()))
    ..registerLazySingleton(() => DeleteRole(sl()))
    ..registerLazySingleton(() => GetRolesForDropdown(sl()));

  // Cubit
  sl.registerFactory(
    () => RoleCubit(
      getAllRoles: sl(),
      getRoleById: sl(),
      createRole: sl(),
      updateRole: sl(),
      deleteRole: sl(),
      getRolesForDropdown: sl(),
    ),
  );

  // ========================
  // 🏢 DEPARTMENT MANAGEMENT FEATURE
  // ========================

  // Data sources
  sl.registerLazySingleton<DepartmentRemoteDataSource>(
    () => DepartmentRemoteDataSourceImpl(dioClient: sl()),
  );

  // Repositories
  sl.registerLazySingleton<DepartmentRepository>(
    () => DepartmentRepositoryImpl(remoteDataSource: sl()),
  );

  // Use cases
  sl
    ..registerLazySingleton(() => GetAllDepartments(sl()))
    ..registerLazySingleton(() => GetDepartmentById(sl()))
    ..registerLazySingleton(() => CreateDepartment(sl()))
    ..registerLazySingleton(() => UpdateDepartment(sl()))
    ..registerLazySingleton(() => DeleteDepartment(sl()))
    ..registerLazySingleton(() => RestoreDepartment(sl()))
    ..registerLazySingleton(() => ToggleDepartmentStatus(sl()))
    ..registerLazySingleton(() => GetDepartmentsForDropdown(sl()))
    ..registerLazySingleton(() => GetDepartmentHierarchy(sl()));

  // Cubit
  sl.registerFactory(
    () => DepartmentCubit(
      getAllDepartments: sl(),
      getDepartmentById: sl(),
      createDepartment: sl(),
      updateDepartment: sl(),
      deleteDepartment: sl(),
      restoreDepartment: sl(),
      toggleDepartmentStatus: sl(),
      getDepartmentsForDropdown: sl(),
      getDepartmentHierarchy: sl(),
    ),
  );

  // ========================
  // ✅ DONE
  // ========================
  await sl.allReady();
}
