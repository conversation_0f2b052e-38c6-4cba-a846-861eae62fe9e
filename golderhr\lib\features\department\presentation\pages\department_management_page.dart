import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/theme/app_text_styles.dart';
import 'package:golderhr/shared/widgets/show_custom_snackBar.dart';
import 'package:iconsax/iconsax.dart';

import '../cubit/department_cubit.dart';
import '../widgets/create_department_dialog.dart';
import '../widgets/edit_department_dialog.dart';
import '../../domain/entities/department_entity.dart';

class DepartmentManagementPage extends StatefulWidget {
  const DepartmentManagementPage({super.key});

  @override
  State<DepartmentManagementPage> createState() =>
      _DepartmentManagementPageState();
}

class _DepartmentManagementPageState extends State<DepartmentManagementPage> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _setupScrollListener();
  }

  void _loadInitialData() {
    context.read<DepartmentCubit>().loadDepartments();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent * 0.8) {
        context.read<DepartmentCubit>().loadMoreDepartments(
          search: _searchController.text.trim().isEmpty
              ? null
              : _searchController.text.trim(),
        );
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.lightTheme;
    final responsive = context.responsive;
    final l10n = context.l10n;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Department Management',
          style: theme.textTheme.displaySmall!.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () => _showCreateDepartmentDialog(context),
            icon: const Icon(Iconsax.add),
            tooltip: 'Add Department',
          ),
        ],
      ),
      body: BlocConsumer<DepartmentCubit, DepartmentState>(
        listener: (context, state) {
          if (state.hasError) {
            showTopSnackBar(
              context,
              title: l10n.error,
              message: state.error!,
              isError: true,
            );
            context.read<DepartmentCubit>().clearMessages();
          } else if (state.hasSuccess) {
            showTopSnackBar(
              context,
              title: l10n.success,
              message: state.successMessage!,
              isError: false,
            );
            context.read<DepartmentCubit>().clearMessages();
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              // Search and Filter Section
              Container(
                padding: responsive.padding(all: 16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _searchController,
                            decoration: InputDecoration(
                              hintText: 'Search departments...',
                              prefixIcon: const Icon(Iconsax.search_normal),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(
                                  responsive.defaultRadius,
                                ),
                              ),
                              contentPadding: responsive.padding(
                                horizontal: 16,
                                vertical: 12,
                              ),
                            ),
                            onChanged: (value) {
                              // Debounce search
                              Future.delayed(
                                const Duration(milliseconds: 500),
                                () {
                                  if (_searchController.text == value) {
                                    context
                                        .read<DepartmentCubit>()
                                        .refreshDepartments(
                                          search: value.trim().isEmpty
                                              ? null
                                              : value.trim(),
                                        );
                                  }
                                },
                              );
                            },
                          ),
                        ),
                        SizedBox(width: responsive.scaleWidth(12)),
                        IconButton(
                          onPressed: () => context
                              .read<DepartmentCubit>()
                              .refreshDepartments(
                                search: _searchController.text.trim().isEmpty
                                    ? null
                                    : _searchController.text.trim(),
                              ),
                          icon: const Icon(Iconsax.refresh),
                          tooltip: 'Refresh',
                        ),
                      ],
                    ),
                    SizedBox(height: responsive.scaleHeight(12)),
                    Row(
                      children: [
                        FilterChip(
                          label: Text('Include Deleted'),
                          selected: state.includeDeleted,
                          onSelected: (_) => context
                              .read<DepartmentCubit>()
                              .toggleIncludeDeleted(),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Departments List
              Expanded(
                child: state.isLoading && state.departments.isEmpty
                    ? const Center(child: CircularProgressIndicator())
                    : state.departments.isEmpty
                    ? _buildEmptyState(context)
                    : RefreshIndicator(
                        onRefresh: () =>
                            context.read<DepartmentCubit>().refreshDepartments(
                              search: _searchController.text.trim().isEmpty
                                  ? null
                                  : _searchController.text.trim(),
                            ),
                        child: ListView.builder(
                          controller: _scrollController,
                          padding: responsive.padding(all: 16),
                          itemCount:
                              state.departments.length +
                              (state.isLoading ? 1 : 0),
                          itemBuilder: (context, index) {
                            if (index >= state.departments.length) {
                              return const Center(
                                child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator(),
                                ),
                              );
                            }

                            final department = state.departments[index];
                            return _buildDepartmentCard(context, department);
                          },
                        ),
                      ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final responsive = context.responsive;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Iconsax.building,
            size: responsive.scaleRadius(64),
            color: Colors.grey[400],
          ),
          SizedBox(height: responsive.scaleHeight(16)),
          Text(
            'No departments found',
            style: AppTextStyle.bold(
              context,
              size: 18,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: responsive.scaleHeight(8)),
          Text(
            'Create your first department to get started',
            style: AppTextStyle.regular(
              context,
              size: 14,
              color: Colors.grey[500],
            ),
          ),
          SizedBox(height: responsive.scaleHeight(24)),
          ElevatedButton.icon(
            onPressed: () => _showCreateDepartmentDialog(context),
            icon: const Icon(Iconsax.add),
            label: const Text('Add Department'),
          ),
        ],
      ),
    );
  }

  Widget _buildDepartmentCard(
    BuildContext context,
    DepartmentEntity department,
  ) {
    final responsive = context.responsive;
    final theme = context.lightTheme;

    Color statusColor = Colors.green;
    String statusText = 'Active';
    IconData statusIcon = Iconsax.tick_circle;

    if (department.isDeleted) {
      statusColor = Colors.red;
      statusText = 'Deleted';
      statusIcon = Iconsax.close_circle;
    } else if (department.isDisabled) {
      statusColor = Colors.orange;
      statusText = 'Disabled';
      statusIcon = Iconsax.pause_circle;
    } else if (!department.isActive) {
      statusColor = Colors.grey;
      statusText = 'Inactive';
      statusIcon = Iconsax.minus_cirlce;
    }

    return Card(
      margin: responsive.padding(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(responsive.defaultRadius),
      ),
      child: ListTile(
        contentPadding: responsive.padding(horizontal: 16, vertical: 8),
        leading: CircleAvatar(
          backgroundColor: theme.primaryColor.withValues(alpha: 0.1),
          child: Icon(Iconsax.building, color: theme.primaryColor),
        ),
        title: Text(
          department.name,
          style: AppTextStyle.bold(context, size: 16),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (department.description != null)
              Text(
                department.description!,
                style: AppTextStyle.regular(
                  context,
                  size: 12,
                  color: Colors.grey[600],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            if (department.code != null)
              Text(
                'Code: ${department.code}',
                style: AppTextStyle.regular(
                  context,
                  size: 11,
                  color: Colors.grey[500],
                ),
              ),
            Row(
              children: [
                Icon(statusIcon, size: 12, color: statusColor),
                SizedBox(width: responsive.scaleWidth(4)),
                Text(
                  statusText,
                  style: AppTextStyle.regular(
                    context,
                    size: 11,
                    color: statusColor,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) =>
              _handleDepartmentAction(context, department, value),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Iconsax.edit, size: 16),
                  SizedBox(width: 8),
                  Text('Edit'),
                ],
              ),
            ),
            if (!department.isDeleted) ...[
              PopupMenuItem(
                value: 'toggle_status',
                child: Row(
                  children: [
                    Icon(
                      department.isDisabled ? Iconsax.play : Iconsax.pause,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(department.isDisabled ? 'Enable' : 'Disable'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Iconsax.trash, size: 16, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Delete', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ] else ...[
              const PopupMenuItem(
                value: 'restore',
                child: Row(
                  children: [
                    Icon(Iconsax.refresh, size: 16, color: Colors.green),
                    SizedBox(width: 8),
                    Text('Restore', style: TextStyle(color: Colors.green)),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _handleDepartmentAction(
    BuildContext context,
    DepartmentEntity department,
    String action,
  ) {
    switch (action) {
      case 'edit':
        _showEditDepartmentDialog(context, department);
        break;
      case 'toggle_status':
        context.read<DepartmentCubit>().toggleExistingDepartmentStatus(
          department.id,
        );
        break;
      case 'delete':
        _showDeleteConfirmation(context, department);
        break;
      case 'restore':
        context.read<DepartmentCubit>().restoreExistingDepartment(
          department.id,
        );
        break;
    }
  }

  void _showCreateDepartmentDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const CreateDepartmentDialog(),
    );
  }

  void _showEditDepartmentDialog(
    BuildContext context,
    DepartmentEntity department,
  ) {
    showDialog(
      context: context,
      builder: (context) => EditDepartmentDialog(department: department),
    );
  }

  void _showDeleteConfirmation(
    BuildContext context,
    DepartmentEntity department,
  ) {
    final l10n = context.l10n;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Department'),
        content: Text(
          'Are you sure you want to delete the department "${department.name}"?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<DepartmentCubit>().deleteExistingDepartment(
                department.id,
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
