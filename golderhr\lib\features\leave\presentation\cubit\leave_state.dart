import 'package:equatable/equatable.dart';
import '../../domain/entities/leave_balance.dart';
import '../../domain/entities/leave_request.dart';
import '../../domain/entities/leave_policy.dart';

abstract class LeaveState extends Equatable {
  const LeaveState();

  @override
  List<Object?> get props => [];
}

class LeaveInitial extends LeaveState {}

class LeaveLoading extends LeaveState {}

class LeaveLoaded extends LeaveState {
  final LeaveBalance balance;
  final List<LeaveRequest> recentRequests;
  final List<LeavePolicy> policies;

  const LeaveLoaded({
    required this.balance,
    required this.recentRequests,
    required this.policies,
  });

  @override
  List<Object?> get props => [balance, recentRequests, policies];
}

class LeaveError extends LeaveState {
  final String message;

  const LeaveError(this.message);

  @override
  List<Object?> get props => [message];
}

class LeaveRequestSubmitting extends LeaveState {}

class LeaveRequestSubmitted extends LeaveState {
  final String message;

  const LeaveRequestSubmitted(this.message);

  @override
  List<Object?> get props => [message];
}

class LeaveRequestError extends LeaveState {
  final String message;

  const LeaveRequestError(this.message);

  @override
  List<Object?> get props => [message];
}
