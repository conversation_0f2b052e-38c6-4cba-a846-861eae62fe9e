import 'package:flutter/material.dart';
import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/responsive/responsive.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/leave_request.dart';

class LeaveTypeSelector extends StatelessWidget {
  final LeaveType selectedType;
  final Function(LeaveType) onTypeChanged;

  const LeaveTypeSelector({
    super.key,
    required this.selectedType,
    required this.onTypeChanged,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: responsive.padding(all: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.textSecondary.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.category_outlined,
                  color: AppColors.primaryBlue,
                  size: 20,
                ),
              ),
              SizedBox(width: responsive.widthPercentage(3)),
              Text(
                context.l10n.leaveType,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontSize: responsive.fontSize(16),
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: responsive.heightPercentage(2)),

          // Leave Type Options
          ...LeaveType.values.map((type) {
            final isSelected = selectedType == type;
            return Padding(
              padding: EdgeInsets.only(bottom: responsive.heightPercentage(1)),
              child: InkWell(
                onTap: () => onTypeChanged(type),
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: responsive.padding(all: 16),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? AppColors.primaryBlue.withValues(alpha: 0.1)
                        : AppColors.background,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected
                          ? AppColors.primaryBlue
                          : AppColors.textSecondary.withValues(alpha: 0.2),
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: _getTypeColor(type).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          _getTypeIcon(type),
                          color: _getTypeColor(type),
                          size: 20,
                        ),
                      ),
                      SizedBox(width: responsive.widthPercentage(3)),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _getTypeDisplayName(context, type),
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontSize: responsive.fontSize(14),
                                color: AppColors.textPrimary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            SizedBox(height: responsive.heightPercentage(0.5)),
                            Text(
                              _getTypeDescription(context, type),
                              style: theme.textTheme.bodySmall?.copyWith(
                                fontSize: responsive.fontSize(12),
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (isSelected)
                        Icon(
                          Icons.check_circle,
                          color: AppColors.primaryBlue,
                          size: 24,
                        ),
                    ],
                  ),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Color _getTypeColor(LeaveType type) {
    switch (type) {
      case LeaveType.annual:
        return AppColors.primaryBlue;
      case LeaveType.sick:
        return AppColors.error;
      case LeaveType.personal:
        return AppColors.warning;
      case LeaveType.maternity:
        return AppColors.primaryGreen;
      case LeaveType.unpaid:
        return AppColors.textSecondary;
    }
  }

  IconData _getTypeIcon(LeaveType type) {
    switch (type) {
      case LeaveType.annual:
        return Icons.beach_access_outlined;
      case LeaveType.sick:
        return Icons.local_hospital_outlined;
      case LeaveType.personal:
        return Icons.person_outline;
      case LeaveType.maternity:
        return Icons.child_care_outlined;
      case LeaveType.unpaid:
        return Icons.money_off_outlined;
    }
  }

  String _getTypeDisplayName(BuildContext context, LeaveType type) {
    switch (type) {
      case LeaveType.annual:
        return context.l10n.annualLeave;
      case LeaveType.sick:
        return context.l10n.sickLeave;
      case LeaveType.personal:
        return context.l10n.personalLeave;
      case LeaveType.maternity:
        return context.l10n.maternityLeave;
      case LeaveType.unpaid:
        return context.l10n.unpaidLeave;
    }
  }

  String _getTypeDescription(BuildContext context, LeaveType type) {
    switch (type) {
      case LeaveType.annual:
        return context.l10n.annualLeaveDescription;
      case LeaveType.sick:
        return context.l10n.sickLeaveDescription;
      case LeaveType.personal:
        return context.l10n.personalLeaveDescription;
      case LeaveType.maternity:
        return context.l10n.maternityLeaveDescription;
      case LeaveType.unpaid:
        return context.l10n.unpaidLeaveDescription;
    }
  }
}
