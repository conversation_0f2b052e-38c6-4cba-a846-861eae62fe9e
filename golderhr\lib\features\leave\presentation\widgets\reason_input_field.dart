import 'package:flutter/material.dart';
import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/responsive/responsive.dart';
import '../../../../shared/theme/app_colors.dart';

class ReasonInputField extends StatelessWidget {
  final TextEditingController controller;
  final String? Function(String?)? validator;

  const ReasonInputField({super.key, required this.controller, this.validator});

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: responsive.padding(all: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.textSecondary.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.warning.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.edit_note_outlined,
                  color: AppColors.warning,
                  size: 20,
                ),
              ),
              SizedBox(width: responsive.widthPercentage(3)),
              Text(
                context.l10n.reasonForLeave,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontSize: responsive.fontSize(16),
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: responsive.heightPercentage(2)),

          TextFormField(
            controller: controller,
            maxLines: 4,
            minLines: 3,
            validator: validator,
            decoration: InputDecoration(
              hintText: context.l10n.leaveReasonHint,
              hintStyle: theme.textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary.withValues(alpha: 0.7),
                fontSize: responsive.fontSize(14),
                height: 1.4,
              ),
              filled: true,
              fillColor: AppColors.background,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: AppColors.textSecondary.withValues(alpha: 0.3),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: AppColors.textSecondary.withValues(alpha: 0.3),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.primaryBlue, width: 2),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.error, width: 2),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.error, width: 2),
              ),
              contentPadding: responsive.padding(all: 16),
            ),
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppColors.textPrimary,
              fontSize: responsive.fontSize(14),
              height: 1.4,
            ),
          ),

          SizedBox(height: responsive.heightPercentage(1.5)),

          // Character counter and tips
          Row(
            children: [
              Icon(Icons.lightbulb_outline, color: AppColors.warning, size: 16),
              SizedBox(width: responsive.widthPercentage(1)),
              Expanded(
                child: Text(
                  'Tip: Be specific about your reason to help with approval',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontSize: responsive.fontSize(12),
                    color: AppColors.textSecondary,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
