{".env": [".env"], "assets/fonts/Roboto-Black.ttf": ["assets/fonts/Roboto-Black.ttf"], "assets/fonts/Roboto-Bold.ttf": ["assets/fonts/Roboto-Bold.ttf"], "assets/fonts/Roboto-Italic.ttf": ["assets/fonts/Roboto-Italic.ttf"], "assets/fonts/Roboto-Light.ttf": ["assets/fonts/Roboto-Light.ttf"], "assets/fonts/Roboto-Medium.ttf": ["assets/fonts/Roboto-Medium.ttf"], "assets/fonts/Roboto-Regular.ttf": ["assets/fonts/Roboto-Regular.ttf"], "assets/images/default_avatar.png": ["assets/images/default_avatar.png"], "assets/images/logo.jpg": ["assets/images/logo.jpg"], "assets/images/no_notification.svg": ["assets/images/no_notification.svg"], "assets/images/splash.jpg": ["assets/images/splash.jpg"], "assets/images/splash2.png": ["assets/images/splash2.png"], "packages/cupertino_icons/assets/CupertinoIcons.ttf": ["packages/cupertino_icons/assets/CupertinoIcons.ttf"], "packages/iconsax/lib/assets/fonts/iconsax.ttf": ["packages/iconsax/lib/assets/fonts/iconsax.ttf"], "packages/syncfusion_flutter_datagrid/assets/font/FilterIcon.ttf": ["packages/syncfusion_flutter_datagrid/assets/font/FilterIcon.ttf"], "packages/syncfusion_flutter_datagrid/assets/font/UnsortIcon.ttf": ["packages/syncfusion_flutter_datagrid/assets/font/UnsortIcon.ttf"]}