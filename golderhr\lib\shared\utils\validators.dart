import 'package:flutter/cupertino.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';

class Validators {
  static String? validateFullName(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return context.l10n.loginPleaseEnterFullName;
    }
    return null;
  }

  static String? validateEmail(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return context.l10n.loginPleaseEnterEmail;
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return context.l10n.loginPleaseEnterEmail;
    }
    return null;
  }

  static String? validatePassword(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return context.l10n.loginPleaseEnterPassword;
    }
    if (value.length < 6) {
      return context.l10n.loginPasswordMinLength;
    }
    return null;
  }

  static String? validateConfirmPassword(
    String? value,
    String password,
    BuildContext context,
  ) {
    if (value == null || value.isEmpty) {
      return context.l10n.registerPleaseConfirmPassword;
    }
    if (value != password) {
      return context.l10n.registerPasswordsDoNotMatch;
    }
    return null;
  }

  static String? validateOvertimeReason(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.overtimeReasonRequired;
    }
    if (value.trim().length < 10) {
      return context.l10n.overtimeReasonTooShort;
    }
    if (value.trim().length > 500) {
      return context.l10n.overtimeReasonTooLong;
    }
    return null;
  }

  static String? validateRejectionReason(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.rejectionReasonRequired;
    }
    if (value.trim().length < 10) {
      return context.l10n.rejectionReasonTooShort;
    }
    if (value.trim().length > 500) {
      return context.l10n.rejectionReasonTooLong;
    }
    return null;
  }

  // Admin User Management Validators
  static String? validateFirstName(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.firstNameRequired;
    }
    if (value.trim().length < 2) {
      return 'First name must be at least 2 characters';
    }
    return null;
  }

  static String? validateLastName(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.lastNameRequired;
    }
    if (value.trim().length < 2) {
      return 'Last name must be at least 2 characters';
    }
    return null;
  }

  static String? validatePhoneNumber(String? value, BuildContext context) {
    if (value != null && value.isNotEmpty) {
      if (!RegExp(r'^\+?[0-9]{10,15}$').hasMatch(value)) {
        return context.l10n.phoneInvalid;
      }
    }
    return null;
  }

  static String? validateRole(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return context.l10n.roleRequired;
    }
    return null;
  }

  static String? validateDepartment(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return context.l10n.departmentRequired;
    }
    return null;
  }

  static String? validateStrongPassword(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return context.l10n.passwordRequired;
    }
    if (value.length < 8) {
      return context.l10n.passwordTooShort;
    }
    // Check for uppercase, lowercase, number, and special character
    if (!RegExp(
      r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]',
    ).hasMatch(value)) {
      return context.l10n.passwordTooWeak;
    }
    return null;
  }
}
