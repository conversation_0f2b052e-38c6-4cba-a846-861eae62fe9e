import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/overtime_request_entity.dart';
import '../repositories/overtime_repository.dart';

class GetApprovers implements UseCase<List<ApproverEntity>, NoParams> {
  final OvertimeRepository repository;

  GetApprovers(this.repository);

  @override
  Future<Either<Failure, List<ApproverEntity>>> call(NoParams params) async {
    return await repository.getApprovers();
  }
}
