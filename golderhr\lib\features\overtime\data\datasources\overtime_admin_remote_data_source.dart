import '../../../../core/network/dio_client.dart';
import '../../../../core/error/exceptions.dart';
import '../models/overtime_request_model.dart';

abstract class OvertimeAdminRemoteDataSource {
  Future<List<OvertimeRequestModel>> getAllOvertimeRequests({
    int page = 1,
    int limit = 10,
    String? status,
  });
  Future<OvertimeRequestModel> approveOvertimeRequest(String requestId);
  Future<OvertimeRequestModel> rejectOvertimeRequest(
    String requestId,
    String rejectionReason,
  );
}

class OvertimeAdminRemoteDataSourceImpl
    implements OvertimeAdminRemoteDataSource {
  final DioClient dioClient;

  OvertimeAdminRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<List<OvertimeRequestModel>> getAllOvertimeRequests({
    int page = 1,
    int limit = 10,
    String? status,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {'page': page, 'limit': limit};

      if (status != null && status.isNotEmpty) {
        queryParams['status'] = status;
      }

      final response = await dioClient.get(
        '/api/overtime/admin/all',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        final data = response.data['data'];

        // Handle both array and object with requests array
        List<dynamic> requestsData;
        if (data is List) {
          requestsData = data;
        } else if (data is Map && data.containsKey('requests')) {
          requestsData = data['requests'];
        } else {
          throw ServerException('Invalid response format');
        }

        final List<OvertimeRequestModel> requests = [];
        for (int i = 0; i < requestsData.length; i++) {
          try {
            final json = requestsData[i];
            final model = OvertimeRequestModel.fromJson(json);
            requests.add(model);
          } catch (e) {
            // Skip invalid requests
          }
        }

        return requests;
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to get overtime requests',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error occurred while fetching requests');
    }
  }

  @override
  Future<OvertimeRequestModel> approveOvertimeRequest(String requestId) async {
    try {
      final response = await dioClient.put(
        '/api/overtime/admin/$requestId/approve',
      );

      if (response.statusCode == 200) {
        final data = response.data['data'];
        return OvertimeRequestModel.fromJson(data);
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to approve overtime request',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error occurred while approving request');
    }
  }

  @override
  Future<OvertimeRequestModel> rejectOvertimeRequest(
    String requestId,
    String rejectionReason,
  ) async {
    try {
      final response = await dioClient.put(
        '/api/overtime/admin/$requestId/reject',
        data: {'rejectionReason': rejectionReason},
      );

      if (response.statusCode == 200) {
        final data = response.data['data'];
        return OvertimeRequestModel.fromJson(data);
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to reject overtime request',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error occurred while rejecting request');
    }
  }
}
