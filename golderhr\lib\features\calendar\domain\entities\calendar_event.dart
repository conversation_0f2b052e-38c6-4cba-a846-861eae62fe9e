import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// Enum định nghĩa các lo<PERSON> sự kiện trong calendar
enum EventType { meeting, leave, holiday, training, event }

/// Extension để lấy tên hiển thị của EventType
extension EventTypeExtension on EventType {
  String get displayName {
    switch (this) {
      case EventType.meeting:
        return 'Meeting';
      case EventType.leave:
        return 'Leave';
      case EventType.holiday:
        return 'Holiday';
      case EventType.training:
        return 'Training';
      case EventType.event:
        return 'Event';
    }
  }

  IconData get icon {
    switch (this) {
      case EventType.meeting:
        return Icons.people_outline;
      case EventType.leave:
        return Icons.flight_takeoff_outlined;
      case EventType.holiday:
        return Icons.celebration_outlined;
      case EventType.training:
        return Icons.school_outlined;
      case EventType.event:
        return Icons.event_note_outlined;
    }
  }
}

/// Entity đại diện cho một sự kiện trong calendar
class CalendarEvent extends Equatable {
  final String id;
  final String title;
  final String description;
  final DateTime date;
  final String time;
  final EventType type;
  final Color color;
  final bool isAllDay;
  final DateTime? startTime;
  final DateTime? endTime;
  final String? location;
  final List<String> attendees;
  final bool isRecurring;
  final String? recurrenceRule;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CalendarEvent({
    required this.id,
    required this.title,
    required this.description,
    required this.date,
    required this.time,
    required this.type,
    required this.color,
    this.isAllDay = false,
    this.startTime,
    this.endTime,
    this.location,
    this.attendees = const [],
    this.isRecurring = false,
    this.recurrenceRule,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Tạo CalendarEvent với các giá trị mặc định
  factory CalendarEvent.create({
    required String title,
    required String description,
    required DateTime date,
    required String time,
    required EventType type,
    required Color color,
    bool isAllDay = false,
    DateTime? startTime,
    DateTime? endTime,
    String? location,
    List<String> attendees = const [],
    bool isRecurring = false,
    String? recurrenceRule,
  }) {
    final now = DateTime.now();
    return CalendarEvent(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      description: description,
      date: date,
      time: time,
      type: type,
      color: color,
      isAllDay: isAllDay,
      startTime: startTime,
      endTime: endTime,
      location: location,
      attendees: attendees,
      isRecurring: isRecurring,
      recurrenceRule: recurrenceRule,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Copy với các thay đổi
  CalendarEvent copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? date,
    String? time,
    EventType? type,
    Color? color,
    bool? isAllDay,
    DateTime? startTime,
    DateTime? endTime,
    String? location,
    List<String>? attendees,
    bool? isRecurring,
    String? recurrenceRule,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CalendarEvent(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      date: date ?? this.date,
      time: time ?? this.time,
      type: type ?? this.type,
      color: color ?? this.color,
      isAllDay: isAllDay ?? this.isAllDay,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      location: location ?? this.location,
      attendees: attendees ?? this.attendees,
      isRecurring: isRecurring ?? this.isRecurring,
      recurrenceRule: recurrenceRule ?? this.recurrenceRule,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Kiểm tra xem sự kiện có trong ngày được chỉ định không
  bool isOnDate(DateTime targetDate) {
    return date.year == targetDate.year &&
        date.month == targetDate.month &&
        date.day == targetDate.day;
  }

  /// Kiểm tra xem sự kiện có trong khoảng thời gian không
  bool isInDateRange(DateTime startDate, DateTime endDate) {
    final eventDate = DateTime(date.year, date.month, date.day);
    final start = DateTime(startDate.year, startDate.month, startDate.day);
    final end = DateTime(endDate.year, endDate.month, endDate.day);
    return !eventDate.isBefore(start) && !eventDate.isAfter(end);
  }

  @override
  List<Object?> get props => [
    id,
    title,
    description,
    date,
    time,
    type,
    color,
    isAllDay,
    startTime,
    endTime,
    location,
    attendees,
    isRecurring,
    recurrenceRule,
    createdAt,
    updatedAt,
  ];
}
