// quick_stats_section.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:iconsax/iconsax.dart';

import '../../../attendance/presentation/cubit/attendance_page/attendance_cubit.dart';
import '../../../attendance/presentation/cubit/attendance_page/attendance_state.dart';
import 'stat_card.dart';

class QuickStatsSection extends StatelessWidget {
  const QuickStatsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final responsive = context.responsive;

    return BlocBuilder<AttendanceCubitV1, AttendanceState>(
      builder: (context, state) {
        if (state is AttendanceLoading || state is AttendanceInitial) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is AttendanceError) {
          return Center(
            child: Text(
              'Error: ${state.message}',
              style: const TextStyle(color: Colors.red),
            ),
          );
        } else if (state is AttendanceLoaded) {
          String workHoursDisplay = "N/A";
          String leaveDaysDisplay = "N/A";
          String overtimeDisplay = "N/A";
          workHoursDisplay = state.monthlySummary.totalHours; // ví dụ
          leaveDaysDisplay = state.monthlySummary.overtime; // ví dụ
          overtimeDisplay = "${state.monthlySummary.daysOff}"; // ví dụ
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: StatCard(
                  title: l10n.homeWorkHours,
                  value: workHoursDisplay,
                  icon: Icons.schedule,
                  color: Colors.blue,
                ),
              ),
              SizedBox(width: responsive.widthPercentage(3.0)),
              Expanded(
                child: StatCard(
                  title: l10n.homeOvertime,
                  value: leaveDaysDisplay,
                  icon: Iconsax.timer,
                  color: Colors.orange,
                ),
              ),
              SizedBox(width: responsive.widthPercentage(3.0)),
              Expanded(
                child: StatCard(
                  title: l10n.homeLeave,
                  value: overtimeDisplay,
                  icon: Icons.calendar_today,
                  color: Colors.redAccent,
                ),
              ),
            ],
          );
        }

        return const Center(child: Text('Unknown state'));
      },
    );
  }
}
