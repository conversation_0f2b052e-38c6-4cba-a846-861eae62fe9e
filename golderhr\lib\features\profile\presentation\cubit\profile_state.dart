import 'package:equatable/equatable.dart';
import '../../domain/entities/user_profile.dart';

enum ProfileStatus { initial, loading, loaded, error }

class ProfileState extends Equatable {
  final ProfileStatus status;
  final UserProfile userProfile;
  final String? newImageFile;
  final String? errorMessage;

  const ProfileState({
    required this.status,
    required this.userProfile,
    this.newImageFile,
    this.errorMessage,
  });

  factory ProfileState.initial() => ProfileState(
    status: ProfileStatus.initial,
    userProfile: const UserProfile(
      id: '',
      name: '',
      email: '',
      phone: '',
      avatarUrl: '',
      department: '',
      position: '',
    ),
  );

  ProfileState copyWith({
    ProfileStatus? status,
    UserProfile? userProfile,
    String? newImageFile,
    String? errorMessage,
  }) {
    return ProfileState(
      status: status ?? this.status,
      userProfile: userProfile ?? this.userProfile,
      newImageFile: newImageFile ?? this.newImageFile,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [status, userProfile, newImageFile, errorMessage];
}
