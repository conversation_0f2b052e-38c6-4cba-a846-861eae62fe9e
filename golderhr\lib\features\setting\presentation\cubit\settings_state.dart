// features/setting/presentation/cubit/settings_state.dart

import 'package:equatable/equatable.dart';

class SettingsState extends Equatable {
  final bool isFaceRecognitionEnabled;
  final bool isBiometricLoginEnabled;
  final bool isAutoCheckOutEnabled;

  const SettingsState({
    this.isFaceRecognitionEnabled = false, // Giá trị mặc định
    this.isBiometricLoginEnabled = false,
    this.isAutoCheckOutEnabled = true,
  });

  SettingsState copyWith({
    bool? isFaceRecognitionEnabled,
    bool? isBiometricLoginEnabled,
    bool? isAutoCheckOutEnabled,
  }) {
    return SettingsState(
      isFaceRecognitionEnabled:
          isFaceRecognitionEnabled ?? this.isFaceRecognitionEnabled,
      isBiometricLoginEnabled:
          isBiometricLoginEnabled ?? this.isBiometricLoginEnabled,
      isAutoCheckOutEnabled:
          isAutoCheckOutEnabled ?? this.isAutoCheckOutEnabled,
    );
  }

  @override
  List<Object> get props => [
    isFaceRecognitionEnabled,
    isBiometricLoginEnabled,
    isAutoCheckOutEnabled,
  ];
}
