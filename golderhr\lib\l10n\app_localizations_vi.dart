// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Vietnamese (`vi`).
class AppLocalizationsVi extends AppLocalizations {
  AppLocalizationsVi([String locale = 'vi']) : super(locale);

  @override
  String get acceptTerms => 'Tôi đồng ý với điều khoản và điều kiện';

  @override
  String get authForgotPassword => 'Quên mật khẩu';

  @override
  String get authNext => 'Tiếp theo';

  @override
  String get authResetPassword => 'Đặt lại mật khẩu';

  @override
  String get authSubTitleForgotPassword =>
      'Nhập email của bạn để đặt lại mật khẩu.';

  @override
  String get authSubTitleResetPassword => 'Nhập mật khẩu mới của bạn.';

  @override
  String get authSubTitleVerifyOtp =>
      'Nhập mã 4 chữ số được gửi đến email của bạn.';

  @override
  String get authTitleResetPassword => 'Đặt lại mật khẩu';

  @override
  String get authVerifyOtp => 'Xác minh OTP';

  @override
  String get changePassword => 'Đổi mật khẩu';

  @override
  String get contactInfo => 'Thông tin liên hệ';

  @override
  String get detectFaceAnalyzingSecurity => 'Đang phân tích bảo mật...';

  @override
  String get detectFaceAttendanceCompleted => 'Đã hoàn thành điểm danh';

  @override
  String get detectFaceCaptureImage => 'Chụp Ảnh';

  @override
  String get detectFaceCaptureToStart => 'Chụp ảnh để bắt đầu';

  @override
  String get detectFaceCheckIn => 'Chấm Công';

  @override
  String detectFaceCheckInError(Object error) {
    return 'Chấm công thất bại: $error';
  }

  @override
  String get detectFaceCheckInSuccess => 'Chấm công thành công lúc';

  @override
  String get detectFaceCheckOut => 'Đăng xuất';

  @override
  String detectFaceCheckOutError(Object error) {
    return 'Face CheckOut Error $error';
  }

  @override
  String get detectFaceCheckOutSuccess => 'Check-out successful';

  @override
  String get detectFaceCurrentLocation => 'Vị trí hiện tại';

  @override
  String get detectFaceFaceAnalysis => 'Phân tích khuôn mặt';

  @override
  String get detectFaceFaceAnalysisDescription =>
      'Sử dụng ML để xác định vị trí, các đặc điểm trên khuôn mặt và đảm bảo mắt mở.';

  @override
  String get detectFaceFaceNotFound => 'Không tìm thấy khuôn mặt trong ảnh.';

  @override
  String detectFaceFacesDetected(Object count) {
    return 'Tìm thấy $count khuôn mặt';
  }

  @override
  String get detectFaceGettingLocation => 'Đang lấy vị trí...';

  @override
  String get detectFaceImageCapturedReady =>
      'Ảnh đã được chụp. Sẵn sàng chấm công.';

  @override
  String get detectFaceImageNotCaptured => 'Đã hủy chụp ảnh.';

  @override
  String get detectFaceLocationNotAvailable => 'Face Location Not Available';

  @override
  String get detectFaceLocationPermissionDenied =>
      'Quyền truy cập vị trí đã bị từ chối.';

  @override
  String get detectFaceLocationPermissionPermanentlyDenied =>
      'Quyền vị trí bị từ chối vĩnh viễn.';

  @override
  String get detectFaceLocationVerification => 'Xác minh vị trí';

  @override
  String get detectFaceLocationVerificationDescription =>
      'Đảm bảo vị trí chấm công nằm trong khu vực hợp lệ của công ty.';

  @override
  String get detectFaceOpeningCamera => 'Đang mở camera...';

  @override
  String get detectFaceProcessingCheckIn => 'Đang xử lý chấm công...';

  @override
  String get detectFaceProcessingCheckOut => 'Đang xử lý đăng xuất...';

  @override
  String get detectFaceReadyForCheckIn => 'Sẵn sàng để chấm công';

  @override
  String get detectFaceReadyForCheckOut => 'Sẵn sàng để đăng xuất';

  @override
  String get detectFaceRetakeImage => 'Chụp Lại';

  @override
  String get detectFaceSecureCheckInSystem => 'Hệ thống chấm công bảo mật';

  @override
  String get detectFaceSecureCheckInTitle => 'Chấm Công Bảo Mật';

  @override
  String get detectFaceSecurityInfoNotice =>
      'Hệ thống sử dụng nhiều lớp phân tích để đảm bảo tính minh bạch và công bằng.';

  @override
  String get detectFaceSecurityInfoTitle => 'Thông Tin Bảo Mật';

  @override
  String get detectFaceSecurityInfoTooltip => 'Thông tin bảo mật';

  @override
  String get detectFaceSystemStatus => 'TRẠNG THÁI HỆ THỐNG';

  @override
  String get detectFaceUnderstood => 'Đã hiểu';

  @override
  String get detectFaceSystemWillCheck =>
      'System will verify the authenticity of the image';

  @override
  String get detectFaceUpdateLocationTooltip => 'Cập nhật vị trí';

  @override
  String get editProfile => 'Chỉnh sửa hồ sơ';

  @override
  String get error => 'Lỗi';

  @override
  String get home => 'Trang chủ';

  @override
  String get homeErrorFunction => 'Lỗi cấu hình chức năng';

  @override
  String get homeFaceRecognition => 'Nhận diện khuôn mặt';

  @override
  String get homeGoodAfternoon => 'Chào buổi chiều';

  @override
  String get homeGoodEvening => 'Chào buổi tối';

  @override
  String get homeGoodMorning => 'Chào buổi sáng';

  @override
  String get homeLeave => 'Nghỉ phép';

  @override
  String get homeNotificationAndUpdate => 'Thông báo & Cập nhật';

  @override
  String get homeOvertime => 'Tăng ca';

  @override
  String get homeTodayAttendance => 'Điểm danh hôm nay';

  @override
  String get homeWorkHours => 'Giờ làm';

  @override
  String get loginEmail => 'Email';

  @override
  String get loginForgotPassword => 'Quên mật khẩu?';

  @override
  String get loginHintEmail => 'Nhập email của bạn';

  @override
  String get loginHintPassword => 'Nhập mật khẩu của bạn';

  @override
  String get loginNoAccount => 'Không có tài khoản?';

  @override
  String get loginPassword => 'Mật khẩu';

  @override
  String get loginPasswordMinLength => 'Mật khẩu phải có ít nhất 6 ký tự';

  @override
  String get loginPleaseEnterEmail => 'Vui lòng nhập email';

  @override
  String get loginPleaseEnterFullName => 'Vui lòng nhập họ tên của bạn';

  @override
  String get loginPleaseEnterPassword => 'Vui lòng nhập mật khẩu';

  @override
  String get loginRememberMe => 'Ghi nhớ tôi';

  @override
  String get loginSignIn => 'Đăng nhập';

  @override
  String get loginSignUp => 'Đăng ký';

  @override
  String get loginSubtitle =>
      'Rất vui được gặp lại bạn. Hãy đăng nhập để tiếp tục.';

  @override
  String get loginSuccess => 'Đăng nhập thành công';

  @override
  String get loginTitle => 'Chào mừng quay lại!';

  @override
  String get logout => 'Đăng xuất';

  @override
  String get logOutSuccess => 'Đăng xuất thành công';

  @override
  String get moreAllFeatures => 'Tất cả chức năng';

  @override
  String get moreAttendance => 'Điểm danh';

  @override
  String get moreCalendar => 'Lịch làm việc';

  @override
  String get moreCRM => 'Khách hàng & Kinh doanh (CRM)';

  @override
  String get moreHRM => 'Quản lý nhân sự (HRM)';

  @override
  String get moreLeave => 'Nghỉ phép';

  @override
  String get moreOverTime => 'Làm thêm giờ';

  @override
  String get moreQR => 'Quét QR';

  @override
  String get moreSetting => 'Cài đặt';

  @override
  String get moreSupport => 'Hỗ trợ';

  @override
  String get moreTeam => 'Đội nhóm';

  @override
  String get moreTraining => 'Đào tạo';

  @override
  String get moreRecruitment => ' Tuyển dụng';

  @override
  String get moreUtility => 'Tiện ích & Cài đặt';

  @override
  String get navigationBarCustomer => 'Khách hàng';

  @override
  String get navigationBarHome => 'Trang chủ';

  @override
  String get navigationBarMore => 'Thêm';

  @override
  String get navigationBarNotify => 'Thông báo';

  @override
  String get navigationBarWork => 'Công việc';

  @override
  String get notificationAll => 'Tất cả';

  @override
  String get notificationAllRead => 'Tất cả đã đọc';

  @override
  String get notificationCategory => 'Phân loại';

  @override
  String get notificationCustomers => 'Khách hàng';

  @override
  String get notificationDetail => 'Chi tiết thông báo';

  @override
  String get notificationEmpty => 'Bạn không có thông báo quan trọng nào';

  @override
  String get notificationEmptyFromCustomer =>
      'No notifications from customers yet';

  @override
  String get notificationImportant => 'Quang Trọng ';

  @override
  String get notificationLevel => 'Mức độ';

  @override
  String get notificationNoNewUpdates => 'Chưa có gì mới';

  @override
  String get notificationPlaceholder =>
      'Tất cả thông báo của bạn sẽ hiện tại đây';

  @override
  String get notificationReceivedTime => 'Thời gian nhận';

  @override
  String get notificationTittle => 'Thông báo';

  @override
  String get notificationUnread => 'Chưa đọc';

  @override
  String get notificationViewAll => 'Bạn đã xem hết tất cả thông báo';

  @override
  String get onTime => 'On Time';

  @override
  String get otpNotReceived => 'OTP không được nhận';

  @override
  String get otpVerifySuccessfully => 'Xác minh OTP thành công';

  @override
  String get passwordResetSuccessfully => 'Đặt lại mật khẩu thành công!';

  @override
  String get pleaseEnterConfirmPassword => 'Vui lòng nhập mật khẩu xác nhận';

  @override
  String get profile => 'Hồ sơ';

  @override
  String get profileDepartment => 'Phòng ban';

  @override
  String get profileJobInfo => 'Thông tin công việc';

  @override
  String get profilePhone => 'Số điện thoại';

  @override
  String get profilePosition => 'Chức vụ';

  @override
  String get professionalInformation => 'Thông tin nghề nghiệp';

  @override
  String get fullName => 'Họ và tên';

  @override
  String get registerConfirmPassword => 'Xác nhận mật khẩu';

  @override
  String get registerEmail => 'Email';

  @override
  String get registerFullName => 'Họ tên';

  @override
  String get registerHaveAccount => 'Đã có tài khoản?';

  @override
  String get registerPassword => 'Mật khẩu';

  @override
  String get registerPasswordsDoNotMatch => 'Passwords do not match';

  @override
  String get registerPleaseConfirmPassword => 'Please confirm your password';

  @override
  String get registerSubTitleSignUp => 'Nhập thông tin để tạo tài khoản.';

  @override
  String get registerSuccess => 'Đăng ký thành công';

  @override
  String get registerTitleSignUp => 'Tạo tài khoản';

  @override
  String get resendCode => 'Gửi lại mã';

  @override
  String get retry => 'Thử lại';

  @override
  String get save => 'Lưu';

  @override
  String get setting => 'Cài đặt';

  @override
  String get settingAccount => 'Tài khoản';

  @override
  String get settingAddImage => 'Thêm Ảnh';

  @override
  String get settingApp => 'Tùy chọn ứng dụng';

  @override
  String get settingAutoCheckOut => 'Đăng xuất tự động';

  @override
  String get settingBiometricLogin => 'Đăng nhập bằng vân tay';

  @override
  String get settingCancel => 'Hủy';

  @override
  String get settingCheckInAndSecurity => 'Chấm công & bảo mật';

  @override
  String get settingCheckUpDate => 'Kiểm tra cập nhật';

  @override
  String get settingChooseAnEmployeeFromList =>
      'Chọn một nhân viên từ danh sách';

  @override
  String get settingChooseEmployee => 'Chọn nhân viên';

  @override
  String get settingChooseImageALibrary => 'Chọn ảnh từ thư viện';

  @override
  String get settingConfirmLogOut => 'Bạn có chắc chắn muốn đăng xuất?';

  @override
  String get settingConfirmPassword => 'Xác nhận mật khẩu';

  @override
  String get settingContactSupport => 'Liên hệ hỗ trợ';

  @override
  String get settingCurrentPassword => 'Mật khẩu hiện tại';

  @override
  String get settingEmailSupport => 'Email hỗ trợ';

  @override
  String get settingError => 'Đã xảy ra lỗi vui lòng thử lại sau!';

  @override
  String get settingFaceRecognition => 'Nhận diện khuôn mặt';

  @override
  String get settingHelpCenter => 'Trung tâm trợ giúp';

  @override
  String get settingIntroduce => 'Giới thiệu';

  @override
  String get settingLanguage => 'Ngôn ngữ';

  @override
  String get settingLatestVersion => 'Phiên bản mới nhất';

  @override
  String get settingMessageErrorEmail =>
      'Không tìm thấy ứng dụng email nào trên thiết bị.';

  @override
  String get settingMessageErrorPhone => 'Không thể mở ứng dụng gọi điện.';

  @override
  String get settingMessageErrorSocial => 'Không thể mở liên kết này.';

  @override
  String get settingMessageSupport => 'Tin nhắn hỗ trợ';

  @override
  String get settingNewPassword => 'Mật khẩu mới';

  @override
  String get settingPhoneSupport => 'Số điện thoại hỗ trợ';

  @override
  String get settingSubChangePass => 'Chỉnh sửa mật khẩu';

  @override
  String get settingSubEnableAutoCheckOut => 'Bật đăng xuất tự động';

  @override
  String get settingSubEnableFaceRecognition => 'Bật nhận diện khuôn mặt';

  @override
  String get settingSubGetHelp => 'Nhận giúp và hỗ trợ';

  @override
  String get settingSubTitleProfile => 'Chỉnh sửa thông tin cá nhân';

  @override
  String get settingSubUseBiometricLogin => 'Sử dụng đăng nhập bằng vân tay';

  @override
  String get settingSupport => 'Hỗ trợ';

  @override
  String get settingTheme => 'Giao diện';

  @override
  String get settingTitleProfile => 'Thông tin cá nhân';

  @override
  String get settingUploadImage => 'Upload ảnh';

  @override
  String get settingUploadImageSuccess => 'Upload ảnh thành công';

  @override
  String get settingVersion => 'Phiên bản';

  @override
  String get success => 'Thành công';

  @override
  String get thisWeek => 'Tuần này';

  @override
  String get today => 'Hôm nay';

  @override
  String get initializing => 'Đang khởi tạo';

  @override
  String get refreshingStatus => 'Đang làm mới trạng thái...';

  @override
  String get errorLoadingAttendanceStatus => 'Lỗi tải trạng thái chấm công.';

  @override
  String get allSessionsCompleted => 'Tất cả các phiên đã hoàn thành.';

  @override
  String get noFurtherActionsAvailable => 'Không còn hành động nào khác.';

  @override
  String get locationServicesDisabled => 'Dịch vụ định vị đã bị tắt.';

  @override
  String detectFaceFaceFoundCount(Object count) {
    return 'Đã phát hiện khuôn mặt ($count)';
  }

  @override
  String errorDetectingFaces(Object e) {
    return 'Lỗi khi phát hiện khuôn mặt. $e';
  }

  @override
  String notReadyForAction(Object e) {
    return 'Hệ thống chưa sẵn sàng để thực hiện hành động. $e';
  }

  @override
  String get checkIn => 'Chấm công';

  @override
  String get imageCleared => 'Hình ảnh đã được xóa.';

  @override
  String get statusUpdated => 'Trạng thái đã được cập nhật.';

  @override
  String errorGettingLocation(Object error) {
    return 'Lỗi lấy vị trí: $error';
  }

  @override
  String errorCapturingImage(Object error) {
    return 'Lỗi chụp ảnh: $error';
  }

  @override
  String get lastCheckOut => 'Lần chấm công ra gần nhất';

  @override
  String get lastCheckIn => 'Lần chấm công vào gần nhất';

  @override
  String get totalCheckIns => 'Tổng số lần chấm công vào';

  @override
  String get totalCheckOuts => 'Tổng số lần chấm công ra';

  @override
  String get notCurrentlyWorking => 'Hiện không làm việc';

  @override
  String get working => 'Đang làm việc';

  @override
  String get loading => 'Đang tải...';

  @override
  String get retryInitialization => 'Thử khởi động lại';

  @override
  String get noActionAvailable => 'Không có hành động nào';

  @override
  String get startNewSession => 'Bắt đầu phiên mới';

  @override
  String get notification => 'Thông báo';

  @override
  String get errorLoadingData => 'Lỗi tải dữ liệu';

  @override
  String get detectFaceConfirmCheckOut =>
      'Đã phát hiện khuôn mặt. Xác nhận chấm công ra?';

  @override
  String get detectFaceConfirmCheckIn =>
      'Đã phát hiện khuôn mặt. Xác nhận chấm công vào?';

  @override
  String get clearImage => 'Xóa hình ảnh';

  @override
  String get checkOut => 'Check out';

  @override
  String get unexpectedErrorPleaseRetry => 'Đã xảy ra lỗi. Vui lòng thử lại.';

  @override
  String get noFaceDetectedInImage => 'Không phát hiện khuôn mặt trong ảnh.';

  @override
  String get pleaseCaptureImage => 'Vui lòng chụp hình ảnh.';

  @override
  String get pleaseWaitForLocation => 'Vui lòng đợi lấy dữ liệu vị trí...';

  @override
  String get attendance => 'Điểm danh';

  @override
  String get thisMonth => 'Tháng này';

  @override
  String get unexpectedError => 'Đã xảy ra lỗi không mong muốn.';

  @override
  String get todaysSummary => 'Tổng kết hôm nay';

  @override
  String get checkedIn => 'Đã vào ca';

  @override
  String get notCheckedIn => 'Chưa vào ca';

  @override
  String get totalHours => 'Tổng giờ';

  @override
  String get overtime => 'Tăng ca';

  @override
  String get weeklySummary => 'Tổng kết tuần';

  @override
  String get workDays => 'Ngày làm việc';

  @override
  String get lateArrivals => 'Đi trễ';

  @override
  String get weeklyPerformance => 'Hiệu suất tuần';

  @override
  String get monthlySummary => 'Tổng kết tháng';

  @override
  String get overtimeRequest => 'Yêu cầu làm thêm giờ';

  @override
  String get overtimeSummary => 'Tóm tắt làm thêm giờ';

  @override
  String get overtimeDetails => 'Chi tiết làm thêm giờ';

  @override
  String get newRequest => 'Yêu cầu mới';

  @override
  String get myOvertime => 'Làm thêm giờ của tôi';

  @override
  String get pending => 'Chờ duyệt';

  @override
  String get approved => 'Đã duyệt';

  @override
  String get rejected => 'Bị từ chối';

  @override
  String get date => 'Ngày';

  @override
  String get startTime => 'Giờ bắt đầu';

  @override
  String get endTime => 'Giờ kết thúc';

  @override
  String get reason => 'Lý do';

  @override
  String get overtimeType => 'Loại làm thêm giờ';

  @override
  String get regularOvertime => 'Thường';

  @override
  String get weekendOvertime => 'Cuối tuần';

  @override
  String get holidayOvertime => 'Ngày lễ';

  @override
  String get submitRequest => 'Gửi yêu cầu';

  @override
  String get submitting => 'Đang gửi...';

  @override
  String get select => 'Chọn';

  @override
  String get hour => 'Giờ';

  @override
  String get minute => 'Phút';

  @override
  String get period => 'Buổi';

  @override
  String get selected => 'Đã chọn';

  @override
  String get pleaseSelectAllFields =>
      'Vui lòng chọn tất cả các trường bắt buộc';

  @override
  String get endTimeMustBeAfterStartTime => 'Giờ kết thúc phải sau giờ bắt đầu';

  @override
  String get cannotSelectPastDates =>
      'Không thể chọn ngày đã qua cho yêu cầu làm thêm giờ';

  @override
  String get cannotSelectPastTime =>
      'Không thể chọn giờ đã qua cho làm thêm giờ hôm nay';

  @override
  String get minimumOvertimeDuration =>
      'Thời gian làm thêm giờ phải ít nhất 30 phút';

  @override
  String get maximumOvertimeDuration =>
      'Thời gian làm thêm giờ không được quá 12 tiếng';

  @override
  String get reasonableWorkingHours =>
      'Giờ làm thêm nên từ 6:00 sáng đến 11:00 tối';

  @override
  String get filterByStatus => 'Lọc theo trạng thái';

  @override
  String get allStatus => 'Tất cả trạng thái';

  @override
  String get noOvertimeRequests => 'Không có yêu cầu làm thêm giờ';

  @override
  String get submitYourFirstOvertimeRequest =>
      'Gửi yêu cầu làm thêm giờ đầu tiên của bạn bằng tab Yêu cầu mới';

  @override
  String get loadMore => 'Tải thêm';

  @override
  String get rejectionReason => 'Lý do từ chối';

  @override
  String get all => 'Tất cả';

  @override
  String get totalRequests => 'Tổng số đơn';

  @override
  String get approve => 'Duyệt';

  @override
  String get reject => 'Từ chối';

  @override
  String get rejectRequest => 'Từ chối đơn';

  @override
  String get rejectionReasonDescription =>
      'Vui lòng cung cấp lý do rõ ràng để từ chối đơn làm thêm giờ này. Điều này sẽ giúp nhân viên hiểu được quyết định.';

  @override
  String get enterRejectionReason => 'Nhập lý do từ chối...';

  @override
  String get rejectionReasonRequired => 'Lý do từ chối là bắt buộc';

  @override
  String get rejectionReasonTooShort =>
      'Lý do từ chối phải có ít nhất 10 ký tự';

  @override
  String get rejectionReasonTooLong =>
      'Lý do từ chối không được vượt quá 500 ký tự';

  @override
  String get overtimeReasonRequired => 'Lý do làm thêm giờ là bắt buộc';

  @override
  String get overtimeReasonTooShort => 'Lý do phải có ít nhất 10 ký tự';

  @override
  String get overtimeReasonTooLong => 'Lý do không được vượt quá 500 ký tự';

  @override
  String get approvedBy => 'Được duyệt bởi';

  @override
  String get rejectedBy => 'Bị từ chối bởi';

  @override
  String get time => 'Thời gian';

  @override
  String get duration => 'Thời gian';

  @override
  String get daysOff => 'Ngày nghỉ';

  @override
  String get fullMonthHistory => 'Lịch sử cả tháng';

  @override
  String get inShort => 'Vào';

  @override
  String get outShort => 'Ra';

  @override
  String get hoursShort => 'Giờ';

  @override
  String get viewFullHistory => 'Xem lịch sử đầy đủ';

  @override
  String get selectDateToViewDetails => 'Chọn ngày để xem chi tiết';

  @override
  String get errorOccurred => 'Đã xảy ra lỗi';

  @override
  String get selectApprover => 'Chọn người duyệt';

  @override
  String get noApproversAvailable => 'Không có người duyệt';

  @override
  String get cancel => 'Hủy';

  @override
  String get createRequest => 'Tạo đơn';

  @override
  String get loadingOvertimeHistory => 'Đang tải lịch sử làm thêm giờ...';

  @override
  String get overtimeHistory => 'Lịch sử làm thêm giờ';

  @override
  String get viewAll => 'Xem tất cả';

  @override
  String get recentRequests => 'Yêu cầu gần đây';

  @override
  String get calendar => 'Lịch';

  @override
  String get calendarPageTitle => 'Lịch';

  @override
  String get calendarTabMonth => 'Tháng';

  @override
  String get calendarTabWeek => 'Tuần';

  @override
  String get calendarTabAgenda => 'Lịch trình';

  @override
  String get calendarHeaderEvents => 'Sự kiện';

  @override
  String get calendarHeaderMeetings => 'Cuộc họp';

  @override
  String get calendarHeaderHolidays => 'Ngày lễ';

  @override
  String get calendarWeekViewTitle => 'Xem theo tuần';

  @override
  String get calendarWeekEventsTitle => 'Sự kiện trong tuần';

  @override
  String get calendarAddEventDialogTitle => 'Thêm sự kiện';

  @override
  String get calendarEventTitleHint => 'Tiêu đề sự kiện';

  @override
  String get calendarTimeHint => 'Thời gian';

  @override
  String get calendarEventTypeHint => 'Loại sự kiện';

  @override
  String get calendarAddEventButton => 'Thêm sự kiện';

  @override
  String calendarEventsOnDate(String date) {
    return 'Sự kiện ngày $date';
  }

  @override
  String get calendarDayAbbreviationsSun => 'CN';

  @override
  String get calendarDayAbbreviationsMon => 'T2';

  @override
  String get calendarDayAbbreviationsTue => 'T3';

  @override
  String get calendarDayAbbreviationsWed => 'T4';

  @override
  String get calendarDayAbbreviationsThu => 'T5';

  @override
  String get calendarDayAbbreviationsFri => 'T6';

  @override
  String get calendarDayAbbreviationsSat => 'T7';

  @override
  String get calendarFullMonthNamesJan => 'Tháng 1';

  @override
  String get calendarFullMonthNamesFeb => 'Tháng 2';

  @override
  String get calendarFullMonthNamesMar => 'Tháng 3';

  @override
  String get calendarFullMonthNamesApr => 'Tháng 4';

  @override
  String get calendarFullMonthNamesMay => 'Tháng 5';

  @override
  String get calendarFullMonthNamesJun => 'Tháng 6';

  @override
  String get calendarFullMonthNamesJul => 'Tháng 7';

  @override
  String get calendarFullMonthNamesAug => 'Tháng 8';

  @override
  String get calendarFullMonthNamesSep => 'Tháng 9';

  @override
  String get calendarFullMonthNamesOct => 'Tháng 10';

  @override
  String get calendarFullMonthNamesNov => 'Tháng 11';

  @override
  String get calendarFullMonthNamesDec => 'Tháng 12';

  @override
  String get calendarShortMonthNamesJan => 'Th1';

  @override
  String get calendarShortMonthNamesFeb => 'Th2';

  @override
  String get calendarShortMonthNamesMar => 'Th3';

  @override
  String get calendarShortMonthNamesApr => 'Th4';

  @override
  String get calendarShortMonthNamesMay => 'Th5';

  @override
  String get calendarShortMonthNamesJun => 'Th6';

  @override
  String get calendarShortMonthNamesJul => 'Th7';

  @override
  String get calendarShortMonthNamesAug => 'Th8';

  @override
  String get calendarShortMonthNamesSep => 'Th9';

  @override
  String get calendarShortMonthNamesOct => 'Th10';

  @override
  String get calendarShortMonthNamesNov => 'Th11';

  @override
  String get calendarShortMonthNamesDec => 'Th12';

  @override
  String get calendarSampleStatEventsCount => '12';

  @override
  String get calendarSampleStatMeetingsCount => '8';

  @override
  String get calendarSampleStatHolidaysCount => '3';

  @override
  String get tomorrow => 'Ngày mai';

  @override
  String get noEventsForThisWeek => 'Không có sự kiện nào trong tuần này';

  @override
  String get noUpcomingEvents => 'Không có sự kiện sắp tới';

  @override
  String get addEvent => 'Thêm sự kiện';

  @override
  String get editEvent => 'Sửa sự kiện';

  @override
  String get deleteEvent => 'Xóa sự kiện';

  @override
  String get eventDetails => 'Chi tiết sự kiện';

  @override
  String get eventTitle => 'Tiêu đề sự kiện';

  @override
  String get eventDescription => 'Mô tả sự kiện';

  @override
  String get eventDate => 'Ngày sự kiện';

  @override
  String get eventTime => 'Thời gian sự kiện';

  @override
  String get eventType => 'Loại sự kiện';

  @override
  String get eventLocation => 'Địa điểm';

  @override
  String get allDay => 'Cả ngày';

  @override
  String get recurring => 'Lặp lại';

  @override
  String get attendees => 'Người tham gia';

  @override
  String get meeting => 'Cuộc họp';

  @override
  String get leave => 'Nghỉ phép';

  @override
  String get holiday => 'Ngày lễ';

  @override
  String get training => 'Đào tạo';

  @override
  String get event => 'Sự kiện';

  @override
  String get searchEvents => 'Tìm kiếm sự kiện';

  @override
  String get noEventsFound => 'Không tìm thấy sự kiện nào';

  @override
  String get loadingEvents => 'Đang tải sự kiện...';

  @override
  String get addingEvent => 'Đang thêm sự kiện...';

  @override
  String get updatingEvent => 'Đang cập nhật sự kiện...';

  @override
  String get deletingEvent => 'Đang xóa sự kiện...';

  @override
  String get eventAddedSuccessfully => 'Thêm sự kiện thành công';

  @override
  String get eventUpdatedSuccessfully => 'Cập nhật sự kiện thành công';

  @override
  String get eventDeletedSuccessfully => 'Xóa sự kiện thành công';

  @override
  String get failedToAddEvent => 'Thêm sự kiện thất bại';

  @override
  String get failedToUpdateEvent => 'Cập nhật sự kiện thất bại';

  @override
  String get failedToDeleteEvent => 'Xóa sự kiện thất bại';

  @override
  String get confirmDeleteEvent => 'Bạn có chắc chắn muốn xóa sự kiện này?';

  @override
  String confirmDeleteEvents(int count) {
    return 'Bạn có chắc chắn muốn xóa $count sự kiện?';
  }

  @override
  String get selectEventType => 'Chọn loại sự kiện';

  @override
  String get selectDate => 'Chọn ngày';

  @override
  String get selectTime => 'Chọn thời gian';

  @override
  String get edit => 'Sửa';

  @override
  String get delete => 'Xóa';

  @override
  String get refresh => 'Làm mới';

  @override
  String get sync => 'Đồng bộ';

  @override
  String get export => 'Xuất';

  @override
  String get import => 'Nhập';

  @override
  String get filter => 'Lọc';

  @override
  String get clear => 'Xóa';

  @override
  String get apply => 'Áp dụng';

  @override
  String get close => 'Đóng';

  @override
  String get sessionDetails => 'Chi tiết phiên';

  @override
  String sessionNumber(int number) {
    return 'Lần $number';
  }

  @override
  String multipleSessionsDetails(int count) {
    return 'Chi tiết các lần chấm công ($count)';
  }

  @override
  String get sessionCompleted => 'Hoàn thành';

  @override
  String get sessionInProgress => 'Đang thực hiện';

  @override
  String get location => 'Vị trí';

  @override
  String get unknownLocation => 'Vị trí không xác định';

  @override
  String get statusOnTime => 'Đúng giờ';

  @override
  String get statusLate => 'Đi trễ';

  @override
  String get statusAbsent => 'Vắng mặt';

  @override
  String get statusOnLeave => 'Nghỉ phép';

  @override
  String get statusWeekend => 'Cuối tuần';

  @override
  String get statusNoRecord => 'Không có dữ liệu';

  @override
  String get sunday => 'Chủ nhật';

  @override
  String get monday => 'Thứ hai';

  @override
  String get tuesday => 'Thứ ba';

  @override
  String get wednesday => 'Thứ tư';

  @override
  String get thursday => 'Thứ năm';

  @override
  String get friday => 'Thứ sáu';

  @override
  String get saturday => 'Thứ bảy';

  @override
  String get january => 'Tháng 1';

  @override
  String get february => 'Tháng 2';

  @override
  String get march => 'Tháng 3';

  @override
  String get april => 'Tháng 4';

  @override
  String get may => 'Tháng 5';

  @override
  String get june => 'Tháng 6';

  @override
  String get july => 'Tháng 7';

  @override
  String get august => 'Tháng 8';

  @override
  String get september => 'Tháng 9';

  @override
  String get october => 'Tháng 10';

  @override
  String get november => 'Tháng 11';

  @override
  String get december => 'Tháng 12';

  @override
  String get createOvertimeRequest => 'Tạo yêu cầu làm thêm giờ';

  @override
  String get overtimeDate => 'Ngày làm thêm';

  @override
  String get approver => 'Người duyệt';

  @override
  String get pleaseSelectDate => 'Vui lòng chọn ngày';

  @override
  String get pleaseSelectStartTime => 'Vui lòng chọn giờ bắt đầu';

  @override
  String get pleaseSelectEndTime => 'Vui lòng chọn giờ kết thúc';

  @override
  String get pleaseEnterReason => 'Vui lòng nhập lý do';

  @override
  String get pleaseSelectApprover => 'Vui lòng chọn người duyệt';

  @override
  String get overtimeRequestSubmitted => 'Gửi yêu cầu làm thêm giờ thành công';

  @override
  String get failedToSubmitRequest => 'Gửi yêu cầu thất bại';

  @override
  String get loadingOvertimeRequests => 'Đang tải yêu cầu làm thêm giờ...';

  @override
  String get overtimeRequestDetails => 'Chi tiết yêu cầu làm thêm giờ';

  @override
  String get requestDate => 'Ngày yêu cầu';

  @override
  String get hours => 'giờ';

  @override
  String get minutes => 'phút';

  @override
  String get pendingApproval => 'Chờ phê duyệt';

  @override
  String get submittedOn => 'Đã gửi vào';

  @override
  String get fillDetailsToSubmit =>
      'Điền thông tin chi tiết bên dưới để gửi yêu cầu làm thêm giờ';

  @override
  String get leaveHistory => 'Lịch sử nghỉ phép';

  @override
  String get leaveRequest => 'Yêu cầu nghỉ phép';

  @override
  String get leaveRequestDetails => 'Chi tiết yêu cầu nghỉ phép';

  @override
  String get myLeave => 'Nghỉ phép của tôi';

  @override
  String get submitLeaveRequest => 'Gửi yêu cầu nghỉ phép';

  @override
  String get fillLeaveDetailsToSubmit =>
      'Điền thông tin chi tiết bên dưới để gửi yêu cầu nghỉ phép của bạn';

  @override
  String get remainingLeaveDays => 'Số ngày nghỉ phép còn lại';

  @override
  String outOfDays(int totalDays) {
    return 'trên tổng số $totalDays ngày';
  }

  @override
  String get used => 'Đã sử dụng';

  @override
  String get cancelled => 'Đã hủy';

  @override
  String get days => 'ngày';

  @override
  String get day => 'ngày';

  @override
  String get leavePolicy => 'Chính sách nghỉ phép';

  @override
  String get recentLeaveRequests => 'Yêu cầu nghỉ phép gần đây';

  @override
  String get viewAllLeaveHistory => 'Xem tất cả lịch sử';

  @override
  String get noLeaveRequestsYet => 'Chưa có yêu cầu nghỉ phép nào';

  @override
  String noFilteredRequests(String status) {
    return 'Không có yêu cầu $status';
  }

  @override
  String get yourLeaveRequestsWillAppearHere =>
      'Các yêu cầu nghỉ phép của bạn sẽ hiển thị ở đây';

  @override
  String get showAll => 'Hiển thị tất cả';

  @override
  String get editRequest => 'Chỉnh sửa yêu cầu';

  @override
  String get cancelRequest => 'Hủy yêu cầu';

  @override
  String get requestDetails => 'Chi tiết yêu cầu';

  @override
  String get leaveType => 'Loại nghỉ phép';

  @override
  String get startDate => 'Ngày bắt đầu';

  @override
  String get endDate => 'Ngày kết thúc';

  @override
  String get status => 'Trạng thái';

  @override
  String get approvedOn => 'Đã duyệt vào';

  @override
  String get rejectedOn => 'Bị từ chối vào';

  @override
  String get cancelRequestConfirm =>
      'Bạn có chắc chắn muốn hủy yêu cầu nghỉ phép này không?';

  @override
  String get no => 'Không';

  @override
  String get yes => 'Có';

  @override
  String get requestCancelled => 'Yêu cầu đã được hủy';

  @override
  String get editFunctionalityComingSoon => 'Chức năng chỉnh sửa sẽ sớm có';

  @override
  String get annualLeave => 'Nghỉ phép năm';

  @override
  String get sickLeave => 'Nghỉ ốm';

  @override
  String get personalLeave => 'Nghỉ cá nhân';

  @override
  String get maternityLeave => 'Nghỉ thai sản';

  @override
  String get unpaidLeave => 'Nghỉ không lương';

  @override
  String get annualLeaveDescription =>
      'Nghỉ dưỡng sức, kỳ nghỉ hoặc thời gian cá nhân';

  @override
  String get sickLeaveDescription => 'Khám bệnh hoặc ốm đau';

  @override
  String get personalLeaveDescription => 'Việc cá nhân hoặc khẩn cấp';

  @override
  String get maternityLeaveDescription => 'Nghỉ thai sản hoặc chăm sóc con';

  @override
  String get unpaidLeaveDescription => 'Nghỉ dài hạn không lương';

  @override
  String get dateRange => 'Khoảng thời gian';

  @override
  String durationInfo(int duration, String durationUnit) {
    return 'Thời gian: $duration $durationUnit';
  }

  @override
  String get reasonForLeave => 'Lý do nghỉ phép';

  @override
  String get leaveReasonHint =>
      'Vui lòng cung cấp lý do chi tiết cho yêu cầu nghỉ phép của bạn...\n\nVí dụ:\n• Nghỉ dưỡng sức gia đình\n• Khám bệnh\n• Việc cá nhân khẩn cấp';

  @override
  String get pleaseProvideReason => 'Vui lòng cung cấp lý do nghỉ phép';

  @override
  String get pleaseFillAllFields => 'Vui lòng điền tất cả các trường bắt buộc';

  @override
  String get pleaseSelectStartDate => 'Vui lòng chọn ngày bắt đầu';

  @override
  String get pleaseSelectEndDate => 'Vui lòng chọn ngày kết thúc';

  @override
  String get leaveRequestSubmittedSuccess =>
      'Đã gửi yêu cầu nghỉ phép thành công!';

  @override
  String failedToLoadLeaveData(String error) {
    return 'Không thể tải dữ liệu nghỉ phép: $error';
  }

  @override
  String failedToSubmitLeaveRequest(String error) {
    return 'Không thể gửi yêu cầu nghỉ phép: $error';
  }

  @override
  String get adminUserManagement => 'Quản lý người dùng';

  @override
  String get adminUsers => 'Người dùng';

  @override
  String get adminStatistics => 'Thống kê';

  @override
  String get adminSettings => 'Cài đặt';

  @override
  String get addNewUser => 'Thêm người dùng mới';

  @override
  String get createNewUserAccount => 'Tạo tài khoản người dùng mới';

  @override
  String get createUser => 'Tạo người dùng';

  @override
  String get editUser => 'Chỉnh sửa người dùng';

  @override
  String get editUserDetails => 'Chỉnh sửa thông tin người dùng';

  @override
  String get userDetails => 'Thông tin người dùng';

  @override
  String get firstName => 'Tên';

  @override
  String get lastName => 'Họ';

  @override
  String get email => 'Email';

  @override
  String get password => 'Mật khẩu';

  @override
  String get confirmPassword => 'Xác nhận mật khẩu';

  @override
  String get phoneNumber => 'Số điện thoại';

  @override
  String get role => 'Vai trò';

  @override
  String get department => 'Phòng ban';

  @override
  String get position => 'Chức vụ';

  @override
  String get organization => 'Tổ chức';

  @override
  String get active => 'Hoạt động';

  @override
  String get inactive => 'Không hoạt động';

  @override
  String get disabled => 'Vô hiệu hóa';

  @override
  String get deleted => 'Đã xóa';

  @override
  String get enabled => 'Đã kích hoạt';

  @override
  String get roleAdmin => 'Quản trị viên';

  @override
  String get roleManager => 'Quản lý';

  @override
  String get roleHR => 'Nhân sự';

  @override
  String get roleEmployee => 'Nhân viên';

  @override
  String get roleUser => 'Người dùng';

  @override
  String get departmentIT => 'Công nghệ thông tin';

  @override
  String get departmentHR => 'Nhân sự';

  @override
  String get departmentFinance => 'Tài chính';

  @override
  String get departmentMarketing => 'Marketing';

  @override
  String get departmentOperations => 'Vận hành';

  @override
  String get departmentSales => 'Kinh doanh';

  @override
  String get departmentSupport => 'Hỗ trợ';

  @override
  String get searchUsers => 'Tìm kiếm người dùng...';

  @override
  String get filterAndSort => 'Lọc & Sắp xếp';

  @override
  String get bulkActions => 'Thao tác hàng loạt';

  @override
  String get selectAll => 'Chọn tất cả';

  @override
  String get deselectAll => 'Bỏ chọn tất cả';

  @override
  String get clearSelection => 'Xóa lựa chọn';

  @override
  String usersSelected(int count) {
    return 'Đã chọn $count người dùng';
  }

  @override
  String get allRoles => 'Tất cả vai trò';

  @override
  String get allDepartments => 'Tất cả phòng ban';

  @override
  String get sortBy => 'Sắp xếp theo';

  @override
  String get sortByName => 'Tên';

  @override
  String get sortByEmail => 'Email';

  @override
  String get sortByRole => 'Vai trò';

  @override
  String get sortByDepartment => 'Phòng ban';

  @override
  String get sortByCreatedDate => 'Ngày tạo';

  @override
  String get sortByLastLogin => 'Lần đăng nhập cuối';

  @override
  String get sortAscending => 'Tăng dần';

  @override
  String get sortDescending => 'Giảm dần';

  @override
  String get viewUser => 'Xem người dùng';

  @override
  String get editUserAction => 'Chỉnh sửa người dùng';

  @override
  String get deleteUser => 'Xóa người dùng';

  @override
  String get restoreUser => 'Khôi phục người dùng';

  @override
  String get disableUser => 'Vô hiệu hóa người dùng';

  @override
  String get enableUser => 'Kích hoạt người dùng';

  @override
  String get resetPassword => 'Đặt lại mật khẩu';

  @override
  String get toggleUserStatus => 'Chuyển đổi trạng thái';

  @override
  String get confirmDeleteUser => 'Bạn có chắc chắn muốn xóa người dùng này?';

  @override
  String get confirmRestoreUser =>
      'Bạn có chắc chắn muốn khôi phục người dùng này?';

  @override
  String get confirmDisableUser =>
      'Bạn có chắc chắn muốn vô hiệu hóa người dùng này?';

  @override
  String get confirmEnableUser =>
      'Bạn có chắc chắn muốn kích hoạt người dùng này?';

  @override
  String get confirmResetPassword =>
      'Bạn có chắc chắn muốn đặt lại mật khẩu của người dùng này?';

  @override
  String get bulkDelete => 'Xóa hàng loạt';

  @override
  String get bulkRestore => 'Khôi phục hàng loạt';

  @override
  String get bulkDisable => 'Vô hiệu hóa hàng loạt';

  @override
  String get bulkEnable => 'Kích hoạt hàng loạt';

  @override
  String confirmBulkDelete(int count) {
    return 'Bạn có chắc chắn muốn xóa $count người dùng?';
  }

  @override
  String confirmBulkRestore(int count) {
    return 'Bạn có chắc chắn muốn khôi phục $count người dùng?';
  }

  @override
  String get userOverview => 'Tổng quan người dùng';

  @override
  String get totalUsers => 'Tổng số người dùng';

  @override
  String get activeUsers => 'Người dùng hoạt động';

  @override
  String get disabledUsers => 'Người dùng bị vô hiệu hóa';

  @override
  String get deletedUsers => 'Người dùng đã xóa';

  @override
  String get roleDistribution => 'Phân bố vai trò';

  @override
  String get analytics => 'Phân tích';

  @override
  String get userGrowth => 'Tăng trưởng người dùng';

  @override
  String get userActivity => 'Hoạt động người dùng';

  @override
  String get departmentBreakdown => 'Phân tích theo phòng ban';

  @override
  String get userCreatedSuccessfully => 'Tạo người dùng thành công!';

  @override
  String get userUpdatedSuccessfully => 'Cập nhật người dùng thành công!';

  @override
  String get userDeletedSuccessfully => 'Xóa người dùng thành công!';

  @override
  String get userRestoredSuccessfully => 'Khôi phục người dùng thành công!';

  @override
  String get userDisabledSuccessfully => 'Vô hiệu hóa người dùng thành công!';

  @override
  String get userEnabledSuccessfully => 'Kích hoạt người dùng thành công!';

  @override
  String failedToCreateUser(String error) {
    return 'Không thể tạo người dùng: $error';
  }

  @override
  String failedToUpdateUser(String error) {
    return 'Không thể cập nhật người dùng: $error';
  }

  @override
  String failedToDeleteUser(String error) {
    return 'Không thể xóa người dùng: $error';
  }

  @override
  String failedToLoadUsers(String error) {
    return 'Không thể tải danh sách người dùng: $error';
  }

  @override
  String get noUsersFound => 'Không tìm thấy người dùng nào';

  @override
  String get loadingUsers => 'Đang tải danh sách người dùng...';

  @override
  String get refreshUsers => 'Làm mới danh sách';

  @override
  String get addUser => 'Thêm người dùng';

  @override
  String get firstNameRequired => 'Tên là bắt buộc';

  @override
  String get lastNameRequired => 'Họ là bắt buộc';

  @override
  String get emailRequired => 'Email là bắt buộc';

  @override
  String get emailInvalid => 'Vui lòng nhập địa chỉ email hợp lệ';

  @override
  String get passwordRequired => 'Mật khẩu là bắt buộc';

  @override
  String get passwordTooShort => 'Mật khẩu phải có ít nhất 8 ký tự';

  @override
  String get passwordTooWeak =>
      'Mật khẩu phải chứa chữ hoa, chữ thường, số và ký tự đặc biệt';

  @override
  String get phoneInvalid => 'Vui lòng nhập số điện thoại hợp lệ';

  @override
  String get roleRequired => 'Vai trò là bắt buộc';

  @override
  String get departmentRequired => 'Phòng ban là bắt buộc';

  @override
  String get requiredField => 'Trường bắt buộc';

  @override
  String get optionalField => 'Trường tùy chọn';

  @override
  String get enterFirstName => 'Nhập tên';

  @override
  String get enterLastName => 'Nhập họ';

  @override
  String get enterEmail => 'Nhập địa chỉ email';

  @override
  String get enterPassword => 'Nhập mật khẩu';

  @override
  String get enterPhoneNumber => 'Nhập số điện thoại';

  @override
  String get selectRole => 'Chọn vai trò';

  @override
  String get selectDepartment => 'Chọn phòng ban';

  @override
  String get enterPosition => 'Nhập chức vụ';

  @override
  String get enterOrganization => 'Nhập tổ chức';

  @override
  String get personalInformation => 'Thông tin cá nhân';

  @override
  String get workInformation => 'Thông tin công việc';

  @override
  String get roleAndStatus => 'Vai trò & Trạng thái';

  @override
  String get saveChanges => 'Lưu thay đổi';

  @override
  String get roleManagement => 'Quản lý vai trò';

  @override
  String get departmentManagement => 'Quản lý phòng ban';

  @override
  String get createNewRole => 'Tạo vai trò mới';

  @override
  String get editRole => 'Chỉnh sửa vai trò';

  @override
  String get createNewDepartment => 'Tạo phòng ban mới';

  @override
  String get editDepartment => 'Chỉnh sửa phòng ban';

  @override
  String get roleName => 'Tên vai trò';

  @override
  String get departmentName => 'Tên phòng ban';

  @override
  String get departmentCode => 'Mã phòng ban';

  @override
  String get departmentDescription => 'Mô tả phòng ban';

  @override
  String get parentDepartment => 'Phòng ban cha';

  @override
  String get noParentTopLevel => 'Không có phòng ban cha (Cấp cao nhất)';

  @override
  String get selectParentDepartment => 'Chọn phòng ban cha (tùy chọn)';

  @override
  String get noDepartment => 'Không có phòng ban';

  @override
  String get basicInformation => 'Thông tin cơ bản';

  @override
  String get hierarchy => 'Cấu trúc tổ chức';

  @override
  String get departmentStatus => 'Trạng thái phòng ban';

  @override
  String get enable => 'Kích hoạt';

  @override
  String get disable => 'Vô hiệu hóa';

  @override
  String get restore => 'Khôi phục';

  @override
  String get deleteRole => 'Xóa vai trò';

  @override
  String get deleteDepartment => 'Xóa phòng ban';

  @override
  String deleteRoleConfirm(Object roleName) {
    return 'Bạn có chắc chắn muốn xóa vai trò \"$roleName\"?';
  }

  @override
  String deleteDepartmentConfirm(Object departmentName) {
    return 'Bạn có chắc chắn muốn xóa phòng ban \"$departmentName\"?';
  }

  @override
  String get roleCreatedSuccessfully => 'Tạo vai trò thành công';

  @override
  String get roleUpdatedSuccessfully => 'Cập nhật vai trò thành công';

  @override
  String get roleDeletedSuccessfully => 'Xóa vai trò thành công';

  @override
  String get departmentCreatedSuccessfully => 'Tạo phòng ban thành công';

  @override
  String get departmentUpdatedSuccessfully => 'Cập nhật phòng ban thành công';

  @override
  String get departmentDeletedSuccessfully => 'Xóa phòng ban thành công';

  @override
  String get departmentRestoredSuccessfully => 'Khôi phục phòng ban thành công';

  @override
  String get departmentStatusUpdatedSuccessfully =>
      'Cập nhật trạng thái phòng ban thành công';

  @override
  String get noRolesFound => 'Không tìm thấy vai trò nào';

  @override
  String get noDepartmentsFound => 'Không tìm thấy phòng ban nào';

  @override
  String get createFirstRole => 'Tạo vai trò đầu tiên để bắt đầu';

  @override
  String get createFirstDepartment => 'Tạo phòng ban đầu tiên để bắt đầu';

  @override
  String get addRole => 'Thêm vai trò';

  @override
  String get addDepartment => 'Thêm phòng ban';

  @override
  String get updateRole => 'Cập nhật vai trò';

  @override
  String get updateDepartment => 'Cập nhật phòng ban';

  @override
  String get createRole => 'Tạo vai trò';

  @override
  String get createDepartment => 'Tạo phòng ban';

  @override
  String get searchRoles => 'Tìm kiếm vai trò...';

  @override
  String get searchDepartments => 'Tìm kiếm phòng ban...';

  @override
  String get includeDeleted => 'Bao gồm đã xóa';

  @override
  String get roleInformation => 'Thông tin vai trò';

  @override
  String get roleDetails => 'Chi tiết vai trò';

  @override
  String get created => 'Đã tạo';

  @override
  String get lastUpdated => 'Cập nhật lần cuối';

  @override
  String get roleId => 'ID vai trò';

  @override
  String get departmentId => 'ID phòng ban';

  @override
  String get enterRoleName =>
      'Nhập tên vai trò (ví dụ: Quản lý, Nhân sự, Lập trình viên)';

  @override
  String get enterDepartmentName =>
      'Nhập tên phòng ban (ví dụ: Nhân sự, Công nghệ thông tin)';

  @override
  String get enterDepartmentCode => 'Nhập mã phòng ban (ví dụ: HR, IT, FIN)';

  @override
  String get enterDepartmentDescription => 'Nhập mô tả phòng ban (tùy chọn)';

  @override
  String get roleNameRequired => 'Tên vai trò là bắt buộc';

  @override
  String get departmentNameRequired => 'Tên phòng ban là bắt buộc';

  @override
  String get roleNameMinLength => 'Tên vai trò phải có ít nhất 2 ký tự';

  @override
  String get departmentNameMinLength => 'Tên phòng ban phải có ít nhất 2 ký tự';

  @override
  String get roleNameMaxLength => 'Tên vai trò phải ít hơn 50 ký tự';

  @override
  String get departmentNameMaxLength => 'Tên phòng ban phải ít hơn 100 ký tự';

  @override
  String get departmentCodeMinLength => 'Mã phòng ban phải có ít nhất 2 ký tự';

  @override
  String get departmentCodeMaxLength => 'Mã phòng ban phải ít hơn 10 ký tự';

  @override
  String get roleNameInvalidCharacters => 'Tên vai trò chứa ký tự không hợp lệ';

  @override
  String get departmentCodeInvalidCharacters =>
      'Mã phòng ban chứa ký tự không hợp lệ';

  @override
  String get roleNameAlreadyExists => 'Tên vai trò đã tồn tại';

  @override
  String get departmentNameAlreadyExists => 'Tên phòng ban đã tồn tại';

  @override
  String get departmentCodeAlreadyExists => 'Mã phòng ban đã tồn tại';

  @override
  String get changingRoleNameWarning =>
      'Thay đổi tên vai trò có thể ảnh hưởng đến người dùng được gán vai trò này.';

  @override
  String get roleAndDepartmentInfo =>
      'Tên vai trò và mã phòng ban phải là duy nhất. Chúng sẽ được sử dụng trong toàn bộ hệ thống để kiểm soát quyền truy cập.';

  @override
  String get departmentHierarchyInfo =>
      'Tên và mã phòng ban phải là duy nhất. Bạn có thể tạo cấu trúc phân cấp bằng cách chọn phòng ban cha.';
}
