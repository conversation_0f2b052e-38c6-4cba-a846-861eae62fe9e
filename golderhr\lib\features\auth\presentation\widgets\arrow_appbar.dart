import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class ArrowAppBar extends StatelessWidget {
  const ArrowAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topLeft,
      child: Icon<PERSON>utton(
        onPressed: () {
          context.pop();
        },
        icon: const Icon(Icons.arrow_back),
        color: Theme.of(context).appBarTheme.iconTheme?.color ?? Colors.white,
        iconSize: 28,
        padding: const EdgeInsets.all(8.0),
        tooltip: 'Back',
      ),
    );
  }
}
