import '../entities/leave_request.dart';

class LeaveTypeBalance {
  final int used;
  final int remaining;
  final int total;

  const LeaveTypeBalance({
    required this.used,
    required this.remaining,
    required this.total,
  });

  double get usagePercentage => total > 0 ? (used / total) * 100 : 0;
  bool get hasRemaining => remaining > 0;
}

class LeaveBalance {
  final int remainingDays;
  final int totalDays;
  final int usedDays;
  final int pendingDays;
  final int approvedDays;
  final int rejectedDays;
  final Map<LeaveType, LeaveTypeBalance>? balanceByType;

  const LeaveBalance({
    required this.remainingDays,
    required this.totalDays,
    required this.usedDays,
    required this.pendingDays,
    required this.approvedDays,
    required this.rejectedDays,
    this.balanceByType,
  });

  double get usagePercentage =>
      totalDays > 0 ? (usedDays / totalDays) * 100 : 0;

  bool get hasRemainingDays => remainingDays > 0;

  // Get balance for specific leave type
  LeaveTypeBalance? getBalanceForType(LeaveType type) {
    return balanceByType?[type];
  }

  // Get remaining days for specific leave type
  int getRemainingDaysForType(LeaveType type) {
    return balanceByType?[type]?.remaining ?? 0;
  }
}
